<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>intact.devtools</groupId>
    <artifactId>intact-parent-pom-git</artifactId>
    <version>2.3.20</version>
  </parent>

  <name>Intact Broker Admin</name>
  <description>POM to build Broker Admin project (web, core)</description>

  <groupId>intact.web.brokeroffice</groupId>
  <artifactId>broker-admin-app</artifactId>
  <version>3.0.4-SNAPSHOT</version>
  <packaging>pom</packaging>

  <modules>
    <module>broker-admin-web</module>
    <module>broker-admin-business</module>
  </modules>

  <scm>
    <connection>scm:git:https://githubifc.iad.ca.inet/lab-se/broker-admin-app.git</connection>
    <developerConnection>scm:git:https://githubifc.iad.ca.inet/lab-se/broker-admin-app.git</developerConnection>
    <url>https://githubifc.iad.ca.inet/lab-se/broker-admin-app</url>
    <tag>HEAD</tag>
  </scm>

  <properties>
    <!-- Override parent properties -->
    <!-- Default configuration for TOOLCHAINS -->
    <jdk>17</jdk>
    <jdk.version>17</jdk.version>
    <!-- Used for toolchains. Sometimes can be different from jdk.version -->
    <java.version>${jdk.version}</java.version>

    <jdk.vendor>temurin</jdk.vendor>

    <!-- Override parent config which is set to UTF-8 -->
    <project.build.sourceEncoding>ISO-8859-1</project.build.sourceEncoding>

    <shortGroupId>web.brokeroffice</shortGroupId>
    <build_version>${project.version}</build_version>

    <tools.version>2.2.0.7</tools.version>
    <security.version>1.60.0.23</security.version>
    <plp.version>5.1.0</plp.version>
    <cif.version>4.0.9</cif.version>
    <brm.version>5.0.1</brm.version>
<!--    <capi.version>2.4.0.39</capi.version>-->
    <abr.version>2.0.0.0</abr.version>

    <selenium.version>2.53.1</selenium.version>
    <myfaces.version>4.1.1</myfaces.version>
    <primefaces.version>14.0.12</primefaces.version>
    <spring.version>6.0.23</spring.version>
    <hibernate.version>5.6.15.Final</hibernate.version>
    <jackson.version>2.18.1</jackson.version>
    <slf4j.version>2.0.17</slf4j.version>
    <log4j.version>2.24.3</log4j.version>
    <esapi.version>2.6.2.0</esapi.version>

    <!--        <sonar.coverage.exclusions>**/java/**</sonar.coverage.exclusions>-->
    <mavenJacocoPluginVersion>0.8.12</mavenJacocoPluginVersion>
  </properties>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>org.apache.httpcomponents.client5</groupId>
        <artifactId>httpclient5</artifactId>
        <version>5.4.4</version>
      </dependency>

      <!--   WEB DEPENDENCIES  -->
      <dependency>
        <groupId>jakarta.persistence</groupId>
        <artifactId>jakarta.persistence-api</artifactId>
        <version>3.1.0</version>
      </dependency>
      <dependency>
        <groupId>jakarta.servlet</groupId>
        <artifactId>jakarta.servlet-api</artifactId>
        <version>6.1.0</version>
        <scope>provided</scope>
      </dependency>
      <dependency>
        <groupId>com.sun.activation</groupId>
        <artifactId>jakarta.activation</artifactId>
        <version>2.0.1</version>
      </dependency>
      <dependency>
        <groupId>org.apache.tomee</groupId>
        <artifactId>javaee-api</artifactId>
        <version>8.0-6</version>
      </dependency>
      <dependency>
        <groupId>jakarta.xml.bind</groupId>
        <artifactId>jakarta.xml.bind-api</artifactId>
        <version>3.0.1</version>
      </dependency>

      <dependency>
        <groupId>org.glassfish.jaxb</groupId>
        <artifactId>jaxb-runtime</artifactId>
        <version>4.0.5</version>
      </dependency>

      <dependency>
        <groupId>org.apache.xmlgraphics</groupId>
        <artifactId>batik-codec</artifactId>
        <version>1.18</version>
        <exclusions>
          <exclusion>
            <groupId>xml-apis</groupId>
            <artifactId>xml-apis</artifactId>
          </exclusion>
          <exclusion>
            <groupId>xerces</groupId>
            <artifactId>xercesImpl</artifactId>
          </exclusion>
          <exclusion>
            <groupId>xalan</groupId>
            <artifactId>xalan</artifactId>
          </exclusion>
        </exclusions>
      </dependency>

      <!--Primefaces-->
      <dependency>
        <groupId>org.primefaces</groupId>
        <artifactId>primefaces</artifactId>
        <classifier>jakarta</classifier>
        <version>${primefaces.version}</version>
      </dependency>

      <dependency>
        <groupId>org.primefaces.extensions</groupId>
        <artifactId>primefaces-extensions</artifactId>
        <classifier>jakarta</classifier>
        <version>${primefaces.version}</version>
      </dependency>

      <dependency>
        <groupId>com.fasterxml.jackson.core</groupId>
        <artifactId>jackson-databind</artifactId>
        <version>${jackson.version}</version>
      </dependency>

      <dependency>
        <groupId>org.owasp.esapi</groupId>
        <artifactId>esapi</artifactId>
        <version>${esapi.version}</version>
      </dependency>

      <!--        SPRING        -->
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-framework-bom</artifactId>
        <scope>import</scope>
        <type>pom</type>
        <version>${spring.version}</version>
      </dependency>

      <!--      HIBERNATE       -->
      <dependency>
        <groupId>org.hibernate</groupId>
        <artifactId>hibernate-core-jakarta</artifactId>
        <version>${hibernate.version}</version>
      </dependency>

      <dependency>
        <groupId>com.atomikos</groupId>
        <artifactId>transactions-jta</artifactId>
        <version>6.0.0</version>
        <classifier>jakarta</classifier>
      </dependency>
      <dependency>
        <groupId>com.atomikos</groupId>
        <artifactId>transactions-jdbc</artifactId>
        <version>6.0.0</version>
        <classifier>jakarta</classifier>
      </dependency>

      <dependency>
        <groupId>org.jadira.usertype</groupId>
        <artifactId>usertype.core</artifactId>
        <version>6.0.1.GA</version>
      </dependency>

      <!--      LOGGING         -->
      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>jul-to-slf4j</artifactId>
        <version>${slf4j.version}</version>
      </dependency>

      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-api</artifactId>
        <version>${slf4j.version}</version>
      </dependency>

      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-simple</artifactId>
        <version>${slf4j.version}</version>
      </dependency>

      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>log4j-over-slf4j</artifactId>
        <version>${slf4j.version}</version>
      </dependency>

      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-api</artifactId>
        <version>${log4j.version}</version>
      </dependency>

      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-core</artifactId>
        <version>${log4j.version}</version>
      </dependency>

      <!--   JAVA DEPENDENCIES  -->
      <dependency>
        <groupId>jakarta.inject</groupId>
        <artifactId>jakarta.inject-api</artifactId>
        <version>2.0.1.MR</version>
      </dependency>

      <dependency>
        <groupId>jakarta.annotation</groupId>
        <artifactId>jakarta.annotation-api</artifactId>
        <version>1.3.5</version>
      </dependency>

      <!--  INTACT DEPENDENCIES  -->
      <dependency>
        <groupId>intact.devtools.intact-poms</groupId>
        <artifactId>intact-3rdparty-bom</artifactId>
        <type>pom</type>
        <scope>import</scope>
        <version>2.1.0.1</version>
        <exclusions>
          <exclusion>
            <groupId>commons-logging</groupId>
            <artifactId>commons-logging</artifactId>
          </exclusion>
        </exclusions>
      </dependency>

      <dependency>
        <groupId>intact.tools</groupId>
        <artifactId>tools-api-project</artifactId>
        <scope>import</scope>
        <version>${tools.version}</version>
        <type>pom</type>
      </dependency>

      <dependency>
        <groupId>intact.commons.utility</groupId>
        <artifactId>security-tools-project</artifactId>
        <scope>import</scope>
        <version>${security.version}</version>
        <type>pom</type>
        <exclusions>
          <exclusion>
            <!-- as it comes with JDK6 / WAS -->
            <groupId>xml-apis</groupId>
            <artifactId>xml-apis</artifactId>
          </exclusion>
          <exclusion>
            <groupId>xerces</groupId>
            <artifactId>xercesImpl</artifactId>
          </exclusion>
          <exclusion>
            <groupId>xalan</groupId>
            <artifactId>xalan</artifactId>
          </exclusion>
          <exclusion>
            <groupId>nl.captcha</groupId>
            <artifactId>simplecaptcha</artifactId>
          </exclusion>
          <exclusion>
            <groupId>org.jsoup</groupId>
            <artifactId>jsoup</artifactId>
          </exclusion>
        </exclusions>
      </dependency>

      <dependency>
        <groupId>intact.commons.service.cif</groupId>
        <artifactId>cif-project</artifactId>
        <scope>import</scope>
        <type>pom</type>
        <version>${cif.version}</version>
      </dependency>

      <dependency>
        <groupId>intact.commons.service.plp</groupId>
        <artifactId>plp-project</artifactId>
        <scope>import</scope>
        <type>pom</type>
        <version>${plp.version}</version>
      </dependency>

      <dependency>
        <groupId>intact.commons.service.brm</groupId>
        <artifactId>brm-project</artifactId>
        <scope>import</scope>
        <type>pom</type>
        <version>${brm.version}</version>
      </dependency>

      <dependency>
        <groupId>intact.commons.abr</groupId>
        <artifactId>business-rule-project</artifactId>
        <scope>import</scope>
        <type>pom</type>
        <version>${abr.version}</version>
      </dependency>

      <!--   COMMONS DEPENDENCIES  -->
      <dependency>
        <groupId>commons-io</groupId>
        <artifactId>commons-io</artifactId>
        <version>2.17.0</version>
      </dependency>

      <dependency>
        <groupId>commons-collections</groupId>
        <artifactId>commons-collections</artifactId>
        <version>3.2.2</version>
      </dependency>

      <dependency>
        <groupId>commons-beanutils</groupId>
        <artifactId>commons-beanutils</artifactId>
        <version>1.11.0</version>
      </dependency>

      <dependency>
        <groupId>commons-fileupload</groupId>
        <artifactId>commons-fileupload</artifactId>
        <version>1.5</version>
      </dependency>

      <dependency>
        <groupId>commons-el</groupId>
        <artifactId>commons-el</artifactId>
        <version>1.0</version>
      </dependency>

      <dependency>
        <groupId>xerces</groupId>
        <artifactId>xercesImpl</artifactId>
        <version>2.12.2</version>
      </dependency>

      <dependency>
        <groupId>com.oracle.ojdbc</groupId>
        <artifactId>ojdbc8</artifactId>
        <version>19.3.0.0</version>
      </dependency>

      <!--  TEST DEPENDENCIES  -->

      <dependency>
        <groupId>com.github.detro</groupId>
        <artifactId>phantomjsdriver</artifactId>
        <version>1.2.0</version>
        <scope>test</scope>
      </dependency>

      <dependency>
        <groupId>org.seleniumhq.selenium</groupId>
        <artifactId>selenium-java</artifactId>
        <version>${selenium.version}</version>
        <scope>test</scope>
        <exclusions>
          <exclusion>
            <artifactId>xml-apis</artifactId>
            <groupId>xml-apis</groupId>
          </exclusion>
          <exclusion>
            <groupId>xerces</groupId>
            <artifactId>xercesImpl</artifactId>
          </exclusion>
          <exclusion>
            <groupId>xalan</groupId>
            <artifactId>xalan</artifactId>
          </exclusion>
        </exclusions>
      </dependency>

      <dependency>
        <groupId>org.seleniumhq.selenium</groupId>
        <artifactId>selenium-server</artifactId>
        <version>${selenium.version}</version>
        <scope>test</scope>
      </dependency>

      <dependency>
        <groupId>org.owasp.antisamy</groupId>
        <artifactId>antisamy</artifactId>
        <version>1.7.8</version>
      </dependency>

      <dependency>
        <groupId>org.apache.commons</groupId>
        <artifactId>commons-collections4</artifactId>
        <version>4.5.0-M2</version>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <profiles>
    <profile>
      <id>local</id>
      <modules>
        <module>broker-admin-local-config</module>
      </modules>
      <dependencies>
        <!--
        When building with "local" profile, we add a special dependency to include local properties
        to the classpath. This ensures that we can deploy the project locally and to OCP without carrying
        extra config files in the WAR.
        -->
        <dependency>
          <groupId>intact.web.brokeroffice</groupId>
          <artifactId>broker-admin-local-config</artifactId>
          <version>1.0.0</version>
        </dependency>
      </dependencies>
    </profile>
  </profiles>

  <build>
    <pluginManagement>
      <plugins>
        <plugin>
          <artifactId>maven-javadoc-plugin</artifactId>
          <configuration>
            <skip>true</skip>
          </configuration>
        </plugin>
        <plugin>
          <artifactId>maven-compiler-plugin</artifactId>
          <configuration>
            <encoding>${project.build.sourceEncoding}</encoding>
          </configuration>
        </plugin>
        <plugin>
          <artifactId>maven-resources-plugin</artifactId>
          <configuration>
            <encoding>${project.build.sourceEncoding}</encoding>
          </configuration>
        </plugin>
      </plugins>
    </pluginManagement>

    <plugins>
      <!-- Jacoco Maven Plugin -->
      <plugin>
        <groupId>org.jacoco</groupId>
        <artifactId>jacoco-maven-plugin</artifactId>
        <executions>
          <execution>
            <goals>
              <goal>prepare-agent</goal>
            </goals>
          </execution>
          <execution>
            <id>report</id>
            <phase>test</phase>
            <goals>
              <goal>report</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-toolchains-plugin</artifactId>
        <version>${mavenToolChainsPluginVersion}</version>
        <executions>
          <execution>
            <goals>
              <goal>toolchain</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <toolchains>
            <jdk>
              <version>${jdk.version}</version>
              <vendor>${jdk.vendor}</vendor>
            </jdk>
          </toolchains>
        </configuration>
      </plugin>
    </plugins>
  </build>
</project>
