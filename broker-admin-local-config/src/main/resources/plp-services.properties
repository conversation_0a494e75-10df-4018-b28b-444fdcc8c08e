hibernateAdapter.showSql=false
hibernateAdapter.databasePlatform=org.hibernate.dialect.Oracle10gDialect
hibernateAdapter.generateDdl=false
hibernateAdapter.default_batch_fetch_size=1
hibernateAdapter.format_sql=false
hibernateAdapter.use_sql_comments=false
hibernateAdapter.fetchSize=1
hibernateAdapter.batchSize=1

dataSource.jndi-name=java:comp/env/jdbc/plpolicy
optimistic.group.lock.enabled=true

# 128 bit encryption key. 32 hexadecimal characters.
IngAesCipher.key=A61B6401E2F362C97B4D7A37AE6301BC

hibernateAdapter.jta_platform=org.hibernate.engine.transaction.jta.platform.internal.AtomikosJtaPlatform
plp.jta.transaction.manager=org.springframework.transaction.jta.JtaTransactionManager
