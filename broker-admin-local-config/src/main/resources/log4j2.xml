<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="DEBUG">
	<Appenders>
		<Console name="Console" target="SYSTEM_OUT">
			<PatternLayout pattern="%d{HH:mm:ss.SSS} [%t] %-5level %logger{36} - %msg%n"/>
		</Console>
	</Appenders>

	<Loggers>
		<Logger name="com.intact.brokeroffice" level="DEBUG" additivity="false">
			<AppenderRef ref="Console"/>
		</Logger>

		<Logger name="org.hibernate" level="ERROR" additivity="false">
			<AppenderRef ref="Console"/>
		</Logger>

		<Logger name="jakarta.faces" level="ERROR" additivity="false">
			<AppenderRef ref="Console"/>
		</Logger>

		<Root level="ERROR">
			<AppenderRef ref="Console"/>
		</Root>
	</Loggers>
</Configuration>
