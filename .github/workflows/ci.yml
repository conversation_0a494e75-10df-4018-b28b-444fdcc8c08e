name: PR Opened (to dev)
on:
  pull_request:
    types: [opened, reopened, synchronize, ready_for_review]
    branches-ignore:
      - 'main'
jobs:
  CI:
    uses: lab-se-actions/java-workflows/.github/workflows/ci.yaml@v2
    with:
      product: ${{ vars.PRODUCT_NAME }}
      app_name: ${{ vars.APP_NAME }}
      timeout: 30 # Optional. Use if your job need more time than the default value
    secrets: inherit
