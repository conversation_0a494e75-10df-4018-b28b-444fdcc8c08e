name: PR Opened (to main)
on:
  pull_request:
    types: [opened, reopened, synchronize, ready_for_review]
    branches:
      - 'main'
jobs:
  CI-Release:
    name: CI - PR Opened (release branch)
    uses: lab-se-actions/java-workflows/.github/workflows/ci-release.yaml@v2
    if: startsWith(github.head_ref, 'release/')
    with:
      product: ${{ vars.PRODUCT_NAME }}
      app_name: ${{ vars.APP_NAME }}
      timeout: 30 # Optional. Use if your job need more time than the default value
    secrets: inherit
  CI-Hotfix:
    name: CI - PR Opened (hotfix branch)
    uses: lab-se-actions/java-workflows/.github/workflows/ci-hotfix.yaml@v2
    if: startsWith(github.head_ref, 'hotfix/')
    with:
      product: ${{ vars.PRODUCT_NAME }}
      app_name: ${{ vars.APP_NAME }}
      timeout: 30 # Optional. Use if your job need more time than the default value
    secrets: inherit
