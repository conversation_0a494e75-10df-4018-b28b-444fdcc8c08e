<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>intact.web.brokeroffice</groupId>
    <artifactId>broker-admin-app</artifactId>
    <version>3.0.4-SNAPSHOT</version>
  </parent>

  <artifactId>broker-admin-web</artifactId>

  <packaging>war</packaging>

  <properties>
    <m2eclipse.wtp.contextRoot>BrokerAdm</m2eclipse.wtp.contextRoot>
  </properties>

  <dependencies>

    <dependency>
      <groupId>intact.web.brokeroffice</groupId>
      <artifactId>broker-admin-business</artifactId>
      <version>${project.version}</version>
      <exclusions>
        <exclusion>
          <artifactId>commons-collections</artifactId>
          <groupId>commons-collections</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>jakarta.inject</groupId>
      <artifactId>jakarta.inject-api</artifactId>
    </dependency>

    <!--   WEB DEPENDENCIES  -->
    <dependency>
      <groupId>joda-time</groupId>
      <artifactId>joda-time</artifactId>
      <version>2.13.0</version>
    </dependency>

    <dependency>
      <groupId>jakarta.servlet</groupId>
      <artifactId>jakarta.servlet-api</artifactId>
      <version>6.1.0</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>jakarta.el</groupId>
      <artifactId>jakarta.el-api</artifactId>
      <version>4.0.0</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>jakarta.enterprise</groupId>
      <artifactId>jakarta.enterprise.cdi-api</artifactId>
      <version>4.0.1</version>
    </dependency>
    <dependency>
      <groupId>org.apache.tomee</groupId>
      <artifactId>javaee-api</artifactId>
    </dependency>

    <dependency>
      <groupId>org.apache.myfaces.core</groupId>
      <artifactId>myfaces-api</artifactId>
      <version>${myfaces.version}</version>
    </dependency>
    <dependency>
      <groupId>org.apache.myfaces.core</groupId>
      <artifactId>myfaces-impl</artifactId>
      <version>${myfaces.version}</version>
      <exclusions>
        <exclusion>
          <groupId>org.apache.myfaces.core</groupId>
          <artifactId>myfaces-api</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <!--Primefaces-->
    <dependency>
      <groupId>org.primefaces</groupId>
      <artifactId>primefaces</artifactId>
      <classifier>jakarta</classifier>
      <version>${primefaces.version}</version>
    </dependency>

    <dependency>
      <groupId>org.primefaces.extensions</groupId>
      <artifactId>primefaces-extensions</artifactId>
      <classifier>jakarta</classifier>
      <version>${primefaces.version}</version>
    </dependency>

    <dependency>
      <groupId>org.apache.xmlgraphics</groupId>
      <artifactId>batik-codec</artifactId>
    </dependency>

    <dependency>
      <groupId>com.atomikos</groupId>
      <artifactId>transactions-jta</artifactId>
      <classifier>jakarta</classifier>
    </dependency>
    <dependency>
      <groupId>com.atomikos</groupId>
      <artifactId>transactions-jdbc</artifactId>
      <classifier>jakarta</classifier>
    </dependency>

    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-databind</artifactId>
    </dependency>

    <dependency>
      <groupId>org.owasp.esapi</groupId>
      <artifactId>esapi</artifactId>
      <exclusions>
        <exclusion>
          <artifactId>xercesImpl</artifactId>
          <groupId>xerces</groupId>
        </exclusion>
        <exclusion>
          <artifactId>commons-collections</artifactId>
          <groupId>commons-collections</groupId>
        </exclusion>
      </exclusions>
    </dependency>

    <!--      LOGGING         -->
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>jul-to-slf4j</artifactId>
    </dependency>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-api</artifactId>
    </dependency>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-simple</artifactId>
    </dependency>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>log4j-over-slf4j</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.logging.log4j</groupId>
      <artifactId>log4j-api</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.logging.log4j</groupId>
      <artifactId>log4j-core</artifactId>
    </dependency>

    <!--  INTACT DEPENDENCIES  -->

    <dependency>
      <groupId>intact.commons.service.plp</groupId>
      <artifactId>plp-services</artifactId>
    </dependency>

    <dependency>
      <groupId>intact.tools</groupId>
      <artifactId>tools-api-ldap</artifactId>
    </dependency>

    <dependency>
      <groupId>intact.tools</groupId>
      <artifactId>tools-string-api</artifactId>
    </dependency>

    <dependency>
      <groupId>intact.tools</groupId>
      <artifactId>tools-sanitization-api</artifactId>
    </dependency>

    <dependency>
      <groupId>intact.tools</groupId>
      <artifactId>tools-comparison-intact</artifactId>
      <scope>runtime</scope>
    </dependency>

    <dependency>
      <groupId>intact.tools</groupId>
      <artifactId>tools-sanitization-intact</artifactId>
      <scope>runtime</scope>
    </dependency>

    <dependency>
      <groupId>intact.tools</groupId>
      <artifactId>tools-string-apache</artifactId>
      <scope>runtime</scope>
    </dependency>

    <!--   COMMONS DEPENDENCIES  -->
    <dependency>
      <groupId>commons-fileupload</groupId>
      <artifactId>commons-fileupload</artifactId>
    </dependency>

    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-lang3</artifactId>
      <version>3.17.0</version>
    </dependency>

    <dependency>
      <groupId>commons-validator</groupId>
      <artifactId>commons-validator</artifactId>
    </dependency>

    <dependency>
      <groupId>commons-io</groupId>
      <artifactId>commons-io</artifactId>
    </dependency>

    <dependency>
      <groupId>commons-el</groupId>
      <artifactId>commons-el</artifactId>
    </dependency>

    <dependency>
      <groupId>oro</groupId>
      <artifactId>oro</artifactId>
      <scope>runtime</scope>
    </dependency>

    <dependency>
      <groupId>jakarta-oro</groupId>
      <artifactId>jakarta-oro</artifactId>
    </dependency>

    <dependency>
      <groupId>jakarta.transaction</groupId>
      <artifactId>jakarta.transaction-api</artifactId>
      <version>2.0.1</version>
    </dependency>

    <dependency>
      <groupId>com.github.detro</groupId>
      <artifactId>phantomjsdriver</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>org.seleniumhq.selenium</groupId>
      <artifactId>selenium-java</artifactId>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>commons-collections</artifactId>
          <groupId>commons-collections</groupId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>org.seleniumhq.selenium</groupId>
      <artifactId>selenium-server</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-test</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-core</artifactId>
      <version>5.15.2</version>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <profiles>
    <profile>
      <id>local</id>
      <build>
        <plugins>
          <plugin>
            <!-- When building the war locally, we must copy tomcat's config to the appropriate folder -->
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-resources-plugin</artifactId>
            <executions>
              <execution>
                <id>copy-resources</id>
                <phase>validate</phase>
                <goals>
                  <goal>copy-resources</goal>
                </goals>
                <configuration>
                  <resources>
                    <resource>
                      <directory>
                        ${basedir}/../broker-admin-local-config/src/main/tomcat
                      </directory>
                    </resource>
                  </resources>
                  <outputDirectory>
                    ${project.build.directory}/${project.build.finalName}/META-INF
                  </outputDirectory>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>

  <build>
    <finalName>${project.artifactId}</finalName>
    <!-- Plugin definitions for the build process -->
    <plugins>
      <plugin>
        <artifactId>maven-war-plugin</artifactId>
        <configuration>
          <warName>BrokerAdm</warName>
          <failOnMissingWebXml>false</failOnMissingWebXml>
          <archive>
            <manifest>
              <addClasspath>true</addClasspath>
              <addDefaultImplementationEntries>true</addDefaultImplementationEntries>
              <classpathPrefix>lib/</classpathPrefix>
            </manifest>
            <manifestEntries>
              <Build-Time>${maven.build.timestamp}</Build-Time>
            </manifestEntries>
          </archive>
        </configuration>
      </plugin>
    </plugins>
  </build>

</project>
