package com.intact.brokeroffice.controller.accounts;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.test.util.ReflectionTestUtils;

import com.ing.canada.cif.domain.enums.ApplicationIdEnum;
import com.ing.canada.cif.domain.enums.CifCompanyEnum;
import com.ing.canada.cif.domain.enums.LineOfBusinessEnum;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.intact.brokeroffice.business.accounts.IAccountsBusinessProcess;
import com.intact.brokeroffice.business.accounts.impl.AccountsBusinessProcess;
import com.intact.brokeroffice.business.exception.BrokerServiceException;
import com.intact.brokeroffice.controller.authentification.AuthentificationController;
import com.intact.brokeroffice.controller.province.ProvinceController;
import com.intact.brokeroffice.controller.tabs.AccountsTabController;
import com.intact.canada.brm.domain.broker.BrokerWebOfficeAccess;
import com.intact.canada.brm.domain.profile.AccessProfile;
import com.intact.canada.brm.domain.profile.AccessProfileEnum;
import com.intact.canada.brm.domain.user.UserAccount;

import jakarta.faces.context.FacesContext;

public class AccountsControllerTest {

	private AccountsController accountsController = new AccountsController();

	private AccountsTabController accountsTabController;

	private IAccountsBusinessProcess accountsBusinessProcess;

	private AccountsAdapter accountsAdapter = new AccountsAdapter();

	private AuthentificationController authentificationController;

	private ProvinceController provinceController;

	private Map<String, String> masterBrokerMap = new HashMap<String, String>();

	@BeforeEach
	public void setUp() throws Exception {

		accountsTabController = mock(AccountsTabController.class);
		accountsBusinessProcess = mock(AccountsBusinessProcess.class);
		//accountsAdapter = mock(AccountsAdapter.class);
		authentificationController = mock(AuthentificationController.class);
		provinceController = mock(ProvinceController.class);

		ReflectionTestUtils.setField(accountsController, "accountsTabController", accountsTabController);
		ReflectionTestUtils.setField(accountsController, "accountsBusinessProcess", accountsBusinessProcess);
		ReflectionTestUtils.setField(accountsController, "authentificationController", authentificationController);
		ReflectionTestUtils.setField(accountsController, "provinceController", provinceController);
		ReflectionTestUtils.setField(accountsController, "masterBrokerMap", masterBrokerMap);
		ReflectionTestUtils.setField(accountsController, "accountsAdapter", accountsAdapter);

	}

	@AfterEach
	public void tearDown() throws Exception {
		accountsTabController = null;
		accountsBusinessProcess = null;
		accountsAdapter = null;
		authentificationController = null;
		provinceController = null;

		accountsController.setFullUserNameFilter(null);
		accountsController.setUserNameFilter(null);
	}

	//TODO: Need to fix this test
	@Disabled
	@Test
	public void testGetAccountBeans() throws BrokerServiceException {
		List<UserAccount> listAccount = new ArrayList<UserAccount>();
		listAccount.add(newUserAccount());

		accountsController.listPageWithModificationNotification();// make "pageAccessed" = true

		masterBrokerMap.put("MasterOwnerCode", "MasterOwnerValue");

		when(authentificationController.getCurrentAccountUId()).thenReturn("CurrentAccountUId");
		when(provinceController.getProvinceCode()).thenReturn(ProvinceCodeEnum.QUEBEC);
		when(accountsBusinessProcess.getUsers(eq("CurrentAccountUId"), eq(ProvinceCodeEnum.QUEBEC),anyString(), anyString())).thenReturn(listAccount);
		when(accountsBusinessProcess.getAssignedMasterBrokers((CifCompanyEnum) any(), anyString(), anyList(), anyList())).thenReturn(
				masterBrokerMap);

		accountsController.setFullUserNameFilter("a");
		accountsController.setUserNameFilter("abc");
		List<AccountBean> resultList = accountsController.getAccountBeans();
		AccountBean resultBean = resultList.get(0);
		assertEquals(1, resultList.size());
		assertEquals("<a href='pages/brokers/account/usersByMaster.jsf?selectedOwnerNo=" + "MasterOwnerCode"
				+ "' target='_blank'>" + "MasterOwnerValue" + " (" + "MasterOwnerCode" + ")" + "</a>",
				resultBean.getMasterBrokers());
	}

	private UserAccount newUserAccount() {
		BrokerWebOfficeAccess brokerWebOfficeAccess = new BrokerWebOfficeAccess();
		brokerWebOfficeAccess.setMasterOwnerCode("MasterOwnerCode");
		UserAccount user = new UserAccount();
		user.setUid("UserUId");
		user.setName("UserAccountName");
		user.addBrokerWebOfficeAccess(brokerWebOfficeAccess);
		return user;
	}

	@Test
	public void testModifyPage() throws BrokerServiceException{
		accountsController.setSelectedUser("UserID");
		UserAccount userAccount = new UserAccount();
		userAccount.setUid("UserName");
		userAccount.setName("UserFullName");
		BrokerWebOfficeAccess brokerWebOfficeAccess = new BrokerWebOfficeAccess();
		brokerWebOfficeAccess.setMasterOwnerCode("MasterBrokerNumber");
		userAccount.addBrokerWebOfficeAccess(brokerWebOfficeAccess);
		when(accountsBusinessProcess.findByUId("UserID")).thenReturn(userAccount);

		Map<String, String> masterBrokerMap = new LinkedHashMap<String, String>();
		masterBrokerMap.put("MasterBrokerNumber", "some content");

		ReflectionTestUtils.setField(accountsController, "masterBrokerMap", masterBrokerMap);

		accountsController.modifyPage();

		AccountBean accountBean = accountsController.getAccountBean();

		assertEquals(1, accountBean.getMasterBrokerBeans().size());
		MasterBrokerBean masterBokeBean = accountBean.getMasterBrokerBeans().get(0);
		assertTrue(masterBokeBean.getSelected());
		assertEquals("UserName", accountBean.getUserName());
		assertEquals("UserFullName", accountBean.getUserFullName());
	}

	@Test
	public void testListPage(){
		accountsController.setPageAccessed(false);
		accountsController.setUserNameFilter("UserFilterTest");
		accountsController.setScrollerPage(1000);
		accountsController.setScrollerPage(10);

		accountsController.listPage();

		assertTrue(accountsController.isPageAccessed());
		assertNotNull(accountsController.getUserNameFilter());
		assertEquals(1, accountsController.getScrollerPage().intValue());
		assertEquals(10, accountsController.getScrollerPerPage().intValue());
	}

	@Test
	public void testFilterUserNameTrue(){
		accountsController.setUserNameFilter("Olivier");
		AccountBean accountBean = new AccountBean();
		accountBean.setUserName("Olivier");
		assertTrue(accountsController.filterUserName(accountBean));
	}

	@Test
	public void testFilterUserNameFalse(){
		accountsController.setUserNameFilter("Olivier");
		AccountBean accountBean = new AccountBean();
		accountBean.setUserName("David");
		assertFalse(accountsController.filterUserName(accountBean));
	}

	@Test
	public void testFilterUserFullNameTrue(){
		accountsController.setFullUserNameFilter("Olivier");
		AccountBean accountBean = new AccountBean();
		accountBean.setUserFullName("Olivier");
		assertTrue(accountsController.filterFullUserName(accountBean));
	}

	@Test
	public void testFilterUserFullNameFalse(){
		accountsController.setFullUserNameFilter("Olivier");
		AccountBean accountBean = new AccountBean();
		accountBean.setUserFullName("David");
		assertFalse(accountsController.filterFullUserName(accountBean));
	}


	/**
	 * Test for
	 * {@link AccountsController#modify()}
	 * Where the page calling the method is the modify page (modification should be executed)
	 * @throws Exception
	 */
	//TODO: Need to fix this test
	@Disabled
	@Test
	public void testModify_modifyPage() throws Exception{
		// General test setup
		String testUser = "test";
		accountsController.setSelectedUser(testUser);
		accountsController.setPageAccessed(false);
		accountsAdapter = mock(AccountsAdapter.class);
		ReflectionTestUtils.setField(accountsController, "accountsAdapter", accountsAdapter);

		// Change the page in the account tabs controller to the Modify Page
		when(this.accountsTabController.getPage()).thenReturn(AccountsTabController.Page.MODIFY_ACCOUNT);

		// Execute tested method and validate results
		accountsController.modify();

		verify(this.accountsBusinessProcess, times(1)).findByUId(testUser);
		verify(this.accountsAdapter, times(1)).loadModel(any(UserAccount.class), any(AccountBean.class), any(CifCompanyEnum.class),
				anyMap());
		verify(this.accountsBusinessProcess, times(1)).update(any(UserAccount.class));

		assertTrue(accountsController.isPageAccessed(), "PageAccessed attribute should've been set to true");
	}
	/**
	 * Test for
	 * {@link AccountsController#modify()}
	 * Where the page calling the method is not the modify page (modification should not be executed)
	 * @throws Exception
	 */
	@Test
	public void testModify_differentPage() throws Exception{
		// General test setup
		String testUser = "test";
		accountsController.setSelectedUser(testUser);
		accountsController.setPageAccessed(false);
		accountsAdapter = mock(AccountsAdapter.class);
		ReflectionTestUtils.setField(accountsController, "accountsAdapter", accountsAdapter);

		// Change the page in the account tabs controller to something other than the Modify Page
		when(this.accountsTabController.getPage()).thenReturn(AccountsTabController.Page.LIST_ACCOUNT);

		// Execute tested method and validate results
		accountsController.modify();

		verify(this.accountsBusinessProcess, never()).findByUId(testUser);
		verify(this.accountsAdapter, never()).loadModel(any(UserAccount.class), any(AccountBean.class), any(CifCompanyEnum.class),
				anyMap());
		verify(this.accountsBusinessProcess, never()).update(any(UserAccount.class));

		assertTrue(accountsController.isPageAccessed(), "PageAccessed attribute should've been set to true");
	}
}





















