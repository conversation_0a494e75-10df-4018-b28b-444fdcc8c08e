package com.intact.brokeroffice.controller.fsa;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import jakarta.faces.component.UIViewRoot;
import jakarta.faces.context.ExternalContext;
import jakarta.faces.context.FacesContext;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.test.util.ReflectionTestUtils;

import com.ing.canada.cif.domain.IFsaLoad;
import com.ing.canada.cif.domain.IFsaMessage;
import com.ing.canada.cif.domain.impl.FsaLoad;
import com.ing.canada.cif.domain.impl.FsaMessage;
import com.ing.canada.cif.domain.impl.FsaParameters;
import com.ing.canada.cif.service.IFsaLoadService;

public class FsaUploadReportControllerTest {

	private MockedStatic<FacesContext> mockedFacesContext;

	private FsaUploadReportController fsaUploadReportController = new FsaUploadReportController();

	private FsaAdapter fsaAdapter;

	private IFsaLoadService fsaLoadService;

	private String selectedLoadId;

	private String uploadFileStatus;

	private FsaLoadBean fsaLoadBean = new FsaLoadBean();

	private FacesContext context = mock(FacesContext.class);
	private ExternalContext externalContext = mock(ExternalContext.class);
	private UIViewRoot viewRoot = mock(UIViewRoot.class);

	private Map<String, String> requestParameters = new HashMap<String, String>();
	private Map<String, Object> attributes = new HashMap<String, Object>();

	@BeforeEach
	public void setUp() throws Exception {
		mockedFacesContext = Mockito.mockStatic(FacesContext.class);
		fsaAdapter = mock(FsaAdapter.class);
		fsaLoadService = mock(IFsaLoadService.class);

		mockedFacesContext.when(FacesContext::getCurrentInstance).thenReturn(context);
		when(context.getExternalContext()).thenReturn(externalContext);
		when(context.getViewRoot()).thenReturn(viewRoot);
		when(viewRoot.getAttributes()).thenReturn(attributes);

		ReflectionTestUtils.setField(fsaUploadReportController, "fsaAdapter", fsaAdapter);
		ReflectionTestUtils.setField(fsaUploadReportController, "fsaLoadService", fsaLoadService);
		ReflectionTestUtils.setField(fsaUploadReportController, "selectedLoadId", selectedLoadId);
		ReflectionTestUtils.setField(fsaUploadReportController, "uploadFileStatus", uploadFileStatus);
		ReflectionTestUtils.setField(fsaUploadReportController, "fsaLoadBean", fsaLoadBean);
	}

	@AfterEach
	public void tearDown() throws Exception {
		mockedFacesContext.closeOnDemand();
	}

	@Test
	public void testgetUploadFileStatusUploadSUCC() {
		requestParameters.put("uploadFileStatus", "SUCC");
		when(externalContext.getRequestParameterMap()).thenReturn(requestParameters);

		List<IFsaMessage> listMessage = new ArrayList<IFsaMessage>();
		IFsaMessage message = new FsaMessage();
		message.setMessage("test message content");
		listMessage.add(message);
		when(fsaLoadService.getFsaMessagesList(any(FsaParameters.class))).thenReturn(listMessage);

		fsaUploadReportController.setSelectedLoadId("1");

		// Execute
		fsaUploadReportController.getUploadFileStatus();

		assertTrue(attributes.containsKey("uploadFileStatus"));
		assertEquals("SUCC", attributes.get("uploadFileStatus"));
	}

	@Test
	public void testgetUploadFileStatusUploadKILL() {
		Date date = new Date();
		attributes.put("uploadFileStatus", "KILL");
		IFsaLoad fsaLoad = new FsaLoad();
		fsaLoad.setLoadId(1l);
		fsaLoad.setFileName("testFileName");
		fsaLoad.setStartDate(date);
		when(fsaLoadService.findById(anyLong())).thenReturn(fsaLoad);

		fsaUploadReportController.setSelectedLoadId("1");
		// Execute
		fsaUploadReportController.getUploadFileStatus();

		assertTrue(attributes.containsKey("uploadFileStatus"));
		assertEquals("KILL", attributes.get("uploadFileStatus"));
		assertEquals(1, fsaLoad.getLoadId().intValue());
		assertEquals("testFileName", fsaLoad.getFileName());
		assertEquals(date, fsaLoad.getStartDate());
	}

}

















