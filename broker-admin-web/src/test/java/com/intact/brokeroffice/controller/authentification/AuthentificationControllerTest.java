package com.intact.brokeroffice.controller.authentification;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

import java.util.ArrayList;
import java.util.List;

import jakarta.faces.context.ExternalContext;
import jakarta.faces.context.FacesContext;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.mock.web.MockHttpSession;

import com.ing.canada.common.web.api.auth.TamTokensEnum;
import com.intact.brokeroffice.controller.enums.SessionConstantsEnum;
import com.intact.brokeroffice.helper.ResourceBundleHelper;

class AuthentificationControllerTest {

	private MockedStatic<ResourceBundleHelper> mockedResourceBundleHelper;

	private MockedStatic<FacesContext> mockedFacesContext;

	private final AuthentificationController authentificationController = new AuthentificationController();

	@Mock
	private FacesContext context;

	@Mock
	private ExternalContext externalContext;

	private MockHttpSession session;

	private AutoCloseable closeable;

	@BeforeEach
	public void setUp() throws Exception {
		closeable = MockitoAnnotations.openMocks(this);

		mockedResourceBundleHelper = Mockito.mockStatic(ResourceBundleHelper.class);
		mockedFacesContext = Mockito.mockStatic(FacesContext.class);
		session = new MockHttpSession();
		mockedFacesContext.when(FacesContext::getCurrentInstance).thenReturn(context);
		when(context.getExternalContext()).thenReturn(externalContext);
		when(externalContext.getSession(anyBoolean())).thenReturn(session);
		mockedResourceBundleHelper.when(() -> ResourceBundleHelper.getMessage(anyString(), any(), anyString())).thenReturn("notAllowedSubbroker");

		session.setAttribute(TamTokensEnum.TAM_USER_NAME_CREDENTIAL.getTokenValue(), "olivier");
		session.setAttribute(SessionConstantsEnum.AVAILABLE_MASTERS.getSessionConstant(), initListMaster());
		session.setAttribute(TamTokensEnum.TAM_SECURITY_ROLES_ATTRIB.getTokenValue(), "limited");
	}

	@AfterEach
	public void tearDown() throws Exception {
		closeable.close();
		mockedFacesContext.closeOnDemand();
		mockedResourceBundleHelper.closeOnDemand();
	}

	@Test
	public void testLogout() {
		assertFalse(session.isInvalid());
		String pageResult = authentificationController.logout();
		assertTrue(session.isInvalid());
		assertEquals("logout", pageResult);
	}

	@Test
	public void testGetCurrentAccountUId() {
		String expectedAccountUId = "olivier";
		assertEquals(expectedAccountUId, authentificationController.getCurrentAccountUId());
	}

	@Test
	public void testGetAvailableMasters() {
		String expectedMaster1 = "master1";
		String expectedMaster2 = "master2";

		List<String> resultMasters = authentificationController.getAvailableMasters();

		assertEquals(expectedMaster1, resultMasters.get(0));
		assertEquals(expectedMaster2, resultMasters.get(1));
	}

	@Test
	public void testGetAccessLevel() {
		String expectedAccessLevel = "limited";

		assertEquals(expectedAccessLevel, authentificationController.getCurrentAccessLevel());
	}

	@Test
	public void testGetBlockedSubbrokers() {
		String expectedBlockedSubbroker = "notAllowedSubbroker";

		List<String> blockedSubbrokerList = authentificationController.getBlockedSubbrokers();

		assertEquals(expectedBlockedSubbroker.toUpperCase(), blockedSubbrokerList.get(0));
	}

	private List<String> initListMaster() {
		List<String> availableMaster = new ArrayList<String>();
		availableMaster.add("master1");
		availableMaster.add("master2");
		return availableMaster;
	}
}















