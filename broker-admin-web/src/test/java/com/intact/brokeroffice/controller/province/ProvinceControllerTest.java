package com.intact.brokeroffice.controller.province;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import jakarta.servlet.http.Cookie;

import jakarta.faces.context.ExternalContext;
import jakarta.faces.context.FacesContext;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpSession;
import org.springframework.test.util.ReflectionTestUtils;

import com.ing.canada.cif.domain.enums.CifCompanyEnum;
import com.ing.canada.common.web.api.auth.TamTokensEnum;
import com.ing.canada.plp.domain.enums.ManufacturerCompanyCodeEnum;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.intact.brokeroffice.controller.authentification.AuthentificationController;
import com.intact.brokeroffice.controller.enums.SessionConstantsEnum;

public class ProvinceControllerTest {

	private MockedStatic<FacesContext> mockedFacesContext;

	private ProvinceController provinceController = new ProvinceController();

	private FacesContext context;

	private MockHttpServletRequest request;

	private MockHttpSession session;

	private ExternalContext externalContext;

	private AuthentificationController authController;


	@BeforeEach
	public void setUp() throws Exception {
		mockedFacesContext = Mockito.mockStatic(FacesContext.class);
		externalContext = mock(ExternalContext.class);
		context = mock(FacesContext.class);
		request = new MockHttpServletRequest();
		session = new MockHttpSession();
		request.setSession(session);

		authController = mock(AuthentificationController.class);

		ReflectionTestUtils.setField(provinceController, "extension", "");

		mockedFacesContext.when(FacesContext::getCurrentInstance).thenReturn(context);
		when(context.getExternalContext()).thenReturn(externalContext);
		when(externalContext.getSession(anyBoolean())).thenReturn(session);
		when(externalContext.getRequest()).thenReturn(request);
		when(authController.isMasterRole()).thenReturn(false);


	}

	@AfterEach
	public void tearDown() throws Exception {
		mockedFacesContext.closeOnDemand();
	}

	@Test
	public void testGetProvince() {
		session.setAttribute(SessionConstantsEnum.COMPANY.getSessionConstant(), "A");

		String province = provinceController.getProvince();

		assertEquals("QC", province);
		assertEquals("A", session.getAttribute(SessionConstantsEnum.COMPANY.getSessionConstant()));
	}

	@Test
	public void testGetProvinceDefault() {
		session.setAttribute(SessionConstantsEnum.PROVINCE.getSessionConstant(), null);
		Cookie cookie = new Cookie("brokerDefaultProvince", "A");
		request.setCookies(cookie);

		String province = provinceController.getProvince();

		assertEquals("QC", province);
	}

	@Test
	public void testIsMethod() {
		ReflectionTestUtils.setField(provinceController, "company", "A");
		assertTrue(provinceController.isCompanyA());

		ReflectionTestUtils.setField(provinceController, "company", "3");
		assertTrue(provinceController.isCompany3());

		ReflectionTestUtils.setField(provinceController, "company", "6");
		assertTrue(provinceController.isCompany6());
	}

	@Test
	public void testNumberDaysLeftForQuebec() {
		ReflectionTestUtils.setField(provinceController, "company", "A");
		assertEquals(31, provinceController.getNumberOfDaysLeft().intValue());
	}

	@Test
	public void testNumberDaysLeftForOntario() {
		ReflectionTestUtils.setField(provinceController, "company", "6");
		assertEquals(61, provinceController.getNumberOfDaysLeft().intValue());
	}

	@Test
	public void testNumberDaysLeftForAlberta() {
		ReflectionTestUtils.setField(provinceController, "company", "3");
		assertEquals(31, provinceController.getNumberOfDaysLeft().intValue());
	}

	@Test
	public void testGetCifCompanyEnumQC() {
		ReflectionTestUtils.setField(provinceController, "company", "A");
		assertEquals(CifCompanyEnum.INTACT_QC, provinceController.getCompanyEnumCode());
	}

	@Test
	public void testGetCifCompanyEnumAB() {
		ReflectionTestUtils.setField(provinceController, "company", "3");
		assertEquals(CifCompanyEnum.INTACT_AB, provinceController.getCompanyEnumCode());
	}

	@Test
	public void testGetCifCompanyEnumON() {
		ReflectionTestUtils.setField(provinceController, "company", "6");
		assertEquals(CifCompanyEnum.INTACT_HALIFAX, provinceController.getCompanyEnumCode());
	}

	@Test
	public void testGetCifCompanyEnumOTHER() {
		ReflectionTestUtils.setField(provinceController, "company", "XX");
		assertEquals(CifCompanyEnum.INTACT_QC, provinceController.getCompanyEnumCode());
	}

	@Test
	public void testManufacturerCompanyCodeQC() {
		ReflectionTestUtils.setField(provinceController, "company", "A");
		assertEquals(ManufacturerCompanyCodeEnum.INTACT_QUEBEC_REGION, provinceController.getManufacturerCompanyCode());
	}

	@Test
	public void testManufacturerCompanyCodeAB() {
		ReflectionTestUtils.setField(provinceController, "company", "3");
		assertEquals(ManufacturerCompanyCodeEnum.ING_WESTERN_REGION, provinceController.getManufacturerCompanyCode());
	}

	@Test
	public void testManufacturerCompanyCodeON() {
		ReflectionTestUtils.setField(provinceController, "company", "6");
		assertEquals(ManufacturerCompanyCodeEnum.ING_CENTRAL_ATLANTIC_REGION,
				provinceController.getManufacturerCompanyCode());
	}

	@Test
	public void testManufacturerCompanyCodeOTHER() {
		ReflectionTestUtils.setField(provinceController, "company", "XX");
		assertEquals(ManufacturerCompanyCodeEnum.INTACT_QUEBEC_REGION, provinceController.getManufacturerCompanyCode());
	}

	@Test
	public void testGetProvinceCodeQC() {
		ReflectionTestUtils.setField(provinceController, "company", "A");
		assertEquals(ProvinceCodeEnum.QUEBEC, provinceController.getProvinceCode());
	}

	@Test
	public void testGetProvinceCodeON() {
		ReflectionTestUtils.setField(provinceController, "company", "6");
		assertEquals(ProvinceCodeEnum.ONTARIO, provinceController.getProvinceCode());
	}

	@Test
	public void testGetProvinceCodeAB() {
		ReflectionTestUtils.setField(provinceController, "company", "3");
		assertEquals(ProvinceCodeEnum.ALBERTA, provinceController.getProvinceCode());
	}

	@Test
	public void testShowUploadQC(){
		session.setAttribute(TamTokensEnum.TAM_USER_CLASSIC_USER_ID.getTokenValue(), "ClassicIdTest");
		session.setAttribute(TamTokensEnum.TAM_USER_NAME_CREDENTIAL.getTokenValue(), "halcionIdTest");

		assertTrue(provinceController.getShowUpload());
	}

	@Test
	public void testShowUploadON(){
		ReflectionTestUtils.setField(provinceController, "company", "6");
		session.setAttribute(TamTokensEnum.TAM_USER_CLASSIC_USER_ID.getTokenValue(), "ClassicIdTest");
		session.setAttribute(TamTokensEnum.TAM_USER_NAME_CREDENTIAL.getTokenValue(), "halcionIdTest");

		assertTrue(provinceController.getShowUpload());
	}

	@Test
	public void testShowUploadAB(){
		ReflectionTestUtils.setField(provinceController, "company", "3");
		session.setAttribute(TamTokensEnum.TAM_USER_CLASSIC_USER_ID.getTokenValue(), "ClassicIdTest");
		session.setAttribute(TamTokensEnum.TAM_USER_NAME_CREDENTIAL.getTokenValue(), "halcionIdTest");

		assertTrue(provinceController.getShowUpload());
	}

	@Test
	public void testShowUploadOTHER(){
		ReflectionTestUtils.setField(provinceController, "company", "XX");
		session.setAttribute(TamTokensEnum.TAM_USER_CLASSIC_USER_ID.getTokenValue(), "ClassicIdTest");
		session.setAttribute(TamTokensEnum.TAM_USER_NAME_CREDENTIAL.getTokenValue(), "halcionIdTest");

		assertFalse(provinceController.getShowUpload());
	}
}



















