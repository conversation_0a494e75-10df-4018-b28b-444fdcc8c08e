package com.intact.brokeroffice.controller.owner.viewusers;

import com.intact.brokeroffice.business.accounts.IAccountsBusinessProcess;
import com.intact.brokeroffice.business.exception.BrokerServiceException;
import com.intact.brokeroffice.controller.accounts.AccountsAdapter;
import jakarta.faces.component.UIViewRoot;
import jakarta.faces.context.ExternalContext;
import jakarta.faces.context.FacesContext;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.LinkedHashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class ViewUsersControllerTest {

	private MockedStatic<FacesContext> mockedFacesContext;

	private ViewUsersController viewUserController = new ViewUsersController();

	@Mock
	private IAccountsBusinessProcess accountsBusinessProcess;

	@Mock
	private AccountsAdapter accountsAdapter;

	@Mock
	private FacesContext context;

	@Mock
	private ExternalContext externalContext;

	@Mock
	private UIViewRoot viewRoot;

	private Map<String, Object> attributes = new LinkedHashMap<String, Object>();

	private Map<String, String> requestMapParameters = new LinkedHashMap<String, String>();


	@BeforeEach
	public void setUp() throws Exception {
		// Initialize mocks
		MockitoAnnotations.openMocks(this);

		// Mock the static FacesContext
		mockedFacesContext = Mockito.mockStatic(FacesContext.class);
		context = Mockito.mock(FacesContext.class);
		externalContext = Mockito.mock(ExternalContext.class);
		viewRoot = Mockito.mock(UIViewRoot.class);

		Mockito.when(FacesContext.getCurrentInstance()).thenReturn(context);
		Mockito.when(context.getExternalContext()).thenReturn(externalContext);
		Mockito.when(context.getViewRoot()).thenReturn(viewRoot);
		Mockito.when(viewRoot.getAttributes()).thenReturn(attributes);

		ReflectionTestUtils.setField(viewUserController, "accountsBusinessProcess", accountsBusinessProcess);
		ReflectionTestUtils.setField(viewUserController, "accountsAdapter", accountsAdapter);
	}

	@AfterEach
	public void tearDown() throws Exception {
		if (mockedFacesContext != null) {
			mockedFacesContext.close();
		}
	}

	@Test
	public void testGetSelectedOwnerNo() throws BrokerServiceException {
		requestMapParameters.put("selectedOwnerNo", "*********");
		Mockito.when(externalContext.getRequestParameterMap()).thenReturn(requestMapParameters);
		Mockito.when(accountsBusinessProcess.findMasterOwnerName(Mockito.anyString())).thenReturn("Olivier");

		String resultNumber = viewUserController.getSelectedOwnerNo();

		assertEquals("*********", resultNumber);
		assertTrue(attributes.containsKey("selectedOwnerNo"));
		assertEquals("Olivier", viewUserController.getMaster().getName());
		assertEquals("*********", viewUserController.getMaster().getNumber());
	}

}
























