package com.intact.brokeroffice.controller.tabs;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.test.util.ReflectionTestUtils;

import com.intact.brokeroffice.controller.province.ProvinceController;

public class SubBrokersTabControllerTest {
	
	private SubBrokersTabController subBrokersTabController = new SubBrokersTabController();
	
	private BrokersTabController brokersTabController;

	private ProvinceController provinceController;

	@BeforeEach
	public void setUp() throws Exception {
		brokersTabController = mock(BrokersTabController.class);
		provinceController = mock(ProvinceController.class);
		
		ReflectionTestUtils.setField(subBrokersTabController, "brokersTabController", brokersTabController);
		ReflectionTestUtils.setField(subBrokersTabController, "provinceController", provinceController);
	}

	@AfterEach
	public void tearDown() throws Exception {
		brokersTabController = null;
		provinceController = null;
	}

	@Test
	public void testGetPagePathON(){
		when(provinceController.getExtension()).thenReturn("ON");
		subBrokersTabController.list();
		assertEquals("/pages/brokers/subbroker/listON.xhtml", subBrokersTabController.getPagePath());
		
		subBrokersTabController.modify();
		assertEquals("/pages/brokers/subbroker/modify.xhtml", subBrokersTabController.getPagePath());
	}
	
	@Test
	public void testGetPagePathOTHER(){
		when(provinceController.getExtension()).thenReturn("QC");
		subBrokersTabController.list();
		assertEquals("/pages/brokers/subbroker/list.xhtml", subBrokersTabController.getPagePath());
		
		subBrokersTabController.modify();
		assertEquals("/pages/brokers/subbroker/modify.xhtml", subBrokersTabController.getPagePath());
	}

}
