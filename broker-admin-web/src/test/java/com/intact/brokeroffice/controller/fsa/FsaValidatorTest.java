package com.intact.brokeroffice.controller.fsa;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;

import com.ing.canada.cif.domain.enums.CifCompanyEnum;
import com.ing.canada.cif.domain.enums.FsaLoadStatusCodeEnum;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.intact.brokeroffice.business.dto.BusinessContextEnum;
import com.intact.brokeroffice.helper.FaceMessageHelper;

import org.primefaces.model.file.UploadedFile;

public class FsaValidatorTest {

	private MockedStatic<FaceMessageHelper> mockedFaceMessageHelper;

	private FsaValidator fsaValidator;

	@BeforeEach
	public void setUp() {
		mockedFaceMessageHelper = mockStatic(FaceMessageHelper.class);
		this.fsaValidator = new FsaValidator();
		// We make certain that the showErrorMessage attribute is set to true (for better coverage)
		this.fsaValidator.setShowErrorMessage(true);
	}

	@AfterEach
	public void tearDown(){
		this.fsaValidator = null;
		mockedFaceMessageHelper.closeOnDemand();
	}

	@Test
	public void test_validate_fileNameFullPath_PERSONAL_VALID() {
		// Prepare
		UploadedFile file = mock(UploadedFile.class);
		FsaUploadBean bean = new FsaUploadBean();
		bean.setFile(file);
		bean.setCompany(CifCompanyEnum.INTACT_QC.getSubBrokerCompanyNumber());
		bean.setBusinessContext(BusinessContextEnum.PERSONAL_LINES_AUTO_RES.getCode());

		when(file.getFileName()).thenReturn("C:\\Users\\<USER>\\Downloads\\pl_fsa_file_to_upload.dat");

		// Execute
		// Verify
		assertTrue(this.fsaValidator.validate(bean), "ValidateFileExtension function should've returned true since there are no error in the uploaded file");
	}

	@Test
	public void test_validate_fileNameFullPath_PERSONAL_INVALID() {
		// Prepare
		UploadedFile file = mock(UploadedFile.class);
		FsaUploadBean bean = new FsaUploadBean();
		bean.setFile(file);
		bean.setCompany(CifCompanyEnum.INTACT_QC.getSubBrokerCompanyNumber());
		bean.setBusinessContext(BusinessContextEnum.PERSONAL_LINES_AUTO_RES.getCode());

		when(file.getFileName()).thenReturn("C:\\Users\\<USER>\\Downloads\\CL_fsa_file_to_upload.dat");

		// Execute
		// Verify
		assertFalse(this.fsaValidator.validate(bean), "ValidateFileExtension function should've returned false since the uploaded file starts with CL instead of pl");
	}

	@Test
	public void test_validate_fileNameFullPath_COMMERCIAL_VALID() {
		// Prepare
		UploadedFile file = mock(UploadedFile.class);
		FsaUploadBean bean = new FsaUploadBean();
		bean.setFile(file);
		bean.setCompany(CifCompanyEnum.INTACT_QC.getSubBrokerCompanyNumber());
		bean.setBusinessContext(BusinessContextEnum.COMMERCIAL_LINES_AUTO.getCode());

		when(file.getFileName()).thenReturn("~opt/users/unix/folder/and/repository/unknown/uploads/cl_fsa_file_to_upload.dat");

		// Execute
		// Verify
		assertTrue(this.fsaValidator.validate(bean), "ValidateFileExtension function should've returned true since there are no error in the uploaded file");
	}

	@Test
	public void test_validate_fileNameFullPath_COMMERCIAL_INVALID() {
		// Prepare
		UploadedFile file = mock(UploadedFile.class);
		FsaUploadBean bean = new FsaUploadBean();
		bean.setFile(file);
		bean.setCompany(CifCompanyEnum.INTACT_QC.getSubBrokerCompanyNumber());
		bean.setBusinessContext(BusinessContextEnum.COMMERCIAL_LINES_AUTO.getCode());

		when(file.getFileName()).thenReturn("~opt/users/unix/folder/and/repository/unknown/uploads/ppl_fsa_file_to_upload.dat");

		// Execute
		// Verify
		assertFalse(this.fsaValidator.validate(bean), "ValidateFileExtension function should've returned false since the uploaded file starts with ppl instead of cl");
	}

	@Test
	public void testValidateFileExtension() {
		UploadedFile file = mock(UploadedFile.class);
		FsaUploadBean bean = new FsaUploadBean();
		bean.setFile(file);
		bean.setCompany(CifCompanyEnum.INTACT_QC.getSubBrokerCompanyNumber());
		bean.setBusinessContext(BusinessContextEnum.PERSONAL_LINES_AUTO_RES.getCode());


		//.TXT FILE IS NOT SUPPORTED
		when(file.getFileName()).thenReturn("PL_QC20120101.txt");
		assertFalse(this.fsaValidator.validate(bean), "ValidateFileExtension function should've returned false - .txt extension not supported");

		//.XLS FILE IS NOT SUPPORTED
		when(file.getFileName()).thenReturn("PL_QC20120101.xls");
		assertFalse(this.fsaValidator.validate(bean), "ValidateFileExtension function should've returned false - .xls extension not supported");

		//.DOCX FILE IS NOT SUPPORTED
		when(file.getFileName()).thenReturn("PL_QC20120101.docs");
		assertFalse(this.fsaValidator.validate(bean), "ValidateFileExtension function should've returned false - .docx extension not supported");

		//.CSV FILE IS NOT SUPPORTE
		when(file.getFileName()).thenReturn("PL_QC20120101.csv");
		assertFalse(this.fsaValidator.validate(bean), "ValidateFileExtension function should've returned false - .csv extension not supported");;

		// ONLY .DAT FILE IS SUPPORTED
		when(file.getFileName()).thenReturn("PL_QC20120101.dat");
		assertTrue(this.fsaValidator.validate(bean), "ValidateFileExtension function should've returned no error- .dat extension supported");

		// EMPTY FILE IS NOT SUPPORTED
		when(file.getFileName()).thenReturn(null);
		assertFalse(this.fsaValidator.validate(bean), "ValidateFileExtension function should've returned false - empty file");
	}

	/**
	 * Tests for the validate method, with and without a file.
	 * {@link FsaValidator#validate(UploadedFile, String)}
	 * @throws IOException
	 */
	@Test
	public void testValidate() throws IOException{
		UploadedFile file = mock(UploadedFile.class);

		FsaUploadBean bean = new FsaUploadBean();
		bean.setFile(file);
		bean.setCompany(CifCompanyEnum.INTACT_QC.getSubBrokerCompanyNumber());
		bean.setBusinessContext(BusinessContextEnum.PERSONAL_LINES_AUTO_RES.getCode());
		when(file.getFileName()).thenReturn("PL_fileName.dat"); // valid extension

		assertTrue(fsaValidator.validate(bean));
	}

	@Test
	public void testValidateNullFileEmptyProvince(){
		// We call the validate method with a null file and an empty province code
		FsaUploadBean bean = new FsaUploadBean();
		bean.setCompany("");
		bean.setBusinessContext(BusinessContextEnum.PERSONAL_LINES_AUTO_RES.getCode());

		boolean valid = fsaValidator.validate(bean);

		assertFalse(valid, "Validate function should've returned false - null file and empty province");
	}

	@Test
	public void testValidateNoLineOfBusiness(){
		UploadedFile file = mock(UploadedFile.class);

		FsaUploadBean bean = new FsaUploadBean();
		bean.setFile(file);
		bean.setCompany(CifCompanyEnum.INTACT_QC.getSubBrokerCompanyNumber());
		bean.setBusinessContext(null);
		when(file.getFileName()).thenReturn("fileName.dat"); // valid extension

		boolean valid = fsaValidator.validate(bean);

		assertFalse(valid, "Validate function should've returned false - testValidateNoLineOfBusiness");
	}

	@Test
	public void testValidateInvalidLineOfBusiness(){
		assertThrows(IllegalArgumentException.class, () -> {
			UploadedFile file = mock(UploadedFile.class);

			FsaUploadBean bean = new FsaUploadBean();
			bean.setFile(file);
			bean.setCompany(CifCompanyEnum.INTACT_QC.getSubBrokerCompanyNumber());
			bean.setBusinessContext("test");
			when(file.getFileName()).thenReturn("fileName.dat"); // valid extension

			fsaValidator.validate(bean);
		});
	}

	/**
	 * Tests for the validateSameFileNamesStatus method, with and without a valid status.
	 * {@link FsaValidator#validate(UploadedFile, String)}
	 */
	@Test
	public void testValidateSameFileNameStatus(){
		List<FsaLoadBean> someBeans = new ArrayList<FsaLoadBean>();
		someBeans.add(newFsaLoadBean(FsaLoadStatusCodeEnum.SUCCESSFULL));
		someBeans.add(newFsaLoadBean(FsaLoadStatusCodeEnum.FAILED));

		assertTrue(fsaValidator.validateSameFileNameStatus(someBeans), "ValidateSameFileNameStatus function should've returned true");
	}

	@Test
	public void testValidateSameFileNameStatusProcessingStatus(){
		List<FsaLoadBean> someBeans = new ArrayList<FsaLoadBean>();
		someBeans.add(newFsaLoadBean(FsaLoadStatusCodeEnum.PROCESSING)); // Should cause it to be invalid

		assertFalse(fsaValidator.validateSameFileNameStatus(someBeans), "ValidateSameFileNamesStatus function should've returned false - processing status present");
	}

	/**
	 * Function to create a new FsaLoadBean with a specified status.
	 * @param status
	 * @return new FsaLoadBean
	 */
	private FsaLoadBean newFsaLoadBean(FsaLoadStatusCodeEnum status){
		FsaLoadBean bean = new FsaLoadBean();
		bean.setStatus(status);
		return bean;
	}

	/**
	 * Utility method need to run this test case in Maven 1.
	 *
	 * @return Test suite
	 */
//	public static junit.framework.Test suite() {
//		return new JUnit4TestAdapter(FsaValidatorTest.class);
//	}
}
