package com.intact.brokeroffice.controller.brokerchanges;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.test.util.ReflectionTestUtils;

import com.ing.canada.cif.domain.enums.CifCompanyEnum;
import com.ing.canada.plp.domain.enums.AssignmentReasonCodeEnum;
import com.ing.canada.plp.domain.enums.ManufacturerCompanyCodeEnum;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.ing.canada.plp.report.SubBrokerAssignmentInfo;
import com.ing.canada.plp.service.ISubBrokerAssignmentInfoService;
import com.intact.brokeroffice.controller.province.ProvinceController;
import com.intact.brokeroffice.controller.tabs.GeneralTabController;

public class BrokerChangesControllerTest {
	
	private ProvinceController mockProvinceController;
	private GeneralTabController mockGeneralTabController;
	private BrokerChangeValidator mockBrokerChangeValidator;
	private ISubBrokerAssignmentInfoService mockSubBrokerAssignmentInfoService;
	
	private List<SubBrokerAssignmentInfo> listSubBrokerAssignmentInfos;
	private Date Date = new Date();
	
	private BrokerChangesController brokerChangesController;

	private SubBrokerAssignmentInfo first_info;
	private SubBrokerAssignmentInfo second_info;
	
	@BeforeEach
	public void setUp() throws Exception {
		brokerChangesController = new BrokerChangesController();
		
		mockProvinceController = mock(ProvinceController.class);
		mockGeneralTabController = mock(GeneralTabController.class);
		mockBrokerChangeValidator = mock(BrokerChangeValidator.class);
		mockSubBrokerAssignmentInfoService = mock(ISubBrokerAssignmentInfoService.class);
		
		fillListSubBrokerAssignmentInfosList();
		
		when(mockGeneralTabController.isReports()).thenReturn(true);
		when(mockProvinceController.getCompanyEnumCode()).thenReturn(CifCompanyEnum.BELAIR);
		when(mockProvinceController.getProvinceCode()).thenReturn(ProvinceCodeEnum.QUEBEC);
		when(mockProvinceController.getManufacturerCompanyCode()).thenReturn(ManufacturerCompanyCodeEnum.BELAIRDIRECT);
		when(mockSubBrokerAssignmentInfoService.getAllSubBrokerAssignmentsSP(
				  (Date) any()
				, (Date) any()
				, (ProvinceCodeEnum) any()
				, anyString()
				, (ManufacturerCompanyCodeEnum) any())).thenReturn(listSubBrokerAssignmentInfos);
		
		ReflectionTestUtils.setField(brokerChangesController, "generalTabController", mockGeneralTabController);
		ReflectionTestUtils.setField(brokerChangesController, "provinceController", mockProvinceController);
		ReflectionTestUtils.setField(brokerChangesController, "subBrokerAssignmentInfoService", mockSubBrokerAssignmentInfoService);
		ReflectionTestUtils.setField(brokerChangesController, "brokerChangeValidator", mockBrokerChangeValidator);
	}
	
	@AfterEach
	public void tearDown() throws Exception {
		brokerChangesController = null;
		listSubBrokerAssignmentInfos = null;
		mockProvinceController = null;
		mockGeneralTabController = null;
		mockBrokerChangeValidator = null;
		mockSubBrokerAssignmentInfoService = null;
	}

	private void fillListSubBrokerAssignmentInfosList() {
		listSubBrokerAssignmentInfos = new ArrayList<SubBrokerAssignmentInfo>();
		first_info = newSubBrokerAssignmentInfo(
				  "Jacques Tremblay"
				, "Old broker name"
				, "New broker name"
				, "Old broker city"
				, "New broker city"
				, AssignmentReasonCodeEnum.OTHER
				, "1");
		listSubBrokerAssignmentInfos.add(first_info);	
		
		second_info = newSubBrokerAssignmentInfo(
				  "Jacques Tremblay"
				, "Old broker name"
				, "New broker name"
				, "Old broker city"
				, "New broker city"
				, AssignmentReasonCodeEnum.OTHER
				, "1");
		listSubBrokerAssignmentInfos.add(second_info);
	}
	
	private SubBrokerAssignmentInfo newSubBrokerAssignmentInfo(String clientFullName, String oldBrokerName
			, String newBrokerName, String oldBrokerCity, String newBrokerCity, AssignmentReasonCodeEnum assignmentReasonCodeEnum
			, String assignorUserId){
		SubBrokerAssignmentInfo info = new SubBrokerAssignmentInfo();
		info.setEffectiveDate(Date);
		info.setClientFullName(clientFullName);
		info.setOldBrokerName(oldBrokerName);
		info.setNewBrokerName(newBrokerName);
		info.setOldBrokerCity(oldBrokerCity);
		info.setNewBrokerCity(newBrokerCity);
		info.setAssignmentReason(assignmentReasonCodeEnum); //OTHER
		info.setAssignorUserId(assignorUserId);
		return info;
	}

	@Test
	public void testGetBrokerChangeBeans() {
		List<BrokerChangeBean> resultList = brokerChangesController.getBrokerChangeBeans();
		
		BrokerChangeBean first_result_bean = resultList.get(0);
		BrokerChangeBean second_result_bean = resultList.get(1);
		
		assertEquals(2, resultList.size());
		assertEquals(first_info.getAssignmentReason().getCode(), first_result_bean.getReasonForChange());
		assertEquals(first_info.getClientFullName(), first_result_bean.getClientFullName());
		
		assertEquals(second_info.getAssignmentReason().getCode(), second_result_bean.getReasonForChange());
		assertEquals(second_info.getClientFullName(), second_result_bean.getClientFullName());
	}
	
	@Test
	public void testFindChanges(){
		when(mockBrokerChangeValidator.validate((Date) any(), (Date) any())).thenReturn(true);
		
		brokerChangesController.getBrokerChangeBeans();//There to make the brokerChangeBeans list not empty when entering 
													   //findChanges()
		
		brokerChangesController.findChanges();	
		List<BrokerChangeBean> resultList = brokerChangesController.getBrokerChangeBeans();
		
		BrokerChangeBean first_result_bean = resultList.get(0);
		BrokerChangeBean second_result_bean = resultList.get(1);
		
		assertEquals(2, resultList.size());
		assertEquals(first_info.getAssignmentReason().getCode(), first_result_bean.getReasonForChange());
		assertEquals(first_info.getClientFullName(), first_result_bean.getClientFullName());
		
		assertEquals(second_info.getAssignmentReason().getCode(), second_result_bean.getReasonForChange());
		assertEquals(second_info.getClientFullName(), second_result_bean.getClientFullName());
	}
}



















