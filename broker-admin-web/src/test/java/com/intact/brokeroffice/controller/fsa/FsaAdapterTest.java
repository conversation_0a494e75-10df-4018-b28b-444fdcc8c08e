package com.intact.brokeroffice.controller.fsa;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.ing.canada.cif.domain.IFsaLoad;
import com.ing.canada.cif.domain.IFsaMessage;
import com.ing.canada.cif.domain.enums.FsaLoadStatusCodeEnum;
import com.ing.canada.cif.domain.impl.FsaLoad;
import com.ing.canada.cif.domain.impl.FsaMessage;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.intact.brokeroffice.business.dto.BusinessContextEnum;
import com.intact.brokeroffice.controller.province.SubscriptionCompanyEnum;

public class FsaAdapterTest {
	
	private FsaAdapter fsaAdapter = new FsaAdapter();

	@BeforeEach
	public void setUp() throws Exception {
	}

	@AfterEach
	public void tearDown() throws Exception {
	}

	@Test
	public void testLoadFsaTableModel() {
		Date date = new Date();
		
		IFsaLoad fsaLoad = new FsaLoad();
		FsaLoadBean fsaLoadBean = new FsaLoadBean();
		fsaLoadBean.setLoadId(1l);
		fsaLoadBean.setFileName("TestFileName");
		fsaLoadBean.setProvince(ProvinceCodeEnum.QUEBEC);
		fsaLoadBean.setBusinessContext(BusinessContextEnum.PERSONAL_LINES_AUTO_RES);
		fsaLoadBean.setCompany(SubscriptionCompanyEnum.GC);
		fsaLoadBean.setStartDate(date);
	    fsaLoadBean.setStatus(FsaLoadStatusCodeEnum.SUCCESSFULL);
	    
	    fsaAdapter.loadFsaTableModel(fsaLoad, fsaLoadBean);
	    
	    assertTrue(1l == fsaLoad.getLoadId());
	    assertEquals("TestFileName", fsaLoad.getFileName());
	    assertEquals(ProvinceCodeEnum.QUEBEC.getCode(), fsaLoad.getProvinceCd());
	    assertEquals(date, fsaLoad.getStartDate());
	    assertEquals(FsaLoadStatusCodeEnum.SUCCESSFULL.getCode(), fsaLoad.getStatusCd());
	}
	
	@Test
	public void testLoadFsaTableBean(){
		Date date = new Date();
		
		List<IFsaLoad> someFsaLoads = new ArrayList<IFsaLoad>();
		List<FsaLoadBean> someBeans = new ArrayList<FsaLoadBean>();
		
		IFsaLoad fsaLoad = new FsaLoad();
		fsaLoad.setLoadId(1l);
		fsaLoad.setFileName("TestFileName");
		fsaLoad.setProvinceCd(ProvinceCodeEnum.QUEBEC.getCode());
		fsaLoad.setStartDate(date);
		fsaLoad.setStatusCd(FsaLoadStatusCodeEnum.SUCCESSFULL.getCode());
		someFsaLoads.add(fsaLoad);
	    
		someBeans = fsaAdapter.loadFsaTableBeans(someFsaLoads);
		
		FsaLoadBean resultBean = someBeans.get(0);
		
	    assertTrue(1l == resultBean.getLoadId());
	    assertEquals("TestFileName", resultBean.getFileName());
	    assertEquals(ProvinceCodeEnum.QUEBEC, resultBean.getProvince());
	    assertEquals(date, resultBean.getStartDate());
	    assertEquals(FsaLoadStatusCodeEnum.SUCCESSFULL, resultBean.getStatus());
	}
	
	@Test
	public void testLoadFsaMessageModel(){
		IFsaMessage anFsaMessage = new FsaMessage();
		FsaMessageBean anFsaMessageBean = new FsaMessageBean();
		anFsaMessageBean.setMessageSequence(1l);
		anFsaMessageBean.setMessage("Message content test");
		anFsaMessageBean.setProvinceCd(ProvinceCodeEnum.QUEBEC.getCode());
		
		fsaAdapter.loadFsaMessageModel(anFsaMessage, anFsaMessageBean);
		
		assertTrue(1l == anFsaMessage.getMessageSequence());
		assertEquals(ProvinceCodeEnum.QUEBEC.getCode(), anFsaMessage.getProvinceCd());
		assertEquals("Message content test", anFsaMessage.getMessage());
	}
	
	@Test
	public void testLoadFsaMessageBean(){
		FsaMessageBean anFsaMessageBean = new FsaMessageBean();
		IFsaMessage anFsaMessage = new FsaMessage();
		anFsaMessage.setMessageSequence(1l);
		anFsaMessage.setMessage("Message content test");
		anFsaMessage.setProvinceCd(ProvinceCodeEnum.QUEBEC.getCode());
		
		fsaAdapter.loadFsaMessageBean(anFsaMessage, anFsaMessageBean);
		
		assertTrue(1l == anFsaMessageBean.getMessageSequence());
		assertEquals(ProvinceCodeEnum.QUEBEC.getCode(), anFsaMessageBean.getProvinceCd());
		assertEquals("Message content test", anFsaMessageBean.getMessage());
	}
	
	@Test
	public void testLoadSuccesMessageBean() throws ParseException{
		
		IFsaMessage message = new FsaMessage();
		message.setMessage("1 ; 0");
		FsaMessageBean messageBean = new FsaMessageBean();
		
		fsaAdapter.loadSuccesMessageBean(message, messageBean);
		
		assertEquals("00:00:00", messageBean.getProceedTime());
		assertEquals("1", messageBean.getFsaProceedNbr());
	}
	
	@Test
	public void testLoadFsaMessageBeans(){
		List<FsaMessageBean> someBeans = new ArrayList<FsaMessageBean>();
		List<IFsaMessage> listMessage = new ArrayList<IFsaMessage>();
		IFsaMessage message = new FsaMessage();
		message.setMessage("some message content");
		listMessage.add(message);
		
		fsaAdapter.loadFsaMessageBeans(listMessage, someBeans);
		
		assertEquals(1, someBeans.size());
		assertEquals("some message content", someBeans.get(0).getMessage());
	}
}





















