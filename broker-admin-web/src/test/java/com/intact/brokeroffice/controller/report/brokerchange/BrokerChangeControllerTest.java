/**
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance 
 * Copyright (c) 2009, Intact Insurance, All rights reserved.<br>
 */
package com.intact.brokeroffice.controller.report.brokerchange;

import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.intact.brokeroffice.controller.brokerchanges.BrokerChangesController;

/**
 * <AUTHOR>
 *
 */
public class BrokerChangeControllerTest {
	
	private BrokerChangesController  brokerChangesController;
	
	@BeforeEach
	public void setup(){		
		this.brokerChangesController = new BrokerChangesController();				
	}
	
	@AfterEach
	public void tearDown() throws Exception {
		this.brokerChangesController = null;		
	}
	
	@Test
	public void testPeriodDates()
	{		
		//The period To date is removed  
		//Period To date will be set to today's date
		this.brokerChangesController.setDateFrom(new Date());
		this.brokerChangesController.setDateTo(null);
		this.brokerChangesController.manageRemovedDate();
		Assertions.assertNotNull(this.brokerChangesController.getDateTo());

		Assertions.assertTrue(Math.abs(this.brokerChangesController.getDateTo().getTime() - new Date().getTime()) < 1000);

		//The period From date is removed 
		//It will be defaulted to period To date - 30 days		
		this.brokerChangesController.setDateFrom(null);
		this.brokerChangesController.setDateTo(new Date());
		this.brokerChangesController.manageRemovedDate();
		GregorianCalendar fromCalendar = new GregorianCalendar();
		fromCalendar.setTime(this.brokerChangesController.getDateTo());
		fromCalendar.add(Calendar.DATE, -30);	
		Assertions.assertNotNull(this.brokerChangesController.getDateFrom());
    Assertions.assertEquals(0, this.brokerChangesController.getDateFrom().compareTo(fromCalendar.getTime()));
		
		//The period From and To are removed  		
		this.brokerChangesController.setDateFrom(null);
		this.brokerChangesController.setDateTo(null);
		this.brokerChangesController.manageRemovedDate();
		Assertions.assertNotNull(this.brokerChangesController.getDateTo());
		Assertions.assertNotNull(this.brokerChangesController.getDateFrom());
		
	}
	
	/**
	 * Utility method need to run this test case in Maven 1.
	 * 
	 * @return Test suite
	 */
//	public static junit.framework.Test suite() {
//		return new JUnit4TestAdapter(BrokerChangeControllerTest.class);
//	}
	
}
