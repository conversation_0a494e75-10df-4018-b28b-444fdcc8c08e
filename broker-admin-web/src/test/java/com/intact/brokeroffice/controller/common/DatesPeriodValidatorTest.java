package com.intact.brokeroffice.controller.common;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.text.ParseException;
import java.util.Date;

import org.joda.time.LocalDate;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class DatesPeriodValidatorTest {
	
	private DatesPeriodValidator dateValidator  = new DatesPeriodValidator();

	@BeforeEach
	public void setUp() throws Exception {
		dateValidator.setShowErrorMessage(false);
	}

	@AfterEach
	public void tearDown() throws Exception {
	}

	@Test
	public void testValidateNull() {	
		assertFalse(dateValidator.validate(null, null, null));
	}
	
	/**
	 * from after today
	 * @throws ParseException
	 */
	@Test
	public void testValidateFromAfterToday() throws ParseException {	
		LocalDate from = new LocalDate();
		Date dateFrom = from.plusDays(7).toDate();
		
		LocalDate to = new LocalDate();
		Date dateTo = to.toDate();
		
		assertFalse(dateValidator.validate(dateFrom, dateTo, null));
	}
	
	/**
	 * to after today
	 * @throws ParseException
	 */
	@Test
	public void testValidateToAfterToday() throws ParseException {	
		LocalDate from = new LocalDate();
		Date dateFrom = from.toDate();
		
		LocalDate to = new LocalDate();
		Date dateTo = to.plusDays(7).toDate();
		
		assertFalse(dateValidator.validate(dateFrom, dateTo, null));
	}
	
	/**
	 * from after todate
	 * @throws ParseException
	 */
	@Test
	public void testValidateFromAfterToDate() throws ParseException {	
		LocalDate from = new LocalDate();
		Date dateFrom = from.minusDays(7).toDate();
		
		LocalDate to = new LocalDate();
		Date dateTo = to.minusDays(14).toDate();
		
		assertFalse(dateValidator.validate(dateFrom, dateTo, null));
	}
	
	
	@Test
	public void testValidateValide() throws ParseException {	
		LocalDate from = new LocalDate();
		Date dateFrom = from.toDate();
		
		LocalDate to = new LocalDate();
		Date dateTo = to.toDate();
		
		assertTrue(dateValidator.validate(dateFrom, dateTo, null));
	}
	
	/**
	 * from null
	 * @throws ParseException
	 */
	@Test
	public void testValidateFromNull() throws ParseException {	
		Date dateFrom = null;
		
		LocalDate to = new LocalDate();
		Date dateTo = to.minusDays(14).toDate();
		
		assertFalse(dateValidator.validate(dateFrom, dateTo, null));
	}

	/**
	 * to null
	 * @throws ParseException
	 */
	@Test
	public void testValidateToNull() throws ParseException {	
		LocalDate from = new LocalDate();
		Date dateFrom = from.toDate();
		
		Date dateTo = null;
		
		assertFalse(dateValidator.validate(dateFrom, dateTo, null));
	}


}
