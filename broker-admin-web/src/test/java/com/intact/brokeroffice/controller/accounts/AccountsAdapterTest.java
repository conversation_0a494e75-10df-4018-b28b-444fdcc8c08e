package com.intact.brokeroffice.controller.accounts;

import com.ing.canada.cif.domain.enums.CifCompanyEnum;
import com.intact.canada.brm.domain.broker.BrokerWebOfficeAccess;
import com.intact.canada.brm.domain.profile.AccessProfile;
import com.intact.canada.brm.domain.profile.AccessProfileEnum;
import com.intact.canada.brm.domain.profile.UserAccessProfile;
import com.intact.canada.brm.domain.user.UserAccount;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;

public class AccountsAdapterTest {

	private final AccountsAdapter accountAdapter = new AccountsAdapter();

	private final String MASTER_BROKER_NUMBER = "12345";

	private final Map<AccessProfileEnum, AccessProfile> apMap = new HashMap<AccessProfileEnum, AccessProfile>(){};

	private final Map<AccessProfileEnum, UserAccessProfile> uapMap = new HashMap<AccessProfileEnum, UserAccessProfile>(){};


	private UserAccount userAccount;

	@BeforeEach
	public void setUp() {
		// setup default hibernate objects

		// Personal Lines access profile (activated by default)
		AccessProfile ap1 = new AccessProfile();
		ap1.setId(1L);
		ReflectionTestUtils.setField(ap1, "name", AccessProfileEnum.PL);

		apMap.put(AccessProfileEnum.PL, ap1);

		UserAccessProfile uap1 = new UserAccessProfile();
		uap1.setId(1L);
		uap1.setAccessProfile(ap1);
		uap1.setEffectiveDate(new Date());

		uapMap.put(AccessProfileEnum.PL, uap1);

		// Commercial IRCA access profile (de-activated by default)
		AccessProfile ap2 = new AccessProfile();
		ap2.setId(2L);
		ReflectionTestUtils.setField(ap2, "name", AccessProfileEnum.IRCA);

		apMap.put(AccessProfileEnum.IRCA, ap2);

		UserAccessProfile uap2 = new UserAccessProfile();
		uap2.setId(2L);
		uap2.setAccessProfile(ap2);
		uap2.setEffectiveDate(new Date());
		uap2.setExpiryDate(new Date());

		uapMap.put(AccessProfileEnum.IRCA, uap2);

		userAccount = new UserAccount();
		BrokerWebOfficeAccess brokerWebOfficeAccess = new BrokerWebOfficeAccess();
		userAccount.addBrokerWebOfficeAccess(brokerWebOfficeAccess);
		userAccount.addUserAccessProfile(uap1);
		userAccount.addUserAccessProfile(uap2);

	}

	@Test
	public void testLoadModelPLOnlySelected() throws Exception {

		AccountBean accountBean = new AccountBean();
		ArrayList<MasterBrokerBean> masterBrokerList = new ArrayList<MasterBrokerBean>();
		MasterBrokerBean masterBrokerBean = new MasterBrokerBean();
		masterBrokerBean.setSelected(true);
		masterBrokerBean.setNumber(MASTER_BROKER_NUMBER);
		masterBrokerList.add(masterBrokerBean);
		accountBean.setMasterBrokerBeans(masterBrokerList);

		AccessProfileBean apBean1 = new AccessProfileBean();
		apBean1.setProfile(AccessProfileEnum.PL);
		apBean1.setSelected(true);
		AccessProfileBean apBean2 = new AccessProfileBean();
		apBean2.setProfile(AccessProfileEnum.IRCA);
		apBean2.setSelected(false);
		List<AccessProfileBean> apBeanList = new ArrayList<AccessProfileBean>();
		apBeanList.add(apBean1);
		apBeanList.add(apBean2);
		accountBean.setAccessProfileBeans(apBeanList);

		accountAdapter.loadModel(userAccount, accountBean, CifCompanyEnum.INTACT_QC, apMap);

		UserAccessProfile uapPL = fetchUserAccessProfile(AccessProfileEnum.PL);
		UserAccessProfile uapIRCA = fetchUserAccessProfile(AccessProfileEnum.IRCA);

		assertNull(uapPL.getExpiryDate());
		assertNotNull(uapIRCA.getExpiryDate());
	}

	@Test
	public void testLoadModelIRCAOnlySelected() throws Exception {

		AccountBean accountBean = new AccountBean();
		ArrayList<MasterBrokerBean> masterBrokerList = new ArrayList<MasterBrokerBean>();
		MasterBrokerBean masterBrokerBean = new MasterBrokerBean();
		masterBrokerBean.setSelected(true);
		masterBrokerBean.setNumber(MASTER_BROKER_NUMBER);
		masterBrokerList.add(masterBrokerBean);
		accountBean.setMasterBrokerBeans(masterBrokerList);

		AccessProfileBean apBean1 = new AccessProfileBean();
		apBean1.setProfile(AccessProfileEnum.PL);
		apBean1.setSelected(false);
		AccessProfileBean apBean2 = new AccessProfileBean();
		apBean2.setProfile(AccessProfileEnum.IRCA);
		apBean2.setSelected(true);
		List<AccessProfileBean> apBeanList = new ArrayList<AccessProfileBean>();
		apBeanList.add(apBean1);
		apBeanList.add(apBean2);
		accountBean.setAccessProfileBeans(apBeanList);

		accountAdapter.loadModel(userAccount, accountBean, CifCompanyEnum.INTACT_QC, apMap);

		UserAccessProfile uapPL = fetchUserAccessProfile(AccessProfileEnum.PL);
		UserAccessProfile uapIRCA = fetchUserAccessProfile(AccessProfileEnum.IRCA);

		assertNotNull(uapPL.getExpiryDate());
		assertNull(uapIRCA.getExpiryDate());
	}

	@Test
	public void testLoadModelPLandIRCASelected() throws Exception {

		AccountBean accountBean = new AccountBean();
		ArrayList<MasterBrokerBean> masterBrokerList = new ArrayList<MasterBrokerBean>();
		MasterBrokerBean masterBrokerBean = new MasterBrokerBean();
		masterBrokerBean.setSelected(true);
		masterBrokerBean.setNumber(MASTER_BROKER_NUMBER);
		masterBrokerList.add(masterBrokerBean);
		accountBean.setMasterBrokerBeans(masterBrokerList);

		AccessProfileBean apBean1 = new AccessProfileBean();
		apBean1.setProfile(AccessProfileEnum.PL);
		apBean1.setSelected(true);
		AccessProfileBean apBean2 = new AccessProfileBean();
		apBean2.setProfile(AccessProfileEnum.IRCA);
		apBean2.setSelected(true);
		List<AccessProfileBean> apBeanList = new ArrayList<AccessProfileBean>();
		apBeanList.add(apBean1);
		apBeanList.add(apBean2);
		accountBean.setAccessProfileBeans(apBeanList);

		accountAdapter.loadModel(userAccount, accountBean, CifCompanyEnum.INTACT_QC, apMap);

		UserAccessProfile uapPL = fetchUserAccessProfile(AccessProfileEnum.PL);
		UserAccessProfile uapIRCA = fetchUserAccessProfile(AccessProfileEnum.IRCA);

		assertNull(uapPL.getExpiryDate());
		assertNull(uapIRCA.getExpiryDate());
	}


	private UserAccessProfile fetchUserAccessProfile(AccessProfileEnum ap) {
		Set<UserAccessProfile> uapSet = userAccount.getUserAccessProfiles();
		assertEquals(uapSet.size(), 2);

		List<UserAccessProfile> uapList = new ArrayList<UserAccessProfile>();
		for(UserAccessProfile uap : uapSet) {
			if (uap.getAccessProfile().getName() == ap)
				uapList.add(uap);
		}
		assertEquals(uapList.size(), 1);
		return uapList.get(0);
	}


	@Test
	public void testLoadForm(){
		UserAccount userAccount = new UserAccount();
    String USER_ACCOUNT_NAME = "Olivier";
    userAccount.setName(USER_ACCOUNT_NAME);
    String USER_ACCOUNT_UID = "678910";
    userAccount.setUid(USER_ACCOUNT_UID);
		BrokerWebOfficeAccess brokerWebOfficeAccess = new BrokerWebOfficeAccess();
		brokerWebOfficeAccess.setMasterOwnerCode(MASTER_BROKER_NUMBER);
		userAccount.addBrokerWebOfficeAccess(brokerWebOfficeAccess);

		userAccount.addUserAccessProfile(uapMap.get(AccessProfileEnum.PL));
		userAccount.addUserAccessProfile(uapMap.get(AccessProfileEnum.IRCA));

		AccountBean accountBean = new AccountBean();
		ArrayList<MasterBrokerBean> masterBrokerList = new ArrayList<MasterBrokerBean>();
		MasterBrokerBean masterBrokerBean = new MasterBrokerBean();
		masterBrokerBean.setSelected(true);
		masterBrokerBean.setNumber(MASTER_BROKER_NUMBER);
		masterBrokerList.add(masterBrokerBean);
		accountBean.setMasterBrokerBeans(masterBrokerList);

		AccessProfileBean apBean1 = new AccessProfileBean();
		apBean1.setProfile(AccessProfileEnum.PL);
		apBean1.setSelected(false);
		AccessProfileBean apBean2 = new AccessProfileBean();
		apBean2.setProfile(AccessProfileEnum.IRCA);
		apBean2.setSelected(false);
		List<AccessProfileBean> apBeanList = new ArrayList<AccessProfileBean>();
		apBeanList.add(apBean1);
		apBeanList.add(apBean2);
		accountBean.setAccessProfileBeans(apBeanList);

		accountAdapter.loadForm(userAccount, accountBean);

		assertEquals(USER_ACCOUNT_NAME, accountBean.getUserFullName());
		assertEquals(USER_ACCOUNT_UID, accountBean.getUserName());
		assertTrue(masterBrokerBean.getSelected());

		Set<AccessProfileBean> apBeanSet = new HashSet<AccessProfileBean>(accountBean.getAccessProfileBeans());
		assertEquals(apBeanSet.size(), 2);

		for (AccessProfileBean apBean : apBeanSet) {
			if (apBean.getProfile() == AccessProfileEnum.PL)
				assertTrue(apBean.getSelected());
			if (apBean.getProfile() == AccessProfileEnum.IRCA)
				assertFalse(apBean.getSelected());
		}


	}

	@Test
	public void testLoadUsers(){
		List<AccountBean> accountBeansList = new ArrayList<AccountBean>();
		List<UserAccount> userAccountList = new ArrayList<UserAccount>();
		userAccountList.add(newUserAccount("123", "Olivier"));
		userAccountList.add(newUserAccount("456", "Ariane"));
		userAccountList.add(newUserAccount("789", "Patrick"));

		accountAdapter.LoadUsers(userAccountList, accountBeansList);

		assertEquals(3, accountBeansList.size());
		assertEquals("123", accountBeansList.get(0).getUserName());
		assertEquals("456", accountBeansList.get(1).getUserName());
		assertEquals("789", accountBeansList.get(2).getUserName());

		assertEquals("Olivier", accountBeansList.get(0).getUserFullName());
		assertEquals("Ariane", accountBeansList.get(1).getUserFullName());
		assertEquals("Patrick", accountBeansList.get(2).getUserFullName());
	}

	private UserAccount newUserAccount(String Uid, String name){
		UserAccount userAccount = new UserAccount();
		userAccount.setName(name);
		userAccount.setUid(Uid);
		return userAccount;
	}

	@Test
	public void loadModel_withValidData_shouldLoadModelCorrectly() throws Exception {
		AccountBean accountBean = new AccountBean();
		ArrayList<MasterBrokerBean> masterBrokerList = new ArrayList<>();
		MasterBrokerBean masterBrokerBean = new MasterBrokerBean();
		masterBrokerBean.setSelected(true);
		masterBrokerBean.setNumber(MASTER_BROKER_NUMBER);
		masterBrokerList.add(masterBrokerBean);
		accountBean.setMasterBrokerBeans(masterBrokerList);

		AccessProfileBean apBean1 = new AccessProfileBean();
		apBean1.setProfile(AccessProfileEnum.PL);
		apBean1.setSelected(true);
		AccessProfileBean apBean2 = new AccessProfileBean();
		apBean2.setProfile(AccessProfileEnum.IRCA);
		apBean2.setSelected(false);
		List<AccessProfileBean> apBeanList = new ArrayList<>();
		apBeanList.add(apBean1);
		apBeanList.add(apBean2);
		accountBean.setAccessProfileBeans(apBeanList);

		accountAdapter.loadModel(userAccount, accountBean, CifCompanyEnum.BELAIR, apMap);

		UserAccessProfile uapPL = fetchUserAccessProfile(AccessProfileEnum.PL);
		UserAccessProfile uapIRCA = fetchUserAccessProfile(AccessProfileEnum.IRCA);

		assertNull(uapPL.getExpiryDate());
		assertNotNull(uapIRCA.getExpiryDate());
	}

	@Test
	public void loadModel_withNoSelectedMasterBroker_shouldNotAddBrokerWebOfficeAccess() throws Exception {
		AccountBean accountBean = new AccountBean();
		ArrayList<MasterBrokerBean> masterBrokerList = new ArrayList<>();
		MasterBrokerBean masterBrokerBean = new MasterBrokerBean();
		masterBrokerBean.setSelected(false);
		masterBrokerBean.setNumber(MASTER_BROKER_NUMBER);
		masterBrokerList.add(masterBrokerBean);
		accountBean.setMasterBrokerBeans(masterBrokerList);

		AccessProfileBean apBean1 = new AccessProfileBean();
		apBean1.setProfile(AccessProfileEnum.PL);
		apBean1.setSelected(true);
		AccessProfileBean apBean2 = new AccessProfileBean();
		apBean2.setProfile(AccessProfileEnum.IRCA);
		apBean2.setSelected(false);
		List<AccessProfileBean> apBeanList = new ArrayList<>();
		apBeanList.add(apBean1);
		apBeanList.add(apBean2);
		accountBean.setAccessProfileBeans(apBeanList);

		UserAccount userAccount = new UserAccount(); // Create a new UserAccount instance

		accountAdapter.loadModel(userAccount, accountBean, CifCompanyEnum.BELAIR, apMap);

		assertTrue(userAccount.getBrokerWebOfficeAccesses().isEmpty());
	}

	@Test
	public void loadModel_withAllProfilesSelected_shouldSetExpiryDateToNull() throws Exception {
		AccountBean accountBean = new AccountBean();
		ArrayList<MasterBrokerBean> masterBrokerList = new ArrayList<>();
		MasterBrokerBean masterBrokerBean = new MasterBrokerBean();
		masterBrokerBean.setSelected(true);
		masterBrokerBean.setNumber(MASTER_BROKER_NUMBER);
		masterBrokerList.add(masterBrokerBean);
		accountBean.setMasterBrokerBeans(masterBrokerList);

		AccessProfileBean apBean1 = new AccessProfileBean();
		apBean1.setProfile(AccessProfileEnum.PL);
		apBean1.setSelected(true);
		AccessProfileBean apBean2 = new AccessProfileBean();
		apBean2.setProfile(AccessProfileEnum.IRCA);
		apBean2.setSelected(true);
		List<AccessProfileBean> apBeanList = new ArrayList<>();
		apBeanList.add(apBean1);
		apBeanList.add(apBean2);
		accountBean.setAccessProfileBeans(apBeanList);

		accountAdapter.loadModel(userAccount, accountBean, CifCompanyEnum.BELAIR, apMap);

		UserAccessProfile uapPL = fetchUserAccessProfile(AccessProfileEnum.PL);
		UserAccessProfile uapIRCA = fetchUserAccessProfile(AccessProfileEnum.IRCA);

		assertNull(uapPL.getExpiryDate());
		assertNull(uapIRCA.getExpiryDate());
	}

	@Test
	public void loadModel_withNoProfilesSelected_shouldSetExpiryDate() throws Exception {
		AccountBean accountBean = new AccountBean();
		ArrayList<MasterBrokerBean> masterBrokerList = new ArrayList<>();
		MasterBrokerBean masterBrokerBean = new MasterBrokerBean();
		masterBrokerBean.setSelected(true);
		masterBrokerBean.setNumber(MASTER_BROKER_NUMBER);
		masterBrokerList.add(masterBrokerBean);
		accountBean.setMasterBrokerBeans(masterBrokerList);

		AccessProfileBean apBean1 = new AccessProfileBean();
		apBean1.setProfile(AccessProfileEnum.PL);
		apBean1.setSelected(false);
		AccessProfileBean apBean2 = new AccessProfileBean();
		apBean2.setProfile(AccessProfileEnum.IRCA);
		apBean2.setSelected(false);
		List<AccessProfileBean> apBeanList = new ArrayList<>();
		apBeanList.add(apBean1);
		apBeanList.add(apBean2);
		accountBean.setAccessProfileBeans(apBeanList);

		accountAdapter.loadModel(userAccount, accountBean, CifCompanyEnum.BELAIR, apMap);

		UserAccessProfile uapPL = fetchUserAccessProfile(AccessProfileEnum.PL);
		UserAccessProfile uapIRCA = fetchUserAccessProfile(AccessProfileEnum.IRCA);

		assertNotNull(uapPL.getExpiryDate());
		assertNotNull(uapIRCA.getExpiryDate());
	}

}


















