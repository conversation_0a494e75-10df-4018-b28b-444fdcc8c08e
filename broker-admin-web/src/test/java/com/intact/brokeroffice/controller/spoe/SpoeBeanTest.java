package com.intact.brokeroffice.controller.spoe;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;

import jakarta.faces.context.ExternalContext;
import jakarta.faces.context.FacesContext;
import jakarta.faces.model.SelectItem;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.springframework.mock.web.MockHttpSession;
import org.springframework.test.util.ReflectionTestUtils;

import com.ing.canada.cif.domain.enums.ApplicationIdEnum;
import com.ing.canada.cif.domain.enums.CifCompanyEnum;
import com.ing.canada.cif.domain.enums.LineOfBusinessEnum;
import com.ing.canada.common.web.api.auth.TamTokensEnum;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.intact.brokeroffice.business.accounts.IAccountsBusinessProcess;
import com.intact.brokeroffice.business.exception.BrokerServiceException;
import com.intact.brokeroffice.controller.province.ProvinceController;
import com.intact.canada.brm.domain.broker.BrokerWebOfficeAccess;
import com.intact.canada.brm.domain.user.UserAccount;

public class SpoeBeanTest {

	private MockedStatic<FacesContext> mockedFacesContext;

	private SpoeController spoeController = new SpoeController();

	private final String ldapGroupProgramAdmins = "GProgramAdmins -QC";

	private final String ldapGroupAdmins = "GoupAdmins -QC";

	private IAccountsBusinessProcess mockAccountsBusinessProcess;

	private ProvinceController mockProvinceController;

	@BeforeEach
	public void setUp() throws Exception {
		mockedFacesContext = mockStatic(FacesContext.class);
		mockProvinceController = mock(ProvinceController.class);
		mockAccountsBusinessProcess = mock(IAccountsBusinessProcess.class);

		ReflectionTestUtils.setField(spoeController, "ldapGroupProgramAdmins", ldapGroupProgramAdmins);
		ReflectionTestUtils.setField(spoeController, "ldapGroupAdmins", ldapGroupAdmins);
		ReflectionTestUtils.setField(spoeController, "accountsBusinessProcess", mockAccountsBusinessProcess);
		ReflectionTestUtils.setField(spoeController, "provinceController", mockProvinceController);
	}

	@AfterEach
	public void tearDown() throws Exception {
		mockedFacesContext.closeOnDemand();
	}

	@Test
	public void testInitAccessLevel() {
		List<SelectItem> selectedItemListResult = spoeController.getAccessLevel();
		assertEquals(10, selectedItemListResult.size());
	}

	@Test
	public void testLogin() throws Exception {
		FacesContext context = Mockito.mock(FacesContext.class);
		MockHttpSession session = new MockHttpSession();
		ExternalContext externalContext = mock(ExternalContext.class);

		Map<String, String> masterBrokerMap = new HashMap<String, String>();
		masterBrokerMap.put("1", "Broker1");
		masterBrokerMap.put("2", "Broker2");
		masterBrokerMap.put("3", "Broker3");
		ReflectionTestUtils.setField(spoeController, "spoeBean", newSpoeBean(ldapGroupAdmins));
		when(mockProvinceController.getCompanyEnumCode()).thenReturn(CifCompanyEnum.BELAIR);
		when(mockProvinceController.getProvinceCode()).thenReturn(ProvinceCodeEnum.QUEBEC);
		when(
				mockAccountsBusinessProcess.getAssignedMasterBrokers(any(CifCompanyEnum.class),
						anyString(), anyList(), anyList())).thenReturn(masterBrokerMap);

		BrokerWebOfficeAccess aBrokerWebOfficeAccess = new BrokerWebOfficeAccess();
		aBrokerWebOfficeAccess.setMasterOwnerCode("MasterOwnerCodeTest");

		UserAccount userAccount = new UserAccount();
		userAccount.addBrokerWebOfficeAccess(aBrokerWebOfficeAccess);
		mockedFacesContext.when(FacesContext::getCurrentInstance).thenReturn(context);
		when(context.getExternalContext()).thenReturn(externalContext);
		when(externalContext.getSession(true)).thenReturn(session);
		when(mockAccountsBusinessProcess.getDefaultProvince("1")).thenReturn(null);
		when(mockAccountsBusinessProcess.findByUId(Mockito.anyString())).thenReturn(userAccount);

		spoeController.getMasterBrokers();
		String stringIndex = spoeController.login();

		assertEquals("index", stringIndex);

		assertEquals("DA7143", session.getAttribute(TamTokensEnum.TAM_USER_CLASSIC_USER_ID.getTokenValue()));

	}

	@Test
	public void testLoginAdmin() throws BrokerServiceException {
		FacesContext context = Mockito.mock(FacesContext.class);
		MockHttpSession session = new MockHttpSession();
		HttpServletRequest request = mock(HttpServletRequest.class);
		ExternalContext externalContext = mock(ExternalContext.class);

		ReflectionTestUtils.setField(spoeController, "spoeBean", newSpoeBean(ldapGroupAdmins));

		Cookie cookies[] = new Cookie[1];
		Cookie cookie = new Cookie("brokerDefaultProvince", "QC");
		cookies[0] = cookie;
		when(mockProvinceController.getCompanyEnumCode()).thenReturn(CifCompanyEnum.BELAIR);
		when(mockProvinceController.getProvinceCode()).thenReturn(ProvinceCodeEnum.QUEBEC);
		mockedFacesContext.when(FacesContext::getCurrentInstance).thenReturn(context);
		when(context.getExternalContext()).thenReturn(externalContext);
		when(externalContext.getSession(true)).thenReturn(session);
		when(externalContext.getRequest()).thenReturn(request);
		when(request.getCookies()).thenReturn(cookies);

		// Execute
		try {
			spoeController.login();
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}

		// Verify
		assertNull(mockProvinceController.getProvince());
		assertEquals("1", session.getAttribute(TamTokensEnum.TAM_USER_NAME_CREDENTIAL.getTokenValue()));
		assertEquals(ldapGroupAdmins, session.getAttribute(TamTokensEnum.TAM_SECURITY_ROLES_ATTRIB.getTokenValue()));
	}

	private SpoeBean newSpoeBean(String selectedAccessLevel) {
		SpoeBean spoeBean = new SpoeBean();
		spoeBean.setUserId("1");
		spoeBean.setSelectedAccessLevel(selectedAccessLevel);
		spoeBean.setSelectedProvince("QC");
		return spoeBean;
	}
}
