/**
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance 
 * Copyright (c) 2009, Intact Insurance, All rights reserved.<br>
 */
package com.intact.brokeroffice.controller.report.brokerchange;

import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.intact.brokeroffice.controller.brokerchanges.BrokerChangeValidator;

/**
 * <AUTHOR>
 *
 */
public class BrokerChangeValidatorTest {

	private BrokerChangeValidator  brokerChangeValidator;	

	@BeforeEach
	public void setup(){		
		this.brokerChangeValidator = new BrokerChangeValidator();	
		this.brokerChangeValidator.setShowErrorMessage(false);		
	}
	
	@Test
	public void testPeriodDates(){				
		
		//the period From date cannot be in future  ---> TEST MUST FAIL	
		GregorianCalendar futureCalendar =  new GregorianCalendar();
		futureCalendar.setTime(new Date());
		futureCalendar.add(Calendar.DATE, 10);		
		Assertions.assertFalse(this.brokerChangeValidator.validate(futureCalendar.getTime(), new Date()));
			
		//The period To date cannot be in future  ---> TEST MUST FAIL			
		Assertions.assertFalse(this.brokerChangeValidator.validate(new Date(), futureCalendar.getTime()));
		
		//The period From date must be less than or equal to the period To date  ---> TEST MUST FAIL
		GregorianCalendar fromCalendar =  new GregorianCalendar();
		fromCalendar =  new GregorianCalendar();
		fromCalendar.set(2010, 4, 1);
		
		GregorianCalendar toCalendar =  new GregorianCalendar();		
		toCalendar.set(2010, 0, 1);			
		Assertions.assertFalse(this.brokerChangeValidator.validate(fromCalendar.getTime(), toCalendar.getTime()));
					
		//TEST MUST PASS NOW
		toCalendar =  new GregorianCalendar();		
		toCalendar.set(2011, 0 , 1);			
		Assertions.assertTrue(this.brokerChangeValidator.validate(fromCalendar.getTime(), toCalendar.getTime()));
	}
	
	
	
	/**
	 * Utility method need to run this test case in Maven 1.
	 * 
	 * @return Test suite
	 */
//	public static junit.framework.Test suite() {
//		return new JUnit4TestAdapter(BrokerChangeValidatorTest.class);
//	}
}
