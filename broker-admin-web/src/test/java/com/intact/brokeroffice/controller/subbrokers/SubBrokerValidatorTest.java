package com.intact.brokeroffice.controller.subbrokers;

import com.ing.canada.cif.domain.enums.ApplicationIdEnum;
import com.ing.canada.cif.domain.enums.LineOfBusinessEnum;
import com.intact.brokeroffice.business.dto.*;
import com.intact.brokeroffice.helper.FaceMessageHelper;
import com.intact.brokeroffice.web.dto.JSFImageDTO;
import com.intact.brokeroffice.web.dto.PhoneSectionCodeEnum;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;

import java.lang.reflect.Method;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.*;

public class SubBrokerValidatorTest {
	private MockedStatic<FaceMessageHelper> mockedFaceMessageHelper;
	private SubBrokerValidator validator;

	@BeforeEach
	public void setUp() {
		mockedFaceMessageHelper = mockStatic(FaceMessageHelper.class);
		validator = new SubBrokerValidator();
	}

	@AfterEach
	void tearDownStaticMocks() {
		mockedFaceMessageHelper.closeOnDemand();
	}

	@Test
	public void test_validateOnlineName_noOnlineName_NoImages() throws Exception {
    // Prepare
    SubBrokerDTO subBrokerBean = new SubBrokerDTO();
    subBrokerBean.setOnlineName(null);
    subBrokerBean.setImages(Collections.<String, ImageDTO>emptyMap());

    // Get the private method and make it accessible
    Method method = SubBrokerValidator.class.getDeclaredMethod("validateOnlineName", SubBrokerDTO.class);
    method.setAccessible(true);

    // Execute
    boolean result = (boolean) method.invoke(validator, subBrokerBean);

    // Verify
    assertFalse(result);
	}

	@Test
	public void test_validateOnlineName_anOnlineName_NoImages() throws Exception {
    // Prepare
    SubBrokerDTO subBrokerBean = new SubBrokerDTO();
    subBrokerBean.setOnlineName("My Online Name");
    subBrokerBean.setImages(Collections.<String, ImageDTO>emptyMap());

    // Get the private method and make it accessible
    Method method = SubBrokerValidator.class.getDeclaredMethod("validateOnlineName", SubBrokerDTO.class);
    method.setAccessible(true);

    // Execute
    boolean result = (boolean) method.invoke(validator, subBrokerBean);

    // Verify
    assertTrue(result);
	}

	@Test
	public void test_validateOnlineName_anOnlineName_WithImages() throws Exception {
    // Prepare
    JSFImageDTO image101 = mock(JSFImageDTO.class);
    JSFImageDTO image202 = mock(JSFImageDTO.class);

    SubBrokerDTO subBrokerBean = new SubBrokerDTO();
    subBrokerBean.setOnlineName("My Online Name 2");
    subBrokerBean.setImages(new HashMap<String, ImageDTO>());
    subBrokerBean.getImages().put("IMAGE-101", image101);
    subBrokerBean.getImages().put("IMAGE-202", image202);

    when(image202.getPreviousImage()).thenReturn(new byte[] {1, 2, 3});

    // Get the private method and make it accessible
    Method method = SubBrokerValidator.class.getDeclaredMethod("validateOnlineName", SubBrokerDTO.class);
    method.setAccessible(true);

    // Execute
    boolean result = (boolean) method.invoke(validator, subBrokerBean);

    // Verify
    assertTrue(result);
	}

  @Test
  public void test_validateBundlePhoneNumbers_when_noPhone_Should_Pass() throws Exception {

    SubBrokerDTO testSubbroker = new SubBrokerDTO();

    Map<String, PhoneDTO> phones = new HashMap<String, PhoneDTO>();

    ApplicationDTO application = new ApplicationDTO();
    application.setAccessType("QIB");
    application.setId(ApplicationIdEnum.WEB_QUOTE.getCode());
    application.setAssignable(true);
    application.setPhones(phones);
    Map<String, ApplicationDTO> applications = new HashMap<String, ApplicationDTO>();
    applications.put(PhoneSectionCodeEnum.DEFAULT.getCode(), application);

    LineOfBusinessDTO lineOfBusinessDTO = new LineOfBusinessDTO();
    lineOfBusinessDTO.setLineOfBusiness(LineOfBusinessEnum.PERSONAL_LINE.getCode());
    lineOfBusinessDTO.setApplications(applications);
    Map<String,LineOfBusinessDTO> linesOfBusinessesDTO = new HashMap<String,LineOfBusinessDTO>();
    linesOfBusinessesDTO.put(LineOfBusinessEnum.PERSONAL_LINE.getCode(), lineOfBusinessDTO);

    testSubbroker.setLinesOfBusiness(linesOfBusinessesDTO);

    boolean result = (boolean) SubBrokerValidator.class.getDeclaredMethod("validateBundlePhoneNumbers", SubBrokerDTO.class)
                                                       .invoke(validator, testSubbroker);

    assertTrue(result);
  }

  @Test
  public void test_validateBundlePhoneNumbers_when_allPhone_Should_Pass() throws Exception {

    SubBrokerDTO testSubbroker = new SubBrokerDTO();

    PhoneDTO phone = new PhoneDTO();
    phone.setPhone("5554448888");
    phone.setUsage(PhoneSectionCodeEnum.DEFAULT.getCode());
    PhoneDTO phoneOffer = new PhoneDTO();
    phoneOffer.setPhone("3336669999");
    phoneOffer.setUsage(PhoneSectionCodeEnum.OFFER.getCode());
    Map<String, PhoneDTO> phones = new HashMap<String, PhoneDTO>();
    phones.put(PhoneSectionCodeEnum.DEFAULT.getCode(), phone);
    phones.put(PhoneSectionCodeEnum.OFFER.getCode(), phoneOffer);

    ApplicationDTO application = new ApplicationDTO();
    application.setAccessType("QIB");
    application.setId(ApplicationIdEnum.WEB_QUOTE.getCode());
    application.setAssignable(true);
    application.setPhones(phones);
    Map<String, ApplicationDTO> applications = new HashMap<String, ApplicationDTO>();
    applications.put(PhoneSectionCodeEnum.DEFAULT.getCode(), application);

    LineOfBusinessDTO lineOfBusinessDTO = new LineOfBusinessDTO();
    lineOfBusinessDTO.setLineOfBusiness(LineOfBusinessEnum.PERSONAL_LINE.getCode());
    lineOfBusinessDTO.setApplications(applications);
    Map<String,LineOfBusinessDTO> linesOfBusinessesDTO = new HashMap<String,LineOfBusinessDTO>();
    linesOfBusinessesDTO.put(LineOfBusinessEnum.PERSONAL_LINE.getCode(), lineOfBusinessDTO);

    testSubbroker.setLinesOfBusiness(linesOfBusinessesDTO);

    boolean result = (boolean) SubBrokerValidator.class.getDeclaredMethod("validateBundlePhoneNumbers", SubBrokerDTO.class)
                                                       .invoke(validator, testSubbroker);

    assertTrue(result);
  }

	@Test
  public void test_validateBundlePhoneNumbers_when_onlyDefault_Should_Fail() throws Exception {

    SubBrokerDTO testSubbroker = new SubBrokerDTO();

    PhoneDTO phone = new PhoneDTO();
    phone.setPhone("5554448888");
    phone.setUsage(PhoneSectionCodeEnum.DEFAULT.getCode());
    Map<String, PhoneDTO> phones = new HashMap<String, PhoneDTO>();
    phones.put(PhoneSectionCodeEnum.DEFAULT.getCode(), phone);

    ApplicationDTO application = new ApplicationDTO();
    application.setAccessType("QIB");
    application.setId(ApplicationIdEnum.WEB_QUOTE.getCode());
    application.setAssignable(true);
    application.setPhones(phones);
    Map<String, ApplicationDTO> applications = new HashMap<String, ApplicationDTO>();
    applications.put(PhoneSectionCodeEnum.DEFAULT.getCode(), application);

    LineOfBusinessDTO lineOfBusinessDTO = new LineOfBusinessDTO();
    lineOfBusinessDTO.setLineOfBusiness(LineOfBusinessEnum.PERSONAL_LINE.getCode());
    lineOfBusinessDTO.setApplications(applications);
    Map<String,LineOfBusinessDTO> linesOfBusinessesDTO = new HashMap<String,LineOfBusinessDTO>();
    linesOfBusinessesDTO.put(LineOfBusinessEnum.PERSONAL_LINE.getCode(), lineOfBusinessDTO);

    testSubbroker.setLinesOfBusiness(linesOfBusinessesDTO);

    boolean result = (boolean) SubBrokerValidator.class.getDeclaredMethod("validateBundlePhoneNumbers", SubBrokerDTO.class)
                                                       .invoke(validator, testSubbroker);

    assertFalse(result);
  }
  @Test
  public void test_validateBundlePhoneNumbers_when_onlyOFFPG_Should_Fail() throws Exception {

    SubBrokerDTO testSubbroker = new SubBrokerDTO();

    PhoneDTO phone = new PhoneDTO();
    phone.setPhone("5554448888");
    phone.setUsage(PhoneSectionCodeEnum.OFFER.getCode());
    Map<String, PhoneDTO> phones = new HashMap<String, PhoneDTO>();
    phones.put(PhoneSectionCodeEnum.OFFER.getCode(), phone);

    ApplicationDTO application = new ApplicationDTO();
    application.setAccessType("QIB");
    application.setId(ApplicationIdEnum.WEB_QUOTE.getCode());
    application.setAssignable(true);
    application.setPhones(phones);
    Map<String, ApplicationDTO> applications = new HashMap<String, ApplicationDTO>();
    applications.put(PhoneSectionCodeEnum.DEFAULT.getCode(), application);

    LineOfBusinessDTO lineOfBusinessDTO = new LineOfBusinessDTO();
    lineOfBusinessDTO.setLineOfBusiness(LineOfBusinessEnum.PERSONAL_LINE.getCode());
    lineOfBusinessDTO.setApplications(applications);
    Map<String,LineOfBusinessDTO> linesOfBusinessesDTO = new HashMap<String,LineOfBusinessDTO>();
    linesOfBusinessesDTO.put(LineOfBusinessEnum.PERSONAL_LINE.getCode(), lineOfBusinessDTO);

    testSubbroker.setLinesOfBusiness(linesOfBusinessesDTO);

    boolean result = (boolean) SubBrokerValidator.class.getDeclaredMethod("validateBundlePhoneNumbers", SubBrokerDTO.class)
                                                       .invoke(validator, testSubbroker);
    assertFalse(result);
  }
}
