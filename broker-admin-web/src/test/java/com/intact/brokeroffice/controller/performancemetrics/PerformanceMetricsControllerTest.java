package com.intact.brokeroffice.controller.performancemetrics;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.test.util.ReflectionTestUtils;

import com.ing.canada.cif.domain.enums.CifCompanyEnum;
import com.ing.canada.plp.domain.enums.ManufacturerCompanyCodeEnum;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.ing.canada.plp.report.performancemetric.PerformanceMetricInfo;
import com.ing.canada.plp.report.service.IPerformanceMetricInfoService;
import com.intact.brokeroffice.controller.province.ProvinceController;

public class PerformanceMetricsControllerTest {

	private PerformanceMetricsController performanceMetricsController;
	private IPerformanceMetricInfoService mockPerformanceMetricInfoService;
	private ProvinceController mockProvinceController;
	private List<PerformanceMetricInfo> listMetricInfo;

	@BeforeEach
	public void setUp() {
		performanceMetricsController = new PerformanceMetricsController();
		mockPerformanceMetricInfoService = mock(IPerformanceMetricInfoService.class);
		mockProvinceController = mock(ProvinceController.class);

		fillListMetricInfo();

		when(mockPerformanceMetricInfoService.getPerformanceMetricListSP(
				any(Date.class), any(Date.class), anyString(), any(ProvinceCodeEnum.class),
				anyString(), any(ManufacturerCompanyCodeEnum.class))).thenReturn(listMetricInfo);
		when(mockProvinceController.getProvinceCode()).thenReturn(ProvinceCodeEnum.QUEBEC);
		when(mockProvinceController.getCompanyEnumCode()).thenReturn(CifCompanyEnum.BELAIR);
		when(mockProvinceController.getManufacturerCompanyCode()).thenReturn(ManufacturerCompanyCodeEnum.BELAIRDIRECT);

		ReflectionTestUtils.setField(performanceMetricsController, "performanceMetricInfoService", mockPerformanceMetricInfoService);
		ReflectionTestUtils.setField(performanceMetricsController, "provinceController", mockProvinceController);
	}

	@AfterEach
	public void tearDown() {
		performanceMetricsController = null;
		mockPerformanceMetricInfoService = null;
		mockProvinceController = null;
		listMetricInfo = null;
	}

	//TODO need to fix this test
	@Disabled
	@Test
	public void retrieveConsolidatedPerformanceMetricBeans_shouldReturnNonEmptyList_whenMetricsExist() {
		performanceMetricsController.retrieveConsolidatedPerformanceMetricBeans();
		List<ConsolidatedPerformanceMetricBean> consolidatedPerformanceMetricBeans = performanceMetricsController.getConsolidatedPerformanceMetricBeans();
		assertFalse(consolidatedPerformanceMetricBeans.isEmpty(), "The consolidated performance metric beans list should not be empty");
	}

	@Test
	public void retrieveConsolidatedPerformanceMetricBeans_shouldReturnEmptyList_whenNoMetricsExist() {
		when(mockPerformanceMetricInfoService.getPerformanceMetricListSP(any(), any(), anyString(), any(), anyString(), any())).thenReturn(Collections.emptyList());
		performanceMetricsController.retrieveConsolidatedPerformanceMetricBeans();
		List<ConsolidatedPerformanceMetricBean> consolidatedPerformanceMetricBeans = performanceMetricsController.getConsolidatedPerformanceMetricBeans();
		assertTrue(consolidatedPerformanceMetricBeans.isEmpty(), "The consolidated performance metric beans list should be empty");
	}

	@Test
	public void retrieveConsolidatedPerformanceMetricBeans_shouldSortBeansByBrokerNumber() {
		performanceMetricsController.retrieveConsolidatedPerformanceMetricBeans();
		List<ConsolidatedPerformanceMetricBean> consolidatedPerformanceMetricBeans = performanceMetricsController.getConsolidatedPerformanceMetricBeans();
		assertTrue(isSortedByBrokerNumber(consolidatedPerformanceMetricBeans), "The consolidated performance metric beans list should be sorted by broker number");
	}

	@Test
	public void retrieveConsolidatedPerformanceMetricBeans_shouldHandleNullPerformanceMetricList() {
		when(mockPerformanceMetricInfoService.getPerformanceMetricListSP(any(), any(), anyString(), any(), anyString(), any())).thenReturn(null);
		performanceMetricsController.retrieveConsolidatedPerformanceMetricBeans();
		List<ConsolidatedPerformanceMetricBean> consolidatedPerformanceMetricBeans = performanceMetricsController.getConsolidatedPerformanceMetricBeans();
		assertTrue(consolidatedPerformanceMetricBeans.isEmpty(), "The consolidated performance metric beans list should be empty when the performance metric list is null");
	}

	private boolean isSortedByBrokerNumber(List<ConsolidatedPerformanceMetricBean> beans) {
		for (int i = 0; i < beans.size() - 1; i++) {
			if (beans.get(i).getBrokerNbr().compareTo(beans.get(i + 1).getBrokerNbr()) > 0) {
				return false;
			}
		}
		return true;
	}

	private void fillListMetricInfo() {
		listMetricInfo = new ArrayList<>();
		listMetricInfo.add(newPerformanceMetricInfo("BelairDirect", "SubBrokerName", "1", "1"));
		listMetricInfo.add(newPerformanceMetricInfo("Intact", "SubBrokerName2", "1", "1"));
	}

	private PerformanceMetricInfo newPerformanceMetricInfo(String masterName, String subName, String masterNbr, String subNbr) {
		PerformanceMetricInfo metricInfo = new PerformanceMetricInfo();
		metricInfo.setMasterBrokerName(masterName);
		metricInfo.setMasterBrokerNbr(masterNbr);
		metricInfo.setSubBrokerName(subName);
		metricInfo.setSubBrokerNbr(subNbr);
		metricInfo.setFollowUpContactNeeded(BigDecimal.ONE);
		metricInfo.setFollowUpNeverContacted(BigDecimal.ONE);
		metricInfo.setFollowUpNoContactNeeded(BigDecimal.ONE);
		metricInfo.setFollowUpNoneRequired(BigDecimal.ONE);
		metricInfo.setFollowUpTotal(BigDecimal.ONE);
		metricInfo.setQuotesComplete(BigDecimal.ONE);
		metricInfo.setQuotesExpired(BigDecimal.ONE);
		metricInfo.setQuotesIncomplete(BigDecimal.ONE);
		metricInfo.setQuotesTotal(BigDecimal.ONE);
		metricInfo.setPurchaseRequestComplete(BigDecimal.ONE);
		metricInfo.setPurchaseRequestIncomplete(BigDecimal.ONE);
		metricInfo.setUploadedQuotesAccepted(BigDecimal.ONE);
		metricInfo.setUploadedQuotesNotProceed(BigDecimal.ONE);
		metricInfo.setUploadedQuotesRefused(BigDecimal.ONE);
		return metricInfo;
	}
}
