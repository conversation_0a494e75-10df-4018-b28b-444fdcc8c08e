package com.intact.brokeroffice.controller.subbrokers;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import com.ing.canada.cif.domain.IContextualElectronicLocators;
import com.ing.canada.cif.domain.IContextualPhoneNumbers;
import com.ing.canada.cif.domain.IContextualSubBrokerGnInfos;
import com.ing.canada.cif.domain.ILocatorTimeRange;
import com.ing.canada.cif.domain.ISubBrokers;
import com.ing.canada.cif.domain.enums.ApplicationIdEnum;
import com.ing.canada.cif.domain.enums.ApplicationOriginEnum;
import com.ing.canada.cif.domain.enums.ElectronicAddressPurposeEnum;
import com.ing.canada.cif.domain.enums.ElectronicAddressTypeEnum;
import com.ing.canada.cif.domain.enums.ImageMimeTypeEnum;
import com.ing.canada.cif.domain.enums.ImageNatureEnum;
import com.ing.canada.cif.domain.enums.ImageUsageEnum;
import com.ing.canada.cif.domain.enums.LanguageEnum;
import com.ing.canada.cif.domain.enums.LineOfBusinessEnum;
import com.ing.canada.cif.domain.enums.PhoneNumberUsageEnum;
import com.ing.canada.cif.domain.enums.WeekDayCodeEnum;
import com.ing.canada.cif.domain.impl.BrandVisuals;
import com.ing.canada.cif.domain.impl.ContextualElectronicLocators;
import com.ing.canada.cif.domain.impl.ContextualPhoneNumbers;
import com.ing.canada.cif.domain.impl.ContextualSubBrokerGnInfos;
import com.ing.canada.cif.domain.impl.LocatorTimeRanges;
import com.ing.canada.cif.domain.impl.SubBrokers;
import com.intact.brokeroffice.web.dto.PhoneSectionCodeEnum;
import com.intact.brokeroffice.business.dto.ApplicationDTO;
import com.intact.brokeroffice.business.dto.BusinessHourDTO;
import com.intact.brokeroffice.business.dto.ElectronicContactDTO;
import com.intact.brokeroffice.business.dto.ImageContextEnum;
import com.intact.brokeroffice.business.dto.ImageDTO;
import com.intact.brokeroffice.business.dto.LineOfBusinessDTO;
import com.intact.brokeroffice.business.dto.PhoneDTO;
import com.intact.brokeroffice.business.dto.PurposeEnum;
import com.intact.brokeroffice.business.dto.ServiceDTO;
import com.intact.brokeroffice.business.dto.SubBrokerDTO;
import com.intact.brokeroffice.business.dto.UsageEnum;
import com.intact.brokeroffice.business.subbrokers.ISubBrokersBusinessProcess;
import com.intact.brokeroffice.web.dto.JSFImageDTO;
import org.mockito.MockitoAnnotations;
import org.primefaces.model.file.UploadedFile;

public class SubBrokerAdapterTest {
	/** Class to be tested **/
	@InjectMocks
	private SubBrokerAdapter adapter = new SubBrokerAdapter();

	/** Mocks **/
	@Mock
	private ISubBrokers testSubBroker;

	@Mock
	private ISubBrokersBusinessProcess mockSubBrokersBusinessProcess;

	/** General test objects **/
	private SubBrokerDTO testSubBrokerBean;

	private IContextualSubBrokerGnInfos testAQAccessType;

	private IContextualSubBrokerGnInfos testQQAccessType;

	private IContextualSubBrokerGnInfos testIRCAQQAccessType;

	private IContextualSubBrokerGnInfos testPCQQAccessType;

	/** Constants **/
	private static final String CURRENT_USER = "testUser";

	@BeforeEach
	public void setUp() {
		MockitoAnnotations.openMocks(this);

		this.createGeneralSubBroker();
		this.createGeneralSubBrokerBean();
		// Set dummy user for certain tests
		this.adapter.setCurrentUser(CURRENT_USER);
	}

	/**
	 * Test for
	 * {@link SubBrokerAdapter#saveContact(ISubBrokers, String, ApplicationIdEnum,
	 * 			LineOfBusinessEnum, PhoneSectionCodeEnum, PurposeEnum)}
	 * Case when a new electronic contact for personal lines is added.
	 * @throws Exception
	 */
	@Test
	public void test_saveContact_addNewPL() throws Exception {
		// Add to the bean a new email and chat id for personal lines that is not in the subbroker
		String testEmail = "<EMAIL>";
		String testChatId = "test12345";
		ElectronicContactDTO contactPL = this.testSubBrokerBean.getLinesOfBusiness().get(LineOfBusinessEnum.PERSONAL_LINE.getCode()).getContact();
		contactPL.setEmailAddress(testEmail);
		contactPL.setChatId(testChatId);

		// Execute the tested method
		this.adapter.saveSubBroker((SubBrokers)this.testSubBroker, (SubBrokerDTO)this.testSubBrokerBean);

		// Validate the results
		assertEquals(2, this.testSubBroker.getContextualElectronicLocators().size());
		IContextualElectronicLocators resultEmail =
				this.testSubBroker.getContact(null, LineOfBusinessEnum.PERSONAL_LINE, null, ElectronicAddressTypeEnum.EMAIL, null);
		IContextualElectronicLocators resultChatId =
				this.testSubBroker.getContact(null, LineOfBusinessEnum.PERSONAL_LINE, null, ElectronicAddressTypeEnum.URL, ElectronicAddressPurposeEnum.CHAT);
		assertEquals(testEmail, resultEmail.getElectronicAddress());
		assertEquals(CURRENT_USER, resultEmail.getCreatedByUserID());
		assertEquals(testChatId, resultChatId.getElectronicAddress());
		assertEquals(CURRENT_USER, resultChatId.getCreatedByUserID());
	}

	/**
	 * Test for
	 * {@link SubBrokerAdapter#saveContact(ISubBrokers, String, ApplicationIdEnum,
	 * 			LineOfBusinessEnum, PhoneSectionCodeEnum, PurposeEnum)}
	 * Case when a new electronic contact for commercial lines is added.
	 * @throws Exception
	 */
	@Test
	public void test_saveContact_addNewCL() throws Exception {
		// Add to the bean a new email and chat id for personal lines that is not in the subbroker
		String testEmail = "<EMAIL>";
		String testChatId = "test12345";
		ElectronicContactDTO contactPL = this.testSubBrokerBean.getLinesOfBusiness().get(LineOfBusinessEnum.COMMERCIAL_LINE.getCode()).getContact();
		contactPL.setEmailAddress(testEmail);
		contactPL.setChatId(testChatId);

		// Execute the tested method
		this.adapter.saveSubBroker((SubBrokers)this.testSubBroker, (SubBrokerDTO)this.testSubBrokerBean);

		// Validate the results
		assertEquals(2, this.testSubBroker.getContextualElectronicLocators().size());
		IContextualElectronicLocators resultEmail =
				this.testSubBroker.getContact(null, LineOfBusinessEnum.COMMERCIAL_LINE, null, ElectronicAddressTypeEnum.EMAIL, null);
		IContextualElectronicLocators resultChatId =
				this.testSubBroker.getContact(null, LineOfBusinessEnum.COMMERCIAL_LINE, null, ElectronicAddressTypeEnum.URL, ElectronicAddressPurposeEnum.CHAT);
		assertEquals(testEmail, resultEmail.getElectronicAddress());
		assertEquals(CURRENT_USER, resultEmail.getCreatedByUserID());
		assertEquals(testChatId, resultChatId.getElectronicAddress());
		assertEquals(CURRENT_USER, resultChatId.getCreatedByUserID());
	}

	/**
	 * Test for
	 * {@link SubBrokerAdapter#saveContact(ISubBrokers, String, ApplicationIdEnum,
	 * 			LineOfBusinessEnum, PhoneSectionCodeEnum, PurposeEnum)}
	 * Case when an electronic contact to be updated.
	 * @throws Exception
	 */
	@Test
	public void test_saveContact_update() throws Exception {
		// Set old email and chat id values in the subbroker
		IContextualElectronicLocators oldEmail = new ContextualElectronicLocators();
		oldEmail.setElectronicAddress("<EMAIL>");
		oldEmail.setLineOfBusinessCode(LineOfBusinessEnum.PERSONAL_LINE);
		oldEmail.setElectronicAddressType(ElectronicAddressTypeEnum.EMAIL);
		oldEmail.setFromBrokerFile("N");
		IContextualElectronicLocators oldChatId = new ContextualElectronicLocators();
		oldChatId.setElectronicAddress("oldChatId");
		oldChatId.setLineOfBusinessCode(LineOfBusinessEnum.PERSONAL_LINE);
		oldChatId.setElectronicAddressType(ElectronicAddressTypeEnum.URL);
		oldChatId.setPurposeCode(ElectronicAddressPurposeEnum.CHAT);
		oldChatId.setFromBrokerFile("N");

		this.testSubBroker.getContextualElectronicLocators().add(oldEmail);
		this.testSubBroker.getContextualElectronicLocators().add(oldChatId);

		// Set new email and chat id values in the bean for the same context as the old values
		ElectronicContactDTO contactPL = this.testSubBrokerBean.getLinesOfBusiness().get(LineOfBusinessEnum.PERSONAL_LINE.getCode()).getContact();
		contactPL.setEmailAddress("<EMAIL>");
		contactPL.setChatId("newchatId");

		// Execute the tested method
		this.adapter.saveSubBroker((SubBrokers)this.testSubBroker, (SubBrokerDTO)this.testSubBrokerBean);

		IContextualElectronicLocators resultEmail =
				this.testSubBroker.getContact(null, LineOfBusinessEnum.PERSONAL_LINE, null, ElectronicAddressTypeEnum.EMAIL, null);
		IContextualElectronicLocators resultChatId =
				this.testSubBroker.getContact(null, LineOfBusinessEnum.PERSONAL_LINE, null, ElectronicAddressTypeEnum.URL, ElectronicAddressPurposeEnum.CHAT);

		// Validate the results
		assertEquals(4, this.testSubBroker.getContextualElectronicLocators().size()); // There should be 2 new contacts
		assertNotNull(oldEmail.getEndDate(), "Old email electronic locator should now have an end date.");
		assertNotNull(oldChatId.getEndDate(), "Old chatid electronic locator should now have an end date.");
		assertEquals(CURRENT_USER, resultEmail.getCreatedByUserID());
		assertEquals(CURRENT_USER, resultChatId.getCreatedByUserID());
	}

	/**
	 * Test for
	 * {@link SubBrokerAdapter#saveContact(ISubBrokers, String, ApplicationIdEnum,
	 * 			LineOfBusinessEnum, PhoneSectionCodeEnum, PurposeEnum)}
	 * Case when an electronic contact to be removed (end date set).
	 * @throws Exception
	 */
	@Test
	public void test_saveContact_remove() throws Exception {
		// Set old email and chat id values in the subbroker
		IContextualElectronicLocators oldEmail = new ContextualElectronicLocators();
		oldEmail.setElectronicAddress("<EMAIL>");
		oldEmail.setLineOfBusinessCode(LineOfBusinessEnum.PERSONAL_LINE);
		oldEmail.setElectronicAddressType(ElectronicAddressTypeEnum.EMAIL);
		oldEmail.setFromBrokerFile("N");
		IContextualElectronicLocators oldChatId = new ContextualElectronicLocators();
		oldChatId.setElectronicAddress("oldChatId");
		oldChatId.setLineOfBusinessCode(LineOfBusinessEnum.PERSONAL_LINE);
		oldChatId.setElectronicAddressType(ElectronicAddressTypeEnum.URL);
		oldChatId.setPurposeCode(ElectronicAddressPurposeEnum.CHAT);
		oldChatId.setFromBrokerFile("N");

		this.testSubBroker.getContextualElectronicLocators().add(oldEmail);
		this.testSubBroker.getContextualElectronicLocators().add(oldChatId);

		// Set new values to be empty so that they are removed
		ElectronicContactDTO contactPL = this.testSubBrokerBean.getLinesOfBusiness().get(LineOfBusinessEnum.PERSONAL_LINE.getCode()).getContact();
		contactPL.setEmailAddress("");
		contactPL.setChatId("");

		// Execute the tested method
		this.adapter.saveSubBroker((SubBrokers)this.testSubBroker, (SubBrokerDTO)this.testSubBrokerBean);

		// Validate the results
		assertEquals(2, this.testSubBroker.getContextualElectronicLocators().size()); // Should still be only 2 contacts
		assertNotNull(oldEmail.getEndDate(), "Old email electronic locator should now have an end date.");
		assertNotNull(oldChatId.getEndDate(), "Old chatid electronic locator should now have an end date.");
	}

	/**
	 * Test for
	 * {@link SubBrokerAdapter#fillPhone(IContextualPhoneNumbers, ISubBrokers,
	 * 					PhoneDTO, LineOfBusinessEnum, ApplicationIdEnum, String)}
	 * Case when a new phone number is added.
	 * @throws Exception
	 */
	@Test
	public void test_fillPhone_addNew() throws Exception {
		// Add to the bean a new phone number for a specific context that is not in the subbroker
		PhoneDTO newPhone = this.testSubBrokerBean.getLinesOfBusiness().get(LineOfBusinessEnum.PERSONAL_LINE.getCode())
							  .getApplications().get(ApplicationIdEnum.AUTO_QUICKQUOTE.getCode())
							  .getPhones().get(PhoneSectionCodeEnum.OFFER.getCode());
		String testBrokerNbr = "5146667852";
		String testIntactNbr = "6665421478";
		newPhone.setBrokerPhone(testBrokerNbr);
		newPhone.setIntactPhone(testIntactNbr);

		// Execute the tested method
		this.adapter.saveSubBroker((SubBrokers)this.testSubBroker, (SubBrokerDTO)this.testSubBrokerBean);

		// Validate the results
		assertEquals(2, this.testSubBroker.getContextualPhoneNumbers().size());
		IContextualPhoneNumbers resultIntactPhone =
				this.testSubBroker.getPhone(ApplicationIdEnum.AUTO_QUICKQUOTE, LineOfBusinessEnum.PERSONAL_LINE, ApplicationOriginEnum.WINI, null, PhoneNumberUsageEnum.BUSINESS_PHONE);
		IContextualPhoneNumbers resultBrokerPhone =
				this.testSubBroker.getPhone(ApplicationIdEnum.AUTO_QUICKQUOTE, LineOfBusinessEnum.PERSONAL_LINE, ApplicationOriginEnum.CNT, null, PhoneNumberUsageEnum.BUSINESS_PHONE);
		assertEquals(testIntactNbr.substring(0,3), resultIntactPhone.getAreaCode());
		assertEquals(testIntactNbr.substring(3), resultIntactPhone.getPhoneNbr());
		assertEquals(CURRENT_USER, resultIntactPhone.getCreatedByUserID());
		assertEquals(testBrokerNbr.substring(0,3), resultBrokerPhone.getAreaCode());
		assertEquals(testBrokerNbr.substring(3), resultBrokerPhone.getPhoneNbr());
		assertEquals(CURRENT_USER, resultBrokerPhone.getCreatedByUserID());
	}

  @Test
  public void test_fillPhoneBundle_addNew() throws Exception {
    // Add to the bean a new phone number for a specific context that is not in the subbroker
    PhoneDTO newPhone = this.testSubBrokerBean.getLinesOfBusiness().get(LineOfBusinessEnum.PERSONAL_LINE.getCode())
        .getApplications().get(ApplicationIdEnum.WEB_QUOTE.getCode())
        .getPhones().get(PhoneSectionCodeEnum.DEFAULT.getCode());
    String testNbr = "5146667852";
    newPhone.setPhone(testNbr);

    // Execute the tested method
    this.adapter.saveSubBroker((SubBrokers)this.testSubBroker, (SubBrokerDTO)this.testSubBrokerBean);

    // Validate the results
    assertEquals(1, this.testSubBroker.getContextualPhoneNumbers().size());
    IContextualPhoneNumbers resultPhone =
        this.testSubBroker.getPhone(ApplicationIdEnum.WEB_QUOTE, LineOfBusinessEnum.PERSONAL_LINE, null, null, PhoneNumberUsageEnum.BUSINESS_PHONE);
     assertEquals(testNbr.substring(0,3), resultPhone.getAreaCode());
    assertEquals(testNbr.substring(3), resultPhone.getPhoneNbr());
    assertEquals(CURRENT_USER, resultPhone.getCreatedByUserID());
  }

	/**
	 * Test for
	 * {@link SubBrokerAdapter#fillPhone(IContextualPhoneNumbers, ISubBrokers,
	 * 					PhoneDTO, LineOfBusinessEnum, ApplicationIdEnum, String)}
	 * Case when an phone number to be updated.
	 * @throws Exception
	 */
	@Test
	public void test_fillPhone_update() throws Exception {
		// Set old phone numbers in the subbroker
		IContextualPhoneNumbers oldBrokerPhone = new ContextualPhoneNumbers();
		oldBrokerPhone.setAreaCode("111");
		oldBrokerPhone.setPhoneNbr("1111111");
		oldBrokerPhone.setApplicationId(ApplicationIdEnum.AUTO_QUICKQUOTE);
		oldBrokerPhone.setLineOfBusinessCode(LineOfBusinessEnum.PERSONAL_LINE);
		oldBrokerPhone.setApplicationOriginCode(ApplicationOriginEnum.CNT);
		oldBrokerPhone.setUsageCode(PhoneNumberUsageEnum.BUSINESS_PHONE);
		oldBrokerPhone.setFromBrokerFile("N");

		IContextualPhoneNumbers oldIntactPhone = new ContextualPhoneNumbers();
		oldIntactPhone.setAreaCode("111");
		oldIntactPhone.setPhoneNbr("1111111");
		oldIntactPhone.setApplicationId(ApplicationIdEnum.AUTO_QUICKQUOTE);
		oldIntactPhone.setLineOfBusinessCode(LineOfBusinessEnum.PERSONAL_LINE);
		oldIntactPhone.setApplicationOriginCode(ApplicationOriginEnum.WINI);
		oldIntactPhone.setUsageCode(PhoneNumberUsageEnum.BUSINESS_PHONE);
		oldIntactPhone.setFromBrokerFile("N");

		this.testSubBroker.getContextualPhoneNumbers().add(oldBrokerPhone);
		this.testSubBroker.getContextualPhoneNumbers().add(oldIntactPhone);


		// Add to the bean a new phone number for the same context as the old values
		PhoneDTO newPhone = this.testSubBrokerBean.getLinesOfBusiness().get(LineOfBusinessEnum.PERSONAL_LINE.getCode())
							  .getApplications().get(ApplicationIdEnum.AUTO_QUICKQUOTE.getCode())
							  .getPhones().get(PhoneSectionCodeEnum.OFFER.getCode());
		newPhone.setBrokerPhone("9999999999");
		newPhone.setIntactPhone("9999999999");

		// Execute the tested method
		this.adapter.saveSubBroker((SubBrokers)this.testSubBroker, (SubBrokerDTO)this.testSubBrokerBean);

		IContextualPhoneNumbers resultIntactPhone =
				this.testSubBroker.getPhone(ApplicationIdEnum.AUTO_QUICKQUOTE, LineOfBusinessEnum.PERSONAL_LINE, ApplicationOriginEnum.WINI, null, PhoneNumberUsageEnum.BUSINESS_PHONE);
		IContextualPhoneNumbers resultBrokerPhone =
				this.testSubBroker.getPhone(ApplicationIdEnum.AUTO_QUICKQUOTE, LineOfBusinessEnum.PERSONAL_LINE, ApplicationOriginEnum.CNT, null, PhoneNumberUsageEnum.BUSINESS_PHONE);

		// Validate the results
		assertEquals(4, this.testSubBroker.getContextualPhoneNumbers().size()); // There should be 2 new phones
		assertNotNull(oldBrokerPhone.getEndDate(), "Old broker phone should now have an end date.");
		assertNotNull(oldIntactPhone.getEndDate(), "Old intact phone should now have an end date.");
		assertEquals(CURRENT_USER, resultIntactPhone.getCreatedByUserID());
		assertEquals(CURRENT_USER, resultBrokerPhone.getCreatedByUserID());
	}

  @Test
  public void test_fillPhoneBundle_update() throws Exception {
    // Set old phone numbers in the subbroker
    IContextualPhoneNumbers oldPhone = new ContextualPhoneNumbers();
    oldPhone.setAreaCode("111");
    oldPhone.setPhoneNbr("1111111");
    oldPhone.setApplicationId(ApplicationIdEnum.WEB_QUOTE);
    oldPhone.setLineOfBusinessCode(LineOfBusinessEnum.PERSONAL_LINE);
    oldPhone.setApplicationOriginCode(null);
    oldPhone.setUsageCode(PhoneNumberUsageEnum.BUSINESS_PHONE);
    oldPhone.setFromBrokerFile("N");

    this.testSubBroker.getContextualPhoneNumbers().add(oldPhone);

    // Add to the bean a new phone number for the same context as the old values
    PhoneDTO newPhone = this.testSubBrokerBean.getLinesOfBusiness().get(LineOfBusinessEnum.PERSONAL_LINE.getCode())
        .getApplications().get(ApplicationIdEnum.WEB_QUOTE.getCode())
        .getPhones().get(PhoneSectionCodeEnum.DEFAULT.getCode());
    newPhone.setPhone("9999999999");

    // Execute the tested method
    this.adapter.saveSubBroker((SubBrokers)this.testSubBroker, (SubBrokerDTO)this.testSubBrokerBean);

    IContextualPhoneNumbers resultPhone =
        this.testSubBroker.getPhone(ApplicationIdEnum.WEB_QUOTE, LineOfBusinessEnum.PERSONAL_LINE, null, null, PhoneNumberUsageEnum.BUSINESS_PHONE);

    // Validate the results
    assertEquals(2, this.testSubBroker.getContextualPhoneNumbers().size()); // There should be 2 new phones
    assertNotNull(oldPhone.getEndDate(), "Old broker phone should now have an end date.");
    assertEquals(CURRENT_USER, resultPhone.getCreatedByUserID());
  }

	/**
	 * Test for
	 * {@link SubBrokerAdapter#fillPhone(IContextualPhoneNumbers, ISubBrokers,
	 * 					PhoneDTO, LineOfBusinessEnum, ApplicationIdEnum, String)}
	 * Case when an phone number to be removed (end date set).
	 * @throws Exception
	 */
	@Test
	public void test_fillPhone_remove() throws Exception {
		// Set old phone numbers in the subbroker
		IContextualPhoneNumbers oldBrokerPhone = new ContextualPhoneNumbers();
		oldBrokerPhone.setAreaCode("111");
		oldBrokerPhone.setPhoneNbr("1111111");
		oldBrokerPhone.setApplicationId(ApplicationIdEnum.AUTO_QUICKQUOTE);
		oldBrokerPhone.setLineOfBusinessCode(LineOfBusinessEnum.PERSONAL_LINE);
		oldBrokerPhone.setApplicationOriginCode(ApplicationOriginEnum.CNT);
		oldBrokerPhone.setUsageCode(PhoneNumberUsageEnum.BUSINESS_PHONE);
		oldBrokerPhone.setFromBrokerFile("N");

		IContextualPhoneNumbers oldIntactPhone = new ContextualPhoneNumbers();
		oldIntactPhone.setAreaCode("111");
		oldIntactPhone.setPhoneNbr("1111111");
		oldIntactPhone.setApplicationId(ApplicationIdEnum.AUTO_QUICKQUOTE);
		oldIntactPhone.setLineOfBusinessCode(LineOfBusinessEnum.PERSONAL_LINE);
		oldIntactPhone.setApplicationOriginCode(ApplicationOriginEnum.WINI);
		oldIntactPhone.setUsageCode(PhoneNumberUsageEnum.BUSINESS_PHONE);
		oldIntactPhone.setFromBrokerFile("N");

		this.testSubBroker.getContextualPhoneNumbers().add(oldBrokerPhone);
		this.testSubBroker.getContextualPhoneNumbers().add(oldIntactPhone);

		// Set new values to be empty so that they are removed
		PhoneDTO newPhone = this.testSubBrokerBean.getLinesOfBusiness().get(LineOfBusinessEnum.PERSONAL_LINE.getCode())
				.getApplications().get(ApplicationIdEnum.AUTO_QUICKQUOTE.getCode())
				.getPhones().get(PhoneSectionCodeEnum.OFFER.getCode());
		newPhone.setBrokerPhone("");
		newPhone.setIntactPhone("");

		// Execute the tested method
		this.adapter.saveSubBroker((SubBrokers)this.testSubBroker, (SubBrokerDTO)this.testSubBrokerBean);

		// Validate the results
		assertEquals(2, this.testSubBroker.getContextualPhoneNumbers().size()); // There should be no new phones
		assertNotNull(oldBrokerPhone.getEndDate(), "Old broker phone should now have an end date.");
		assertNotNull(oldIntactPhone.getEndDate(), "Old intact phone should now have an end date.");
	}

  @Test
  public void test_fillPhoneBundle_remove() throws Exception {
    // Set old phone numbers in the subbroker
    IContextualPhoneNumbers oldPhone = new ContextualPhoneNumbers();
    oldPhone.setAreaCode("111");
    oldPhone.setPhoneNbr("1111111");
    oldPhone.setApplicationId(ApplicationIdEnum.WEB_QUOTE);
    oldPhone.setLineOfBusinessCode(LineOfBusinessEnum.PERSONAL_LINE);
    oldPhone.setApplicationOriginCode(null);
    oldPhone.setUsageCode(PhoneNumberUsageEnum.BUSINESS_PHONE);
    oldPhone.setFromBrokerFile("N");

    this.testSubBroker.getContextualPhoneNumbers().add(oldPhone);

    // Set new values to be empty so that they are removed
    PhoneDTO newPhone = this.testSubBrokerBean.getLinesOfBusiness().get(LineOfBusinessEnum.PERSONAL_LINE.getCode())
        .getApplications().get(ApplicationIdEnum.WEB_QUOTE.getCode())
        .getPhones().get(PhoneSectionCodeEnum.DEFAULT.getCode());
    newPhone.setPhone("");

    // Execute the tested method
    this.adapter.saveSubBroker((SubBrokers)this.testSubBroker, (SubBrokerDTO)this.testSubBrokerBean);

    // Validate the results
    assertEquals(1, this.testSubBroker.getContextualPhoneNumbers().size()); // There should be no new phones
    assertNotNull(oldPhone.getEndDate(), "Old broker phone should now have an end date.");
  }


	/**
	 * Test for
	 * {@link SubBrokerAdapter#saveImages(ISubBrokers, SubBrokerDTO)}
	 * Case when a new image is added.
	 * @throws Exception
	 */
	@Test
	public void test_saveImages_addNew() throws Exception {
		// Add to the bean a new FR and EN images for a specific context that is not in the subbroker
		JSFImageDTO newImageFR = (JSFImageDTO) this.testSubBrokerBean.getImages().get(ImageContextEnum.LOGO_FR_OTHER_UI.getCode());
		JSFImageDTO newImageEN = (JSFImageDTO) this.testSubBrokerBean.getImages().get(ImageContextEnum.LOGO_EN_OTHER_UI.getCode());
		UploadedFile mockImageFile = mock(UploadedFile.class);
		newImageFR.setImage(mockImageFile);
		newImageEN.setImage(mockImageFile);

		// Stubs setup
		when(mockImageFile.getContentType()).thenReturn("png");

		// Execute the tested method
		this.adapter.saveSubBroker((SubBrokers)this.testSubBroker, (SubBrokerDTO)this.testSubBrokerBean);

		// Validate the results
		assertEquals(2, this.testSubBroker.getBrandVisuals().size());
		BrandVisuals resultImageFR = (BrandVisuals) this.testSubBroker.getImage(ImageUsageEnum.UI, ImageNatureEnum.LOGO, LanguageEnum.FRENCH);
		BrandVisuals resultImageEN = (BrandVisuals) this.testSubBroker.getImage(ImageUsageEnum.UI, ImageNatureEnum.LOGO, LanguageEnum.ENGLISH);
		assertNotNull(resultImageFR, "The resulting FR image should not be null");
		assertNotNull(resultImageEN, "The resulting EN image should not be null");
		assertNull(resultImageFR.getGraphic(), "The graphics in the FR image should be null (no actual image)");
		assertNull(resultImageEN.getGraphic(), "The graphics in the EN image should be null (no actual image)");
		assertEquals(CURRENT_USER, resultImageFR.getCreatedByUserID());
		assertEquals(CURRENT_USER, resultImageEN.getCreatedByUserID());
	}

	/**
	 * Test for
	 * {@link SubBrokerAdapter#saveImages(ISubBrokers, SubBrokerDTO)}
	 * Case when an image is updated.
	 * @throws Exception
	 */
	@Test
	public void test_saveImages_update() throws Exception {
		// Set old images in the subbroker
		BrandVisuals oldVisualsFR = new BrandVisuals();
		oldVisualsFR.setLanguageCode(LanguageEnum.FRENCH);
		oldVisualsFR.setMimeTypeCode(ImageMimeTypeEnum.PNG);
		oldVisualsFR.setUsageCode(ImageUsageEnum.UI);
		oldVisualsFR.setNatureCode(ImageNatureEnum.LOGO);

		BrandVisuals oldVisualsEN = new BrandVisuals();
		oldVisualsEN.setLanguageCode(LanguageEnum.ENGLISH);
		oldVisualsEN.setMimeTypeCode(ImageMimeTypeEnum.PNG);
		oldVisualsEN.setUsageCode(ImageUsageEnum.UI);
		oldVisualsEN.setNatureCode(ImageNatureEnum.LOGO);

		this.testSubBroker.getBrandVisuals().add(oldVisualsFR);
		this.testSubBroker.getBrandVisuals().add(oldVisualsEN);

		// Add to the bean a new FR and EN images for the same context as the old values
		JSFImageDTO newImageFR = (JSFImageDTO) this.testSubBrokerBean.getImages().get(ImageContextEnum.LOGO_FR_OTHER_UI.getCode());
		JSFImageDTO newImageEN = (JSFImageDTO) this.testSubBrokerBean.getImages().get(ImageContextEnum.LOGO_EN_OTHER_UI.getCode());
		UploadedFile mockImageFile = mock(UploadedFile.class);
		newImageFR.setImage(mockImageFile);
		newImageEN.setImage(mockImageFile);

		// Stubs setup
		when(mockImageFile.getContentType()).thenReturn("png");

		// Execute the tested method
		this.adapter.saveSubBroker((SubBrokers)this.testSubBroker, (SubBrokerDTO)this.testSubBrokerBean);

		BrandVisuals resultImageFR = (BrandVisuals) this.testSubBroker.getImage(ImageUsageEnum.UI, ImageNatureEnum.LOGO, LanguageEnum.FRENCH);
		BrandVisuals resultImageEN = (BrandVisuals) this.testSubBroker.getImage(ImageUsageEnum.UI, ImageNatureEnum.LOGO, LanguageEnum.ENGLISH);

		// Validate the results
		assertEquals(4, this.testSubBroker.getBrandVisuals().size()); // There should be 2 new images
		assertNotNull(oldVisualsFR.getEndDate(), "Old FR image should now have an end date.");
		assertNotNull(oldVisualsEN.getEndDate(), "Old EN image should now have an end date.");
		assertEquals(CURRENT_USER, resultImageFR.getCreatedByUserID());
		assertEquals(CURRENT_USER, resultImageEN.getCreatedByUserID());
	}

	/**
	 * Test for
	 * {@link SubBrokerAdapter#saveImages(ISubBrokers, SubBrokerDTO)}
	 * Case when an image is removed.
	 * @throws Exception
	 */
	@Test
	public void test_saveImages_remove() throws Exception {
		// Set old images in the subbroker
		BrandVisuals oldVisualsFR = new BrandVisuals();
		oldVisualsFR.setLanguageCode(LanguageEnum.FRENCH);
		oldVisualsFR.setMimeTypeCode(ImageMimeTypeEnum.PNG);
		oldVisualsFR.setUsageCode(ImageUsageEnum.UI);
		oldVisualsFR.setNatureCode(ImageNatureEnum.LOGO);

		BrandVisuals oldVisualsEN = new BrandVisuals();
		oldVisualsEN.setLanguageCode(LanguageEnum.ENGLISH);
		oldVisualsEN.setMimeTypeCode(ImageMimeTypeEnum.PNG);
		oldVisualsEN.setUsageCode(ImageUsageEnum.UI);
		oldVisualsEN.setNatureCode(ImageNatureEnum.LOGO);

		this.testSubBroker.getBrandVisuals().add(oldVisualsFR);
		this.testSubBroker.getBrandVisuals().add(oldVisualsEN);

		// Execute the tested method
		this.adapter.saveSubBroker((SubBrokers)this.testSubBroker, (SubBrokerDTO)this.testSubBrokerBean);

		// Validate the results
		assertEquals(2, this.testSubBroker.getBrandVisuals().size()); // There should be 2 images only
		assertNotNull(oldVisualsFR.getEndDate(), "Old FR image should now have an end date.");
		assertNotNull(oldVisualsEN.getEndDate(), "Old EN image should now have an end date.");
	}

	/**
	 * Test for
	 * {@link SubBrokerAdapter#saveBusinessHours(ISubBrokers, SubBrokerDTO)}
	 * Case when a business hour is updated.
	 * @throws Exception
	 */
	@Test
	public void test_saveHours_update() throws Exception {
		// Set old hours in the subbroker
		LocatorTimeRanges oldTimeRange = new LocatorTimeRanges();
		oldTimeRange.setHoursFrom("05:30");
		oldTimeRange.setHoursTo("17:30");
		oldTimeRange.setWeekDayFromCode(WeekDayCodeEnum.MONDAY);
		oldTimeRange.setWeekDayToCode(WeekDayCodeEnum.MONDAY);

		this.testSubBroker.getLocatorTimeRanges().add(oldTimeRange);

		// Add to the bean an updated time range (different times)
		BusinessHourDTO newHours = new BusinessHourDTO();
		newHours.setOpenHour("07:00");
		newHours.setCloseHour("19:00");
		newHours.setDayOfWeek(1);
		this.testSubBrokerBean.getBusinessHours().put("1", newHours);

		// Execute the tested method
		this.adapter.saveSubBroker((SubBrokers)this.testSubBroker, (SubBrokerDTO)this.testSubBrokerBean);

		ILocatorTimeRange resultTime = this.testSubBroker.getBusinessHours(WeekDayCodeEnum.MONDAY, WeekDayCodeEnum.MONDAY);

		// Validate the results
		assertEquals(2, this.testSubBroker.getLocatorTimeRanges().size());
		assertNotNull(oldTimeRange.getEndDate(), "Old time range should now have an end date.");
		assertEquals(CURRENT_USER, resultTime.getCreatedByUserID());
	}

	/**
	 * Test for
	 * {@link SubBrokerAdapter#saveAccessTypes(ISubBrokers, SubBrokerDTO)}
	 * Case when a general info is updated.
	 * @throws Exception
	 */
	@Test
	public void test_saveAccessTypes_update() throws Exception {
		// Set old general infos in the subbroker
		IContextualSubBrokerGnInfos oldInfos = this.testSubBroker.getAccessType(ApplicationIdEnum.AUTO_QUICKQUOTE, LineOfBusinessEnum.PERSONAL_LINE);
		oldInfos.setWebAccessType("NONE");
		oldInfos.setAssignableInd("Y");
		this.testSubBroker.getContextualSubBrokerGnInfos().add(oldInfos);

		// Add to the bean an updated web access type and assignable flag
		ApplicationDTO currentApp = this.testSubBrokerBean.getLinesOfBusiness().get(LineOfBusinessEnum.PERSONAL_LINE.getCode())
														  .getApplications().get(ApplicationIdEnum.AUTO_QUICKQUOTE.getCode());
		currentApp.setAccessType("QIB");
		currentApp.setAssignable(true);

		// Execute the tested method
		this.adapter.saveSubBroker((SubBrokers)this.testSubBroker, (SubBrokerDTO)this.testSubBrokerBean);

		IContextualSubBrokerGnInfos resultAccess = this.testSubBroker.getAccessType(ApplicationIdEnum.AUTO_QUICKQUOTE, LineOfBusinessEnum.PERSONAL_LINE);

		// Validate the results
		assertEquals(6, this.testSubBroker.getContextualSubBrokerGnInfos().size()); // Size should be 6 : 5 contexts + 1 new value
		assertNotNull(oldInfos.getEndDate(), "Old general info should now have an end date.");
		assertEquals(CURRENT_USER, resultAccess.getCreatedByUserID());
	}

	/**
	 * Private method used to create a general SubBroker that can be used for most tests.
	 */
	private void createGeneralSubBroker(){
		this.testSubBroker = new SubBrokers();

		// Setup contextual infos
		Set<IContextualElectronicLocators> contextualElectronicLocators = new HashSet<IContextualElectronicLocators>();
		Set<IContextualPhoneNumbers> contextualPhoneNumbers = new HashSet<IContextualPhoneNumbers>();
		Set<IContextualSubBrokerGnInfos> contextualSubBrokerGnInfos = new HashSet<IContextualSubBrokerGnInfos>();
		Set<BrandVisuals> brandVisuals = new HashSet<BrandVisuals>();
		Set<LocatorTimeRanges> timeRanges = new HashSet<LocatorTimeRanges>();

		this.testSubBroker.setContextualElectronicLocators(contextualElectronicLocators);
		this.testSubBroker.setContextualPhoneNumbers(contextualPhoneNumbers);
		this.testSubBroker.setContextualSubBrokerGnInfos(contextualSubBrokerGnInfos);
		this.testSubBroker.setBrandVisuals(brandVisuals);
		this.testSubBroker.setLocatorTimeRanges(timeRanges);

		/* Setup access types (give most access by default) */
		// Auto Regular Quote
		this.testAQAccessType = new ContextualSubBrokerGnInfos();
		this.testAQAccessType.setApplicationId(ApplicationIdEnum.AUTO_REGULAR_QUOTE);
		this.testAQAccessType.setLineOfBusinessCode(LineOfBusinessEnum.PERSONAL_LINE);
		this.testAQAccessType.setAssignableInd("Y");
		this.testAQAccessType.setWebAccessType("QIB");
		contextualSubBrokerGnInfos.add(this.testAQAccessType);

		// Auto Quick Quote
		this.testQQAccessType = new ContextualSubBrokerGnInfos();
		this.testQQAccessType.setApplicationId(ApplicationIdEnum.AUTO_QUICKQUOTE);
		this.testQQAccessType.setLineOfBusinessCode(LineOfBusinessEnum.PERSONAL_LINE);
		this.testQQAccessType.setAssignableInd("Y");
		this.testQQAccessType.setWebAccessType("QIB");
		contextualSubBrokerGnInfos.add(this.testQQAccessType);

		// IRCA Quick Quote
		this.testIRCAQQAccessType = new ContextualSubBrokerGnInfos();
		this.testIRCAQQAccessType.setApplicationId(ApplicationIdEnum.AUTO_QUICKQUOTE);
		this.testIRCAQQAccessType.setLineOfBusinessCode(LineOfBusinessEnum.COMMERCIAL_LINE);
		this.testIRCAQQAccessType.setAssignableInd("Y");
		this.testIRCAQQAccessType.setWebAccessType("QIB");
		contextualSubBrokerGnInfos.add(this.testIRCAQQAccessType);

		// IRCA Quick Quote
		this.testPCQQAccessType = new ContextualSubBrokerGnInfos();
		this.testPCQQAccessType.setApplicationId(ApplicationIdEnum.COMMERCIAL_CLIENT_QUICKQUOTE);
		this.testPCQQAccessType.setLineOfBusinessCode(LineOfBusinessEnum.COMMERCIAL_LINE);
		this.testPCQQAccessType.setAssignableInd("Y");
		this.testPCQQAccessType.setWebAccessType("QIB");
		contextualSubBrokerGnInfos.add(this.testPCQQAccessType);
	}

	/**
	 * Private method used to create a general SubBrokerBean that can be used for most tests.
	 */
	private void createGeneralSubBrokerBean(){
		this.testSubBrokerBean = new SubBrokerDTO();

		/* Lines of business setup (set to most access by default) */
		Map<String,LineOfBusinessDTO> testLinesOfBusiness = new HashMap<String,LineOfBusinessDTO>();

		/* Personal Lines*/
		LineOfBusinessDTO lineOfBusinessPLDTO = new LineOfBusinessDTO();
		Map<String, ApplicationDTO> testPLApplications = new HashMap<String, ApplicationDTO>();
		ApplicationDTO applicationInfoAQ = new ApplicationDTO();
		ApplicationDTO applicationInfoQQ = new ApplicationDTO();
		ApplicationDTO applicationInfoRQQ = new ApplicationDTO();
		Map<String, PhoneDTO> phonesAQ = new HashMap<String, PhoneDTO>();
		Map<String, PhoneDTO> phonesQQ = new HashMap<String, PhoneDTO>();
		Map<String, PhoneDTO> phonesRQQ = new HashMap<String, PhoneDTO>();

		// Auto Regular Quote
		applicationInfoAQ.setAccessType("QIB");
		applicationInfoAQ.setId(ApplicationIdEnum.AUTO_REGULAR_QUOTE.getCode());
		applicationInfoAQ.setAssignable(true);
		phonesAQ.put(PhoneSectionCodeEnum.DEFAULT.getCode(), new PhoneDTO());
		applicationInfoAQ.setPhones(phonesAQ);
		testPLApplications.put(ApplicationIdEnum.AUTO_REGULAR_QUOTE.getCode(), applicationInfoAQ);

		// Auto Quick Quote
		applicationInfoQQ.setAccessType("QIB");
		applicationInfoQQ.setId(ApplicationIdEnum.AUTO_QUICKQUOTE.getCode());
		applicationInfoQQ.setAssignable(true);
		phonesQQ.put(PhoneSectionCodeEnum.OFFER.getCode(), new PhoneDTO());
		applicationInfoQQ.setPhones(phonesQQ);
		testPLApplications.put(ApplicationIdEnum.AUTO_QUICKQUOTE.getCode(), applicationInfoQQ);

		// Home Quick Quote
		applicationInfoRQQ.setAccessType("QIB");
		applicationInfoRQQ.setId(ApplicationIdEnum.WEB_QUOTE.getCode());
		applicationInfoRQQ.setAssignable(true);
    phonesRQQ.put(PhoneSectionCodeEnum.DEFAULT.getCode(), new PhoneDTO());
		applicationInfoRQQ.setPhones(phonesRQQ);
		testPLApplications.put(ApplicationIdEnum.WEB_QUOTE.getCode(), applicationInfoRQQ);

		lineOfBusinessPLDTO.setApplications(testPLApplications);
		lineOfBusinessPLDTO.setLineOfBusiness(LineOfBusinessEnum.PERSONAL_LINE.getCode());
		lineOfBusinessPLDTO.setContact(new ElectronicContactDTO());
		testLinesOfBusiness.put(LineOfBusinessEnum.PERSONAL_LINE.getCode(), lineOfBusinessPLDTO);

		/* Commercial Lines */
		LineOfBusinessDTO lineOfBusinessCLDTO = new LineOfBusinessDTO();
		Map<String, ApplicationDTO> testCLApplications = new HashMap<String, ApplicationDTO>();
		ApplicationDTO applicationInfoIRCAQQ = new ApplicationDTO();
		Map<String, PhoneDTO> phonesIRCAQQ = new HashMap<String, PhoneDTO>();

		// IRCA Quick Quote
		applicationInfoIRCAQQ.setAccessType("QIB");
		applicationInfoIRCAQQ.setId(ApplicationIdEnum.AUTO_QUICKQUOTE.getCode());
		applicationInfoIRCAQQ.setAssignable(true);
		applicationInfoIRCAQQ.setPhones(phonesIRCAQQ);
		testCLApplications.put(ApplicationIdEnum.AUTO_QUICKQUOTE.getCode(), applicationInfoIRCAQQ);

		// Commercial Property Quick Quote
		applicationInfoIRCAQQ.setAccessType("QIB");
		applicationInfoIRCAQQ.setId(ApplicationIdEnum.COMMERCIAL_CLIENT_QUICKQUOTE.getCode());
		applicationInfoIRCAQQ.setAssignable(true);
		applicationInfoIRCAQQ.setPhones(phonesIRCAQQ);
		testCLApplications.put(ApplicationIdEnum.COMMERCIAL_CLIENT_QUICKQUOTE.getCode(), applicationInfoIRCAQQ);

		lineOfBusinessCLDTO.setApplications(testCLApplications);
		lineOfBusinessCLDTO.setLineOfBusiness(LineOfBusinessEnum.COMMERCIAL_LINE.getCode());
		lineOfBusinessCLDTO.setContact(new ElectronicContactDTO());
		testLinesOfBusiness.put(LineOfBusinessEnum.COMMERCIAL_LINE.getCode(), lineOfBusinessCLDTO);

		this.testSubBrokerBean.setLinesOfBusiness(testLinesOfBusiness);

		/* Services */
		Map<String, ServiceDTO> services = new HashMap<String, ServiceDTO>();

		// Client Centre
		ServiceDTO serviceCLCT = new ServiceDTO();
		ApplicationDTO applicationInfoCLCT = new ApplicationDTO();
		Map<String, PhoneDTO> phonesCLCT = new HashMap<String, PhoneDTO>();
		phonesCLCT.put(PhoneSectionCodeEnum.DEFAULT.getCode(), new PhoneDTO());
		applicationInfoCLCT.setId(ApplicationIdEnum.CLIENT_CENTRE.getCode());
		applicationInfoCLCT.setPhones(phonesCLCT);
		serviceCLCT.setApplication(applicationInfoCLCT);
		serviceCLCT.setContact(new ElectronicContactDTO());
		services.put(ApplicationIdEnum.CLIENT_CENTRE.getCode() + LineOfBusinessEnum.PERSONAL_LINE.getCode(), serviceCLCT);

		this.testSubBrokerBean.setServices(services);

		/* Images */
		Map<String, ImageDTO> images = new HashMap<String, ImageDTO>();
		JSFImageDTO imageFR = new JSFImageDTO();
		imageFR.setContext("LOGO-FR--UI");
		images.put(ImageContextEnum.LOGO_FR_OTHER_UI.getCode(), imageFR);

		JSFImageDTO imageEN = new JSFImageDTO();
		imageEN.setContext("LOGO-EN--UI");
		images.put(ImageContextEnum.LOGO_EN_OTHER_UI.getCode(), imageEN);
		this.testSubBrokerBean.setImages(images);
	}

}
