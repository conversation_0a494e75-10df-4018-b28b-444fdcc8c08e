package com.intact.brokeroffice.controller.tabs;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.test.util.ReflectionTestUtils;

import com.intact.brokeroffice.controller.province.ProvinceController;

public class AccountsTabControllerTest {
	
	private AccountsTabController accountsTabController = new AccountsTabController();
	
	private BrokersTabController brokersTabController;

	private ProvinceController provinceController;

	@BeforeEach
	public void setUp() throws Exception {
		brokersTabController = mock(BrokersTabController.class);
		provinceController = mock(ProvinceController.class);
		
		ReflectionTestUtils.setField(accountsTabController, "brokersTabController", brokersTabController);
		ReflectionTestUtils.setField(accountsTabController, "provinceController", provinceController);
	}

	@AfterEach
	public void tearDown() throws Exception {
		brokersTabController = null;
		provinceController = null;
	}

	@Test
	public void testGetPagePathON(){
		when(provinceController.getExtension()).thenReturn("ON");
		accountsTabController.list();
		assertEquals("/pages/brokers/account/listON.xhtml", accountsTabController.getPagePath());
		
		accountsTabController.modify();
		assertEquals("/pages/brokers/account/modify.xhtml", accountsTabController.getPagePath());
	}
	
	@Test
	public void testGetPagePathOTHER(){
		when(provinceController.getExtension()).thenReturn("QC");
		accountsTabController.list();
		assertEquals("/pages/brokers/account/list.xhtml", accountsTabController.getPagePath());
		
		accountsTabController.modify();
		assertEquals("/pages/brokers/account/modify.xhtml", accountsTabController.getPagePath());
	}
}
