/**
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance 
 * Copyright (c) 2009, Intact Insurance, All rights reserved.<br>
 */
package com.intact.brokeroffice.controller.report.performancemetric;

import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.intact.brokeroffice.controller.performancemetrics.PerformanceMetricsValidator;



/**
 * <AUTHOR>
 *
 */
public class PerformanceMetricValidatorTest {
		
	private PerformanceMetricsValidator  performanceMetricsValidator;	

	@BeforeEach
	public void setup(){		
		this.performanceMetricsValidator = new PerformanceMetricsValidator();	
		this.performanceMetricsValidator.setShowErrorMessage(false);		
	}
	
	@Test
	public void testPeriodDates(){		
				
		//the period From date cannot be in future  ---> TEST MUST FAIL	
		Calendar fromCalendar =  Calendar.getInstance();
		fromCalendar.setTime(new Date());
		fromCalendar.add(Calendar.DATE, 10);		
		Assertions.assertFalse(this.performanceMetricsValidator.validate(fromCalendar.getTime(), new Date()));
			
		//The period To date cannot be in future  ---> TEST MUST FAIL			
		Assertions.assertFalse(this.performanceMetricsValidator.validate(new Date(), fromCalendar.getTime()));
		
		//The period From date must be less than or equal to the period To date  ---> TEST MUST FAIL
		fromCalendar =  Calendar.getInstance();
		fromCalendar =  new GregorianCalendar();
		fromCalendar.set(2010, 4, 1);
		
		Calendar toCalendar =  Calendar.getInstance();		
		toCalendar.set(2010, 0, 1);			
		Assertions.assertFalse(this.performanceMetricsValidator.validate(fromCalendar.getTime(), toCalendar.getTime()));
					
		//TEST SUCESSFULL NOW
		toCalendar =  new GregorianCalendar();		
		toCalendar.set(2011, 0 , 1);			
		Assertions.assertTrue(this.performanceMetricsValidator.validate(fromCalendar.getTime(), toCalendar.getTime()));
	}
	
	
	
	/**
	 * Utility method need to run this test case in Maven 1.
	 * 
	 * @return Test suite
	 */
//	public static junit.framework.Test suite() {
//		return new JUnit4TestAdapter(PerformanceMetricValidatorTest.class);
//	}

}
