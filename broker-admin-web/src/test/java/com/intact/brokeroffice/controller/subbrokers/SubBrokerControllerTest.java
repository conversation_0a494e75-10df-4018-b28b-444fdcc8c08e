package com.intact.brokeroffice.controller.subbrokers;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.*;
import org.springframework.test.util.ReflectionTestUtils;

import com.ing.canada.cif.domain.IContextualElectronicLocators;
import com.ing.canada.cif.domain.IContextualPhoneNumbers;
import com.ing.canada.cif.domain.IContextualSubBrokerGnInfos;
import com.ing.canada.cif.domain.ISubBrokers;
import com.ing.canada.cif.domain.enums.ApplicationIdEnum;
import com.ing.canada.cif.domain.enums.LineOfBusinessEnum;
import com.ing.canada.cif.domain.impl.ContextualSubBrokerGnInfos;
import com.ing.canada.cif.domain.impl.SubBrokers;
import com.intact.brokeroffice.business.dto.ApplicationDTO;
import com.intact.brokeroffice.business.dto.LineOfBusinessDTO;
import com.intact.brokeroffice.business.dto.SubBrokerDTO;
import com.intact.brokeroffice.business.subbrokers.ISubBrokersBusinessProcess;
import com.intact.brokeroffice.controller.tabs.SubBrokersTabController;
import com.intact.brokeroffice.dao.SystemDAO;
import com.intact.brokeroffice.helper.FaceMessageHelper;

public class SubBrokerControllerTest {
	private MockedStatic<FaceMessageHelper> mockedFaceMessageHelper;
	/** Class to be tested **/
	@InjectMocks
	private SubBrokersController controller = new SubBrokersController();

	/** General test objects **/
	@Mock
	private ISubBrokers testSubBroker;

	private SubBrokerDTO testSubBrokerBean;

	private IContextualSubBrokerGnInfos testAQAccessType;

	private IContextualSubBrokerGnInfos testQQAccessType;

	private IContextualSubBrokerGnInfos testIRCAQQAccessType;

	/** Mocks **/
	@Mock
	private ISubBrokersBusinessProcess mockSubBrokersBusinessProcess;

	@Mock
	private SubBrokerAdapter mockSubBrokerAdapter;

	@Mock
	private SubBrokersTabController mockSubBrokersTabController;

	@Mock
	private SubBrokerValidator mockSubBrokerValidator;

	@Mock
	private SystemDAO mockSystemDAO;

	@BeforeEach
	public void setUp() {
		MockitoAnnotations.openMocks(this);

		mockedFaceMessageHelper = Mockito.mockStatic(FaceMessageHelper.class);
		this.createGeneralSubBroker();
		this.createGeneralSubBrokerBean();


		// Set attributes in the controller by reflection as necessary
		ReflectionTestUtils.setField(this.controller, "subBrokerBean", this.testSubBrokerBean);
		ReflectionTestUtils.setField(this.controller, "subBrokerAdapter", this.mockSubBrokerAdapter);

		// General stubs to be used in every case
		when(this.mockSubBrokersBusinessProcess.getSubBrokerByID(anyLong())).thenReturn(this.testSubBroker);
	}

	@AfterEach
	void tearDownStaticMocks() {
		mockedFaceMessageHelper.closeOnDemand();
	}

	/**
	 * Test for
	 * {@link SubBrokersController#getSubBroker()}
	 * Case where confirmation has been given, so POS unassigned verification is bypassed.
	 * @throws Exception
	 */
	@Test
	public void test_getSubBroker_confirmed() throws Exception {
		// Set confirm to true to bypass unassigned verification
		ReflectionTestUtils.setField(this.controller, "confirm", false);

		// Execute the tested method
		ISubBrokers result = this.controller.getSubBroker();

		// Validate the results
		assertEquals(this.testSubBroker, result);
		verify(this.mockSubBrokerAdapter, times(1)).saveSubBroker((SubBrokers)this.testSubBroker, this.testSubBrokerBean);
	}

	/**
	 * Test for
	 * {@link SubBrokersController#getSubBroker()}
	 * Case for access to go from QI to QB (active to active). Should not verify unassigned status.
	 * @throws Exception
	 */
	@Test
	public void test_getSubBroker_AccessQIToQB() throws Exception {
		// Set from status (subBroker) to QI and to status (bean) to QB for this test
		this.testAQAccessType.setWebAccessType("QI");
		this.testSubBrokerBean.getLinesOfBusiness()
			.get(LineOfBusinessEnum.PERSONAL_LINE.getCode()).getApplications()
			.get(ApplicationIdEnum.AUTO_REGULAR_QUOTE.getCode()).setAccessType("QB");

		// Execute the tested method
		ISubBrokers result = this.controller.getSubBroker();

		// Validate the results
		assertEquals(this.testSubBroker, result);
		verify(this.mockSubBrokersBusinessProcess, never()).verifyPosUnassignedToQuote(anyLong(), any(ApplicationIdEnum.class), any(LineOfBusinessEnum.class));
		verify(this.mockSubBrokerAdapter, times(1)).saveSubBroker((SubBrokers)this.testSubBroker, this.testSubBrokerBean);
	}

	/**
	 * Test for
	 * {@link SubBrokersController#getSubBroker()}
	 * Case for access to go from Inactive to QIB. Should not verify unassigned status.
	 * @throws Exception
	 */
	@Test
	public void test_getSubBroker_AccessInactiveToQIB() throws Exception {
		// Set from status (subBroker) to Intactive and to status (bean) to QIB for this test
		this.testAQAccessType.setWebAccessType(null);
		this.testSubBrokerBean.getLinesOfBusiness()
			.get(LineOfBusinessEnum.PERSONAL_LINE.getCode()).getApplications()
			.get(ApplicationIdEnum.AUTO_REGULAR_QUOTE.getCode()).setAccessType("QIB");

		// Execute the tested method
		ISubBrokers result = this.controller.getSubBroker();

		// Validate the results
		assertEquals(this.testSubBroker, result);
		verify(this.mockSubBrokersBusinessProcess, never()).verifyPosUnassignedToQuote(anyLong(), any(ApplicationIdEnum.class), any(LineOfBusinessEnum.class));
		verify(this.mockSubBrokerAdapter, times(1)).saveSubBroker((SubBrokers)this.testSubBroker, this.testSubBrokerBean);
	}

	/**
	 * Test for
	 * {@link SubBrokersController#getSubBroker()}
	 * Case for access to go from QI to Inactive. Should verify unassigned status.
	 * @throws Exception
	 */
	@Test
	public void test_getSubBroker_AccessQIToInactive() throws Exception {
		// Set from status (subBroker) to QI and to status (bean) to NONE for this test
		this.testAQAccessType.setWebAccessType("QI");
		this.testSubBrokerBean.getLinesOfBusiness()
			.get(LineOfBusinessEnum.PERSONAL_LINE.getCode()).getApplications()
			.get(ApplicationIdEnum.AUTO_REGULAR_QUOTE.getCode()).setAccessType("NONE");

		// Execute the tested method
		ISubBrokers result = this.controller.getSubBroker();

		// Validate the results
		assertNull(result);
		verify(this.mockSubBrokersBusinessProcess, times(1)).verifyPosUnassignedToFsa(anyLong(), any(ApplicationIdEnum.class), any(LineOfBusinessEnum.class));
		verify(this.mockSubBrokersTabController, times(1)).modify();
		verify(this.mockSubBrokerAdapter, never()).saveSubBroker((SubBrokers)this.testSubBroker, this.testSubBrokerBean);
	}

	/**
	 * Test for
	 * {@link SubBrokersController#getSubBroker()}
	 * Case for Auto Regular Quote status to be deactivated and have assigned fsa/quote.
	 * @throws Exception
	 */
	@Test
	public void test_getSubBroker_AQRGDeactivate() throws Exception {
		// Set status change from active to inactive for Auto Regular Quote to trigger verify of unassigned
		this.testAQAccessType.setWebAccessType("QI");
		this.testSubBrokerBean.getLinesOfBusiness()
			.get(LineOfBusinessEnum.PERSONAL_LINE.getCode()).getApplications()
			.get(ApplicationIdEnum.AUTO_REGULAR_QUOTE.getCode()).setAccessType("NONE");

		// Execute the tested method
		ISubBrokers result = this.controller.getSubBroker();

		// Validate the results
		assertNull(result);
		verify(this.mockSubBrokersTabController, times(1)).modify();
		verify(this.mockSubBrokerAdapter, never()).saveSubBroker((SubBrokers)this.testSubBroker, this.testSubBrokerBean);
		// Verify should only have been called for Auto Regular Quote
		verify(this.mockSubBrokersBusinessProcess, times(1)).verifyPosUnassignedToFsa(anyLong(), eq(ApplicationIdEnum.AUTO_REGULAR_QUOTE), eq(LineOfBusinessEnum.PERSONAL_LINE));
		verify(this.mockSubBrokersBusinessProcess, never()).verifyPosUnassignedToFsa(anyLong(), eq(ApplicationIdEnum.AUTO_QUICKQUOTE), eq(LineOfBusinessEnum.PERSONAL_LINE));
		verify(this.mockSubBrokersBusinessProcess, never()).verifyPosUnassignedToFsa(anyLong(), eq(ApplicationIdEnum.AUTO_QUICKQUOTE), eq(LineOfBusinessEnum.COMMERCIAL_LINE));
	}

	/**
	 * Test for
	 * {@link SubBrokersController#getSubBroker()}
	 * Case for Auto Quick Quote status to be deactivated and have assigned fsa/quote.
	 * @throws Exception
	 */
	@Test
	public void test_getSubBroker_AQQKDeactivate() throws Exception {
		// Set status change from active to inactive for Auto QQ to trigger verify of unassigned
		this.testQQAccessType.setWebAccessType("QI");
		this.testSubBrokerBean.getLinesOfBusiness()
			.get(LineOfBusinessEnum.PERSONAL_LINE.getCode()).getApplications()
			.get(ApplicationIdEnum.AUTO_QUICKQUOTE.getCode()).setAccessType("NONE");

		// Execute the tested method
		ISubBrokers result = this.controller.getSubBroker();

		// Validate the results
		assertNull(result);
		verify(this.mockSubBrokersTabController, times(1)).modify();
		verify(this.mockSubBrokerAdapter, never()).saveSubBroker((SubBrokers)this.testSubBroker, this.testSubBrokerBean);
		// Verify should only have been called for Auto Regular Quote
		verify(this.mockSubBrokersBusinessProcess, never()).verifyPosUnassignedToFsa(anyLong(), eq(ApplicationIdEnum.AUTO_REGULAR_QUOTE), eq(LineOfBusinessEnum.PERSONAL_LINE));
		verify(this.mockSubBrokersBusinessProcess, times(1)).verifyPosUnassignedToFsa(anyLong(), eq(ApplicationIdEnum.AUTO_QUICKQUOTE), eq(LineOfBusinessEnum.PERSONAL_LINE));
		verify(this.mockSubBrokersBusinessProcess, never()).verifyPosUnassignedToFsa(anyLong(), eq(ApplicationIdEnum.AUTO_QUICKQUOTE), eq(LineOfBusinessEnum.COMMERCIAL_LINE));
	}

	/**
	 * Test for
	 * {@link SubBrokersController#getSubBroker()}
	 * Case for IRCA Quick Quote status to be deactivated and have assigned fsa/quote.
	 * @throws Exception
	 */
	@Test
	public void test_getSubBroker_IRCAQQDeactivate() throws Exception {
		// Set status change from active to inactive for IRCA QQ to trigger verify of unassigned
		this.testIRCAQQAccessType.setWebAccessType("QI");
		this.testSubBrokerBean.getLinesOfBusiness()
			.get(LineOfBusinessEnum.COMMERCIAL_LINE.getCode()).getApplications()
			.get(ApplicationIdEnum.AUTO_QUICKQUOTE.getCode()).setAccessType("NONE");

		// Execute the tested method
		ISubBrokers result = this.controller.getSubBroker();

		// Validate the results
		assertNull(result);
		verify(this.mockSubBrokersTabController, times(1)).modify();
		verify(this.mockSubBrokerAdapter, never()).saveSubBroker((SubBrokers)this.testSubBroker, this.testSubBrokerBean);
		// Verify should only have been called for Auto Regular Quote
		verify(this.mockSubBrokersBusinessProcess, never()).verifyPosUnassignedToFsa(anyLong(), eq(ApplicationIdEnum.AUTO_REGULAR_QUOTE), eq(LineOfBusinessEnum.PERSONAL_LINE));
		verify(this.mockSubBrokersBusinessProcess, never()).verifyPosUnassignedToFsa(anyLong(), eq(ApplicationIdEnum.AUTO_QUICKQUOTE), eq(LineOfBusinessEnum.PERSONAL_LINE));
		verify(this.mockSubBrokersBusinessProcess, times(1)).verifyPosUnassignedToFsa(anyLong(), eq(ApplicationIdEnum.AUTO_QUICKQUOTE), eq(LineOfBusinessEnum.COMMERCIAL_LINE));
	}

	/**
	 * Test for
	 * {@link SubBrokersController#getSubBroker()}
	 * Case for subBroker to have quote assigned, but for the method to be called twice.
	 * First time should return null, second should return the subBroker.
	 * @throws Exception
	 */
	@Test
	public void test_getSubBroker_assignedQuoteTwice() throws Exception {
		// Set status change from active to inactive to trigger verify of unassigned
		this.testAQAccessType.setWebAccessType("QI");
		this.testSubBrokerBean.getLinesOfBusiness()
			.get(LineOfBusinessEnum.PERSONAL_LINE.getCode()).getApplications()
			.get(ApplicationIdEnum.AUTO_REGULAR_QUOTE.getCode()).setAccessType("NONE");

		// Stubs setup
		when(this.mockSubBrokersBusinessProcess.verifyPosUnassignedToFsa(anyLong(), eq(ApplicationIdEnum.AUTO_REGULAR_QUOTE), eq(LineOfBusinessEnum.PERSONAL_LINE)))
			.thenReturn(true); // FSA check needs to return true to access quote check
		when(this.mockSubBrokersBusinessProcess.verifyPosUnassignedToQuote(anyLong(), eq(ApplicationIdEnum.AUTO_REGULAR_QUOTE), eq(LineOfBusinessEnum.PERSONAL_LINE)))
			.thenReturn(false);

		// Execute the tested method and validate the results
		ISubBrokers result = this.controller.getSubBroker();
		assertNull(result);
		verify(this.mockSubBrokerAdapter, never()).saveSubBroker((SubBrokers)this.testSubBroker, this.testSubBrokerBean);

		// Execute the method again and validate the new results
		result = this.controller.getSubBroker();
		assertEquals(this.testSubBroker, result);
		verify(this.mockSubBrokerAdapter, times(1)).saveSubBroker((SubBrokers)this.testSubBroker, this.testSubBrokerBean);
	}

	/**
	 * Test for
	 * {@link SubBrokersController#verifyUnassignedToQuoteOrFSA(ISubBrokers, ApplicationIdEnum, LineOfBusinessEnum)}
	 * Case for when no FSA or quote is assigned to the subBroker. Should return true.
	 * @throws Exception
	 */
	@Test
	public void test_verifyUnassignedToQuoteOrFSA_unassigned() throws Exception {
		// Set status change from active to inactive to trigger verify of unassigned
		this.testAQAccessType.setWebAccessType("QI");
		this.testSubBrokerBean.getLinesOfBusiness()
			.get(LineOfBusinessEnum.PERSONAL_LINE.getCode()).getApplications()
			.get(ApplicationIdEnum.AUTO_REGULAR_QUOTE.getCode()).setAccessType("NONE");

		// Stubs setup
		when(this.mockSubBrokersBusinessProcess.verifyPosUnassignedToFsa(anyLong(), eq(ApplicationIdEnum.AUTO_REGULAR_QUOTE), eq(LineOfBusinessEnum.PERSONAL_LINE)))
			.thenReturn(true);
		when(this.mockSubBrokersBusinessProcess.verifyPosUnassignedToQuote(anyLong(), eq(ApplicationIdEnum.AUTO_REGULAR_QUOTE), eq(LineOfBusinessEnum.PERSONAL_LINE)))
			.thenReturn(true);

		// Execute the tested method
		boolean result = this.controller.verifyUnassignedToQuoteOrFSA(this.testSubBroker, ApplicationIdEnum.AUTO_REGULAR_QUOTE, LineOfBusinessEnum.PERSONAL_LINE);

		// Validate the results
		assertTrue(result);
		verify(this.mockSubBrokersTabController, never()).modify();
	}

	/**
	 * Test for
	 * {@link SubBrokersController#verifyUnassignedToQuoteOrFSA(ISubBrokers, ApplicationIdEnum, LineOfBusinessEnum)}
	 * Case for when an FSA is assigned to the subBroker. Should return false.
	 * @throws Exception
	 */
	@Test
	public void test_verifyUnassignedToQuoteOrFSA_assignedToFSA() throws Exception {
		// Set status change from active to inactive to trigger verify of unassigned
		this.testAQAccessType.setWebAccessType("QI");
		this.testSubBrokerBean.getLinesOfBusiness()
			.get(LineOfBusinessEnum.PERSONAL_LINE.getCode()).getApplications()
			.get(ApplicationIdEnum.AUTO_REGULAR_QUOTE.getCode()).setAccessType("NONE");

		// Stubs setup
		when(this.mockSubBrokersBusinessProcess.verifyPosUnassignedToFsa(anyLong(), eq(ApplicationIdEnum.AUTO_REGULAR_QUOTE), eq(LineOfBusinessEnum.PERSONAL_LINE)))
			.thenReturn(false);

		// Execute the tested method
		boolean result = this.controller.verifyUnassignedToQuoteOrFSA(this.testSubBroker, ApplicationIdEnum.AUTO_REGULAR_QUOTE, LineOfBusinessEnum.PERSONAL_LINE);

		// Validate the results
		assertFalse(result);
		verify(this.mockSubBrokersTabController, times(1)).modify();
	}

	/**
	 * Test for
	 * {@link SubBrokersController#verifyUnassignedToQuoteOrFSA(ISubBrokers, ApplicationIdEnum, LineOfBusinessEnum)}
	 * Case for when an quote is assigned to the subBroker. Should return false.
	 * @throws Exception
	 */
	@Test
	public void test_verifyUnassignedToQuoteOrFSA_assignedToQuote() throws Exception {
		// Set status change from active to inactive to trigger verify of unassigned
		this.testAQAccessType.setWebAccessType("QI");
		this.testSubBrokerBean.getLinesOfBusiness()
			.get(LineOfBusinessEnum.PERSONAL_LINE.getCode()).getApplications()
			.get(ApplicationIdEnum.AUTO_REGULAR_QUOTE.getCode()).setAccessType("NONE");

		// Stubs setup
		when(this.mockSubBrokersBusinessProcess.verifyPosUnassignedToFsa(anyLong(), eq(ApplicationIdEnum.AUTO_REGULAR_QUOTE), eq(LineOfBusinessEnum.PERSONAL_LINE)))
			.thenReturn(true); // FSA check needs to return true to access quote check
		when(this.mockSubBrokersBusinessProcess.verifyPosUnassignedToQuote(anyLong(), eq(ApplicationIdEnum.AUTO_REGULAR_QUOTE), eq(LineOfBusinessEnum.PERSONAL_LINE)))
			.thenReturn(false);

		// Execute the tested method
		boolean result = this.controller.verifyUnassignedToQuoteOrFSA(this.testSubBroker, ApplicationIdEnum.AUTO_REGULAR_QUOTE, LineOfBusinessEnum.PERSONAL_LINE);

		// Validate the results
		assertFalse(result);
		verify(this.mockSubBrokersTabController, times(1)).modify();
	}

	/**
	 * Test for
	 * {@link SubBrokersController#modifySubBrokers()}
	 * Where subbroker modification should not be done when on a different page than Modify SubBroker
	 * @throws Exception
	 */
	@Test
	public void testModifySubBrokers_InvalidPage() throws Exception {
		// Change the page in the subbrokers tabs controller to a page other than Modify SubBroker
		when(this.mockSubBrokersTabController.getPage()).thenReturn(SubBrokersTabController.Page.LIST_SUBBROKER);

		// Execute tested method and validate results
		this.controller.modifySubBrokers();

		// Validation method should've been called
		verify(this.mockSubBrokerValidator, never()).validate(any(SubBrokerDTO.class));
	}

	/**
	 * Private method used to create a general SubBroker that can be used for most tests.
	 */
	private void createGeneralSubBroker(){
		this.testSubBroker = new SubBrokers();

		// Setup contextual infos
		Set<IContextualElectronicLocators> contextualElectronicLocators = new HashSet<IContextualElectronicLocators>();
		Set<IContextualPhoneNumbers> contextualPhoneNumbers = new HashSet<IContextualPhoneNumbers>();
		Set<IContextualSubBrokerGnInfos> contextualSubBrokerGnInfos = new HashSet<IContextualSubBrokerGnInfos>();

		this.testSubBroker.setContextualElectronicLocators(contextualElectronicLocators);
		this.testSubBroker.setContextualPhoneNumbers(contextualPhoneNumbers);
		this.testSubBroker.setContextualSubBrokerGnInfos(contextualSubBrokerGnInfos);

		/* Setup access types (give most access by default) */
		// Auto Regular Quote
		this.testAQAccessType = new ContextualSubBrokerGnInfos();
		this.testAQAccessType.setApplicationId(ApplicationIdEnum.AUTO_REGULAR_QUOTE);
		this.testAQAccessType.setLineOfBusinessCode(LineOfBusinessEnum.PERSONAL_LINE);
		this.testAQAccessType.setAssignableInd("Y");
		this.testAQAccessType.setWebAccessType("QIB");
		contextualSubBrokerGnInfos.add(this.testAQAccessType);

		// Auto Quick Quote
		this.testQQAccessType = new ContextualSubBrokerGnInfos();
		this.testQQAccessType.setApplicationId(ApplicationIdEnum.AUTO_QUICKQUOTE);
		this.testQQAccessType.setLineOfBusinessCode(LineOfBusinessEnum.PERSONAL_LINE);
		this.testQQAccessType.setAssignableInd("Y");
		this.testQQAccessType.setWebAccessType("QIB");
		contextualSubBrokerGnInfos.add(this.testQQAccessType);

		// IRCA Quick Quote
		this.testIRCAQQAccessType = new ContextualSubBrokerGnInfos();
		this.testIRCAQQAccessType.setApplicationId(ApplicationIdEnum.AUTO_QUICKQUOTE);
		this.testIRCAQQAccessType.setLineOfBusinessCode(LineOfBusinessEnum.COMMERCIAL_LINE);
		this.testIRCAQQAccessType.setAssignableInd("Y");
		this.testIRCAQQAccessType.setWebAccessType("QIB");
		contextualSubBrokerGnInfos.add(this.testIRCAQQAccessType);



	}


	/**
	 * Private method used to create a general SubBrokerBean that can be used for most tests.
	 */
	private void createGeneralSubBrokerBean(){
		this.testSubBrokerBean = new SubBrokerDTO();

		/* Lines of business setup (set to most access by default) */
		Map<String,LineOfBusinessDTO> testLinesOfBusiness = new HashMap<String,LineOfBusinessDTO>();

		// Personal Lines
		LineOfBusinessDTO lineOfBusinessPLDTO = new LineOfBusinessDTO();
		Map<String, ApplicationDTO> testPLApplications = new HashMap<String, ApplicationDTO>();
		ApplicationDTO applicationInfoAQ = new ApplicationDTO();
		ApplicationDTO applicationInfoQQ = new ApplicationDTO();

		applicationInfoAQ.setAccessType("QIB");
		testPLApplications.put(ApplicationIdEnum.AUTO_REGULAR_QUOTE.getCode(), applicationInfoAQ);
		applicationInfoQQ.setAccessType("QIB");
		testPLApplications.put(ApplicationIdEnum.AUTO_QUICKQUOTE.getCode(), applicationInfoQQ);
		lineOfBusinessPLDTO.setApplications(testPLApplications);
		lineOfBusinessPLDTO.setLineOfBusiness(LineOfBusinessEnum.PERSONAL_LINE.getCode());
		testLinesOfBusiness.put(LineOfBusinessEnum.PERSONAL_LINE.getCode(), lineOfBusinessPLDTO);


		// Commercial Lines
		LineOfBusinessDTO lineOfBusinessCLDTO = new LineOfBusinessDTO();
		Map<String, ApplicationDTO> testCLApplications = new HashMap<String, ApplicationDTO>();
		ApplicationDTO applicationInfoIRCA = new ApplicationDTO();

		applicationInfoIRCA.setAccessType("QIB");
		testCLApplications.put(ApplicationIdEnum.AUTO_QUICKQUOTE.getCode(), applicationInfoIRCA);
		lineOfBusinessCLDTO.setApplications(testCLApplications);
		lineOfBusinessCLDTO.setLineOfBusiness(LineOfBusinessEnum.COMMERCIAL_LINE.getCode());
		testLinesOfBusiness.put(LineOfBusinessEnum.COMMERCIAL_LINE.getCode(), lineOfBusinessCLDTO);

		this.testSubBrokerBean.setLinesOfBusiness(testLinesOfBusiness);
	}

}
