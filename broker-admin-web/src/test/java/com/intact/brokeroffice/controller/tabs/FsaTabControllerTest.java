package com.intact.brokeroffice.controller.tabs;

import static org.junit.jupiter.api.Assertions.*;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class FsaTabControllerTest {
	
	private FsaTabController fsaTabController = new FsaTabController();

	@BeforeEach
	public void setUp() throws Exception {
	}

	@AfterEach
	public void tearDown() throws Exception {
	}

	@Test
	public void testGetPagePath() {
		fsaTabController.add();
		assertEquals("/pages/brokers/fsa/add.xhtml", fsaTabController.getPagePath());
		
		fsaTabController.list();
		assertEquals("/pages/brokers/fsa/list.xhtml", fsaTabController.getPagePath());
	}

}
