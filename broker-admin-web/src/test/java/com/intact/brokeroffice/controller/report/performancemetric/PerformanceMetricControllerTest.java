/**
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance 
 * Copyright (c) 2009, Intact Insurance, All rights reserved.<br>
 */
package com.intact.brokeroffice.controller.report.performancemetric;

import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.intact.brokeroffice.controller.performancemetrics.PerformanceMetricsController;


/**
 * <AUTHOR>
 *
 */
public class PerformanceMetricControllerTest {
	
private PerformanceMetricsController  kpiController;


	
	@BeforeEach
	public void setup(){		
		this.kpiController = new PerformanceMetricsController();		
	}
	
	@AfterEach
	public void teardown(){		
		this.kpiController = null;				
	}	
	
	@Test
	public void testPeriodDates()
	{		
		//The period To date is removed  
		//Period To date will be set to today's date
		this.kpiController.setDateFrom(new Date());
		this.kpiController.setDateTo(null);
		this.kpiController.manageRemovedDate();
		Assertions.assertNotNull(this.kpiController.getDateTo());
		
		//Assert.assertTrue(this.kpiController.getDateTo().compareTo(new Date())==0);
		// Truncate time part for comparison, to ensure only the date part is compared.
		Date expectedDateTo = truncateTime(new Date());
		Date actualDateTo = truncateTime(this.kpiController.getDateTo());
		Assertions.assertEquals(expectedDateTo, actualDateTo);

		//The period From date is removed 
		//It will be defaulted to period To date - 30 days		
		this.kpiController.setDateFrom(null);
		this.kpiController.setDateTo(new Date());
		this.kpiController.manageRemovedDate();
		GregorianCalendar fromCalendar = new GregorianCalendar();
		fromCalendar.setTime(this.kpiController.getDateTo());
		fromCalendar.add(Calendar.DATE, -30);	
		Assertions.assertNotNull(this.kpiController.getDateFrom());
		Assertions.assertTrue(this.kpiController.getDateFrom().compareTo(fromCalendar.getTime()) == 0);
		
		//The period From and To are removed  		
		this.kpiController.setDateFrom(null);
		this.kpiController.setDateTo(null);
		this.kpiController.manageRemovedDate();
		Assertions.assertNotNull(this.kpiController.getDateTo());
		Assertions.assertNotNull(this.kpiController.getDateFrom());
		
	}

	private Date truncateTime(Date date) {
		Calendar cal = Calendar.getInstance();
		cal.setTime(date);
		cal.set(Calendar.HOUR_OF_DAY, 0);
		cal.set(Calendar.MINUTE, 0);
		cal.set(Calendar.SECOND, 0);
		cal.set(Calendar.MILLISECOND, 0);
		return cal.getTime();
	}
	
	/**
	 * Utility method need to run this test case in Maven 1.
	 * 
	 * @return Test suite
	 */
//	public static junit.framework.Test suite() {
//		return new JUnit4TestAdapter(PerformanceMetricControllerTest.class);
//	}
}
