package com.intact.brokeroffice.controller.fsa;

import com.ing.canada.cif.domain.IFsaLoad;
import com.ing.canada.cif.domain.enums.CifCompanyEnum;
import com.ing.canada.cif.domain.enums.LineOfBusinessEnum;
import com.ing.canada.cif.service.IFsaLoadService;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.intact.brokeroffice.business.fsa.IFsaBusinessProcess;
import com.intact.brokeroffice.controller.permission.PermissionController;
import com.intact.brokeroffice.controller.province.ProvinceController;
import com.intact.brokeroffice.controller.province.SubscriptionCompanyEnum;
import com.intact.brokeroffice.controller.tabs.FsaTabController;
import com.intact.brokeroffice.controller.tabs.GeneralTabController;
import jakarta.faces.context.ExternalContext;
import jakarta.faces.context.FacesContext;
import jakarta.faces.event.ActionEvent;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;

import org.apache.commons.io.FileUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.primefaces.model.file.UploadedFile;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.mock.web.MockHttpSession;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.sql.Blob;
import java.util.*;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.*;

public class FsaControllerTest {

	private MockedStatic<FacesContext> mockedFacesContext;

	private FsaController fsaController = new FsaController();

	@TempDir
	public File tempFolder;

	private FsaTabController fsaTabController;

	private GeneralTabController generalTabController;

	private IFsaLoadService fsaLoadService;

	private FsaAdapter fsaAdapter;

	private FsaValidator fsaValidator;

	private IFsaBusinessProcess fsaBusinessProcess;

	private ProvinceController provinceController;

	private PermissionController permissionController;

	private FacesContext context;

	private ExternalContext externalContext;

	private HttpServletRequest request;

	private HttpServletResponse response;

	private HttpSession session;

	@BeforeEach
	public void setUp() throws Exception {
		mockedFacesContext = Mockito.mockStatic(FacesContext.class);
		fsaTabController = mock(FsaTabController.class);
		generalTabController = mock(GeneralTabController.class);
		permissionController = mock(PermissionController.class);
		fsaLoadService = mock(IFsaLoadService.class);
		fsaAdapter = mock(FsaAdapter.class);
		fsaValidator = mock(FsaValidator.class);
		fsaBusinessProcess = mock(IFsaBusinessProcess.class);
		provinceController = mock(ProvinceController.class);

		context = mock(FacesContext.class);
		externalContext = mock(ExternalContext.class);
		request = mock(HttpServletRequest.class);
		response = mock(HttpServletResponse.class);
		session = mock(HttpSession.class);

		when(request.getSession()).thenReturn(session);
		when(request.getSession(false)).thenReturn(session);
		mockedFacesContext.when(FacesContext::getCurrentInstance).thenReturn(context);
		when(context.getExternalContext()).thenReturn(externalContext);
		when(externalContext.getRequest()).thenReturn(request);
		when(externalContext.getResponse()).thenReturn(response);

		when(permissionController.getAvailableLinesOfBusiness()).thenReturn(
				new HashSet<LineOfBusinessEnum>(Arrays.asList(new LineOfBusinessEnum[]{LineOfBusinessEnum.COMMERCIAL_LINE, LineOfBusinessEnum.PERSONAL_LINE})));

		ReflectionTestUtils.setField(fsaController, "fsaTabController", fsaTabController);
		ReflectionTestUtils.setField(fsaController, "fsaLoadService", fsaLoadService);
		ReflectionTestUtils.setField(fsaController, "fsaAdapter", fsaAdapter);
		ReflectionTestUtils.setField(fsaController, "fsaValidator", fsaValidator);
		ReflectionTestUtils.setField(fsaController, "fsaBusinessProcess", fsaBusinessProcess);
		ReflectionTestUtils.setField(fsaController, "provinceController", provinceController);
		ReflectionTestUtils.setField(fsaController, "permissionController", permissionController);

		ReflectionTestUtils.setField(fsaController, "fsaUploadBean", new FsaUploadBean());
		fsaController.getFsaUploadBean().setBusinessContext("PL");
	}

	@AfterEach
	public void tearDown() throws Exception {
		fsaTabController = null;
		generalTabController = null;
		fsaLoadService = null;
		fsaAdapter = null;
		fsaValidator = null;
		fsaBusinessProcess = null;
		provinceController = null;
		mockedFacesContext.closeOnDemand();
	}

	/**
	 * Test that an inputStrem content is correctly write to a give file name
	 * {@link FsaController#writeInFsaFile(String)}
	 * @throws Exception
	 */
	@Test
	@Disabled
	public void testWriteInFsaFile() throws Exception {
		File contentFile = File.createTempFile("from.txt", null, tempFolder);
		FileUtils.writeStringToFile(contentFile, "some content");
		InputStream input = new FileInputStream(contentFile);

		UploadedFile file = mock(UploadedFile.class);
		fsaController.getFsaUploadBean().setFile(file);
		when(file.getInputStream()).thenReturn(input);

		File newFile = File.createTempFile("testFile.txt", null, tempFolder);
//		PowerMockito.whenNew(File.class).withArguments(anyString(), anyString()).thenReturn(newFile);

		boolean writeok = fsaController.writeInFsaFile("testFile.txt");

		assertTrue(writeok);
		assertEquals("some content", FileUtils.readFileToString(newFile));
		input.close();
	}

	@Test
	public void testDownloadFile() throws Exception{


		when(request.getParameter("fileName")).thenReturn("testFileName");
		when(request.getParameter("loadId")).thenReturn("loadId");

		String fileName = "startFile.txt";
		when(request.getParameter("fileName")).thenReturn(fileName);
		when(request.getSession(false)).thenReturn(session);
		when(request.getSession()).thenReturn(session);

		Map<String, String> originalIdMap = new HashMap<>();
		originalIdMap.put("loadId", "00001");
		when(session.getAttribute(anyString())).thenReturn(originalIdMap);
        when(response.getOutputStream()).thenReturn(mock(ServletOutputStream.class));

		Blob fileContent  = mock(Blob.class);
		File startFile = File.createTempFile(fileName, null, tempFolder);
		FileUtils.writeStringToFile(startFile, "some content");
		InputStream input = new FileInputStream(startFile);
		when(fileContent.getBinaryStream()).thenReturn(input);
        when(this.fsaLoadService.getFileContent(anyLong())).thenReturn(input);
		fsaController.downloadFile(mock(ActionEvent.class));
		input.close();
	}

	@Test
	@Disabled
	public void testPreUploadedProcess() throws Exception{
		when(fsaValidator.validate(any(FsaUploadBean.class))).thenReturn(true);
		when(provinceController.getProvinceCode()).thenReturn(ProvinceCodeEnum.QUEBEC);

		List<IFsaLoad> someFsaLoads = new ArrayList<IFsaLoad>();
		when(fsaLoadService.findAllSameFiles(anyString())).thenReturn(someFsaLoads);
		when(fsaValidator.validateSameFileNameStatus(anyList())).thenReturn(true);
		when(fsaAdapter.buildNextFileExtension(anyInt(), anyString())).thenReturn("testFile.txt");

		File contentFile = File.createTempFile("from.txt", null, tempFolder);
		FileUtils.writeStringToFile(contentFile, "some content");
		InputStream input = new FileInputStream(contentFile);

		UploadedFile file = mock(UploadedFile.class);
		fsaController.getFsaUploadBean().setFile(file);
		when(file.getInputStream()).thenReturn(input);

		File newFile = File.createTempFile("testFile.txt", null, tempFolder);
//		PowerMockito.whenNew(File.class).withArguments(anyString(), anyString()).thenReturn(newFile);

		when(fsaTabController.getPage()).thenReturn(FsaTabController.Page.ADD_FSA);
		fsaController.fileUploaded();

		assertEquals("some content", FileUtils.readFileToString(newFile));
		input.close();
	}

	/**
	 * Test for the addPage method.
	 * {@link FsaController#addPage()}
	 */
	@Test
	public void testAddPage(){
		// We set a province items list in the controller to validate that it is set to null
		when(provinceController.getProvinceCode()).thenReturn(ProvinceCodeEnum.QUEBEC);
		when(provinceController.getCompanies()).thenReturn(Arrays.asList(SubscriptionCompanyEnum.values()));
		CifCompanyEnum compEnumCode = CifCompanyEnum.INTACT_QC;
		compEnumCode.setSubBrokerCompanyNumber("A");
		when(provinceController.getCompanyEnumCode()).thenReturn(compEnumCode);
		when(provinceController.getProvince()).thenReturn("QUEBEC");
		// We temporarily set the tabController to a real object to validate the page change
		FsaTabController tabController = new FsaTabController();
		ReflectionTestUtils.setField(fsaController, "fsaTabController", tabController);

		fsaController.addPage();

		// We validate the results
		assertEquals(provinceController.getProvinceCode().name(), fsaController.getFsaUploadBean().getProvince(), "The province items should contain Quebec only");

		assertEquals("/pages/brokers/fsa/add.xhtml", tabController.getPagePath()); // should be the add page

		// We set the tabController back to a mock object as a precaution
		ReflectionTestUtils.setField(fsaController, "fsaTabController", fsaTabController);
	}

	/**
	 * Test for the listPage method.
	 * {@link FsaController#listPage()}
	 */
	@Test
	public void testListPage(){
		// We temporarily set the tabController to a real object to validate the page change
		FsaTabController tabController = new FsaTabController();
		ReflectionTestUtils.setField(fsaController, "fsaTabController", tabController);

		fsaController.listPage();

		// We validate the results
		assertEquals(Integer.valueOf(1), fsaController.getScrollerPage());
		assertEquals(Integer.valueOf(10), fsaController.getScrollerPerPage());
		assertEquals("/pages/brokers/fsa/list.xhtml", tabController.getPagePath()); // should be the list page

		// We set the tabController back to a mock object as a precaution
		ReflectionTestUtils.setField(fsaController, "fsaTabController", fsaTabController);
	}


	/**
	 * Test for the generatePath method when the nsfServerMode is false.
	 * {@link FsaController#generatePath()}
	 */
	@Test
	public void testGeneratePathServerModeFalse(){
		// We set the nsfServerMode attribute to "false" for this test
		ReflectionTestUtils.setField(fsaController, "nsfServerMode", "false");

		// We want the getRealPath function to return a test String to better validate the results
		when(externalContext.getRealPath("/")).thenReturn("testPath");

		String resultPath = fsaController.generatePath();
		assertEquals("testPath\\fsa_files\\", resultPath);
	}

	/**
	 * Test for
	 * {@link FsaController#fileUploaded()}
	 * Where fsa upload should be done when on the add table page
	 * @throws Exception
	 */
	@Test
	public void testModifySubBrokers_CorrectPage() throws Exception {
		// Change the page in the fsa tabs controller to the add fsa page
		when(this.fsaTabController.getPage()).thenReturn(FsaTabController.Page.ADD_FSA);

		when(this.fsaValidator.validate(any(FsaUploadBean.class))).thenReturn(false); // Return false to avoid executing rest of upload method

		// Execute tested method and validate results
		fsaController.fileUploaded();

		// Validation method should've been called
		verify(this.fsaValidator, times(1)).validate(any(FsaUploadBean.class));
	}

	/**
	 * Test for
	 * {@link FsaController#fileUploaded()}
	 * Where fsa upload should not be done when not on the add table page
	 * @throws Exception
	 */
	@Test
	public void testModifySubBrokers_InvalidPage() throws Exception {
		// Change the page in the fsa tabs controller to a page other than add FSA
		when(this.fsaTabController.getPage()).thenReturn(FsaTabController.Page.LIST_FSA);

		// Execute tested method and validate results
		fsaController.fileUploaded();

		// Validation method should've been called
		verify(this.fsaValidator, never()).validate(any(FsaUploadBean.class));
	}

}
























