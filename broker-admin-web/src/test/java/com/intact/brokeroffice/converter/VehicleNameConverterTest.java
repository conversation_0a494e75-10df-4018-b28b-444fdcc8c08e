package com.intact.brokeroffice.converter;

import static org.junit.jupiter.api.Assertions.assertEquals;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class VehicleNameConverterTest {
	
	private VehicleNameConverter converter = new VehicleNameConverter();

	@BeforeEach
	public void setUp() throws Exception {
	}

	@AfterEach
	public void tearDown() throws Exception {
	}

	@Test
	public void testGetAsString() {
		String vehicle = "VehiculeTestNameConverter";
		
		String convertedString = converter.getAsString(null, null, vehicle);
		
		assertEquals("VehiculeTestNameConver...", convertedString);
	}

}
