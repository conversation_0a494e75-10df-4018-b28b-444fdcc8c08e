package com.intact.brokeroffice.converter;

import static org.junit.jupiter.api.Assertions.*;


import jakarta.faces.convert.Converter;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class PointOfSaleConverterTest {

	private Converter converter;

	@BeforeEach
	public void setUp() throws Exception {
		converter = new PointOfSaleConverter();
	}

	@AfterEach
	public void tearDown() throws Exception {
		converter = null;
	}

	@Test
	public void testGetAsString_posContainsAWord_LESSTHAN_30_CaractersLong_NoSpace() {
		// Prepare
		String posName = StringUtils.repeat("A", 29);
		
		//Verify length
		assertTrue(posName.length() < 30);

		// Execute
		String result = converter.getAsString(null, null, posName);

		// Verify
		assertNotNull(result);
		assertEquals(29, posName.length());
		assertEquals("AAAAAAAAAAAAAAAAAAAAAAAAAAAAA", result);
	}

	@Test
	public void testGetAsString_posContainsAWord_EXACTLY_30_CaractersLong_NoSpace() {
		// Prepare
		String posName = StringUtils.repeat("A", 30);
		
		//Verify length
		assertTrue(posName.length() == 30);

		// Execute
		String result = converter.getAsString(null, null, posName);

		// Verify
		assertNotNull(result);
		assertEquals(30, posName.length());
		assertEquals("AAAAAAAAAAAAAAAAAAAAAAAAAAAAAA", result);		
	}

	@Test
	public void testGetAsString_posContainsAWord_MORETHAN_30_CaractersLong_NoSpace() {
		// Prepare
		String posName = StringUtils.repeat("A", 31);

		//Verify length
		assertTrue(posName.length() > 30);

		// Execute
		String result = converter.getAsString(null, null, posName);

		// Verify
		assertNotNull(result);
		assertEquals(30, result.length());
		assertEquals("AAAAAAAAAAAAAAAAAAAAAAAAAAA...", result);	
	}

	@Test
	public void testGetAsString_posContainsAWord_MORETHAN_30_CaractersLong_WithHypen() {
		// Prepare
		String posName = StringUtils.repeat("A", 25) + "-" + StringUtils.repeat("A", 7);
		
		//Verify length
		assertTrue(posName.length() == 33);

		// Execute
		String result = converter.getAsString(null, null, posName);

		// Verify
		assertNotNull(result);
		assertEquals(30, result.length());
		assertEquals("AAAAAAAAAAAAAAAAAAAAAAAAA-A...", result);
	}

	@Test
	public void testGetAsString_posContains_ALLWORDS_LESSTHAN_30_Caracters() {
		// Prepare
		String posName = StringUtils.repeat("X", 25) + " " +
						 StringUtils.repeat("a", 27) + " " +
						 StringUtils.repeat("Z", 29);
		
		//Verify length
		assertTrue(posName.length() == (25 + 1 + 27 + 1 + 29));

		// Execute
		String result = converter.getAsString(null, null, posName);

		// Verify
		assertNotNull(result);
		assertEquals(result.length(), posName.length());
		assertEquals("XXXXXXXXXXXXXXXXXXXXXXXXX aaaaaaaaaaaaaaaaaaaaaaaaaaa ZZZZZZZZZZZZZZZZZZZZZZZZZZZZZ", result);
	}
}
