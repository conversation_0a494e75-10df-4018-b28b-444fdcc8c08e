package com.intact.brokeroffice.filter;

import java.io.IOException;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.mock.web.MockHttpSession;
import org.springframework.test.util.ReflectionTestUtils;

import com.ing.canada.common.web.api.auth.TamTokensEnum;

import static org.mockito.Mockito.*;

public class SecurityFilterTest {

	private SecurityFilter securityFilter = new SecurityFilter();

	private MockHttpServletRequest request;
	private MockHttpServletResponse response;
	private ServletRequest servletRequest;
	private ServletResponse servletResponse;
	private MockHttpSession session;

	@BeforeEach
	public void setUp() throws Exception {
		session = new MockHttpSession();
		request = new MockHttpServletRequest();
		response = new MockHttpServletResponse();
		request.setSession(session);
		servletRequest = request;
		servletResponse = response;

		ReflectionTestUtils.setField(securityFilter, "ldapGroupProgramAdmins", "ldapGroupProgramAdmins");
		ReflectionTestUtils.setField(securityFilter, "ldapGroupAdmins", "ldapGroupAdmins");
	}

	@AfterEach
	public void tearDown() throws Exception {
	}

	@Test
	public void testDoFilter_access_denied() throws IOException, ServletException {
		session.setAttribute(TamTokensEnum.TAM_SECURITY_ROLES_ATTRIB.getTokenValue(), "testRole");
		request.setRequestURI("/brokers/subbroker/list.jsf");

		FilterChain chain = mock(FilterChain.class);
		securityFilter.doFilter(servletRequest, servletResponse, chain);

		verify(chain, times(0)).doFilter(servletRequest, servletResponse);
	}

	@Test
	public void testDoFilter_access_allowed() throws IOException, ServletException {
		session.setAttribute(TamTokensEnum.TAM_SECURITY_ROLES_ATTRIB.getTokenValue(),
				"testRole,ldapGroupProgramAdmins");
		request.setRequestURI("/brokers/subbroker/list.jsf");

		FilterChain chain = mock(FilterChain.class);
		securityFilter.doFilter(servletRequest, servletResponse, chain);

		verify(chain).doFilter(servletRequest, servletResponse);
	}

}
