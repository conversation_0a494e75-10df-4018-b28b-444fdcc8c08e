package com.intact.brokeroffice.filter;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.http.Cookie;

import com.ing.canada.cif.domain.enums.LanguageEnum;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.mock.web.MockFilterChain;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.mock.web.MockHttpSession;
import org.springframework.test.util.ReflectionTestUtils;

import com.ing.canada.common.web.api.auth.TamTokensEnum;
import com.intact.brokeroffice.business.accounts.IAccountsBusinessProcess;
import com.intact.brokeroffice.business.exception.BrokerServiceException;
import com.intact.brokeroffice.controller.enums.SessionConstantsEnum;
import com.intact.brokeroffice.controller.language.LanguageController;
import com.intact.brokeroffice.controller.province.ProvinceController;
import com.intact.canada.brm.domain.broker.BrokerWebOfficeAccess;
import com.intact.canada.brm.domain.user.UserAccount;

public class SessionTimeoutFilterTest {
	SessionTimeoutFilter sessionTimeoutFilter = new SessionTimeoutFilter();

	private IAccountsBusinessProcess accountsBusinessProcess;

	private ProvinceController provinceController;

	private LanguageController languageController;


	private String ldapGroupAdmins = "ldapGroupAdmins";

	private String ldapGroupProgramAdmins = "ldapGroupProgramAdmins";

	@BeforeEach
	public void setUp() throws Exception {
		accountsBusinessProcess = mock(IAccountsBusinessProcess.class);
		provinceController = mock(ProvinceController.class);
		languageController = mock(LanguageController.class);

		ReflectionTestUtils.setField(sessionTimeoutFilter, "accountsBusinessProcess", accountsBusinessProcess);
		ReflectionTestUtils.setField(sessionTimeoutFilter, "provinceController", provinceController);
		ReflectionTestUtils.setField(sessionTimeoutFilter, "languageController", languageController);

		ReflectionTestUtils.setField(sessionTimeoutFilter, "ldapGroupAdmins", ldapGroupAdmins);
		ReflectionTestUtils.setField(sessionTimeoutFilter, "ldapGroupProgramAdmins", ldapGroupProgramAdmins);
	}

	@AfterEach
	public void tearDown() throws Exception {
		accountsBusinessProcess = null;
		provinceController = null;
		languageController = null;
	}

	/**
	 * Language not null isAjaxRequest true showTimeOutPage false
	 *
	 * @throws IOException
	 * @throws ServletException
	 */
	@Test
	public void testDoFilter() throws IOException, ServletException {

		MockHttpServletRequest request = new MockHttpServletRequest();
		MockHttpSession session = new MockHttpSession();
		request.setSession(session);
		request.setRequestURI("test/index.jsf");
		request.addParameter("referenceNo", "testRefNumber");
		request.addParameter("AJAXREQUEST", "ajax test request");

		request.addParameter("language", "fr");
		Cookie cookie = new Cookie(SessionConstantsEnum.BROKER_LANGUAGE.getSessionConstant(), "Some value");
		request.setCookies(cookie);
		ServletRequest servletRequest = request;

		MockHttpServletResponse response = new MockHttpServletResponse();
		ServletResponse servletResponse = response;

		FilterChain filterChain = new MockFilterChain();

		sessionTimeoutFilter.setProvinceController(new ProvinceController());
		sessionTimeoutFilter.setLanguageController(new LanguageController());

		sessionTimeoutFilter.doFilter(servletRequest, servletResponse, filterChain);

		assertEquals("testRefNumber",
				session.getAttribute(SessionConstantsEnum.EMAIL_REFERENCE_NUMBER.getSessionConstant()));
		assertEquals("fr", session.getAttribute(SessionConstantsEnum.BROKER_LANGUAGE.getSessionConstant()));
		assertNotNull(response.getCookie(SessionConstantsEnum.BROKER_LANGUAGE.getSessionConstant()));
		assertEquals("Some value", response.getCookie(SessionConstantsEnum.BROKER_LANGUAGE.getSessionConstant())
				.getValue());
	}

	@Test
	public void testDoFilterShowTimeOutPage() throws IOException, ServletException {

		ReflectionTestUtils.setField(sessionTimeoutFilter, "spoeMode", true);

		MockHttpServletRequest request = new MockHttpServletRequest();
		MockHttpSession session = new MockHttpSession();
		request.setSession(session);
		request.setRequestedSessionId("testRequestSessioonID");
		request.setRequestURI("test/index.jsf");
		request.addParameter("referenceNo", "testRefNumber");
		request.addParameter("AJAXREQUEST", "ajax test request");

		Cookie cookie = new Cookie("brokerDefaultCompany", "A");
		request.setCookies(cookie);
		request.setAttribute(SessionConstantsEnum.BROKER_LANGUAGE.getSessionConstant(), null);
		ServletRequest servletRequest = request;

		MockHttpServletResponse response = new MockHttpServletResponse();
		ServletResponse servletResponse = response;

		FilterChain filterChain = new MockFilterChain();

		sessionTimeoutFilter.setProvinceController(new ProvinceController());
		sessionTimeoutFilter.doFilter(servletRequest, servletResponse, filterChain);

		assertEquals("testRefNumber",
				session.getAttribute(SessionConstantsEnum.EMAIL_REFERENCE_NUMBER.getSessionConstant()));
		assertEquals("A", session.getAttribute(SessionConstantsEnum.COMPANY.getSessionConstant()));
	}

	/**
	 * Language null isAjaxRequest false showTimeOutPage false
	 * userRole ldapGroupBroker
	 * @throws IOException
	 * @throws ServletException
	 * @throws BrokerServiceException
	 */
	@SuppressWarnings("unchecked")
	@Test
	public void testDoFilter2() throws IOException, ServletException, BrokerServiceException {
		when(accountsBusinessProcess.getDefaultProvince(anyString())).thenReturn(null);
		when(languageController.filterValue(anyString())).thenReturn(LanguageEnum.FRENCH.getCode());

		UserAccount userAccount = new UserAccount();
		BrokerWebOfficeAccess aBrokerWebOfficeAccess = new BrokerWebOfficeAccess();
		aBrokerWebOfficeAccess.setMasterOwnerCode("MasterOwnerCode");
		userAccount.addBrokerWebOfficeAccess(aBrokerWebOfficeAccess);
		when(accountsBusinessProcess.findByUId(anyString())).thenReturn(userAccount);


		MockHttpServletRequest request = new MockHttpServletRequest();
		MockHttpSession session = new MockHttpSession();

		List<String> provinceList = new ArrayList<String>();
		provinceList.add("QC");
		session.setAttribute(SessionConstantsEnum.AVAILABLE_PROVINCES.getSessionConstant(), provinceList);
		request.setSession(session);
		request.setRequestedSessionId("testRequestSessionId");
		request.setRequestURI("test/index.jsf");
		request.addParameter("referenceNo", "testRefNumber");
		List<String> allowedProvinceList = new ArrayList<String>();
		allowedProvinceList.add("QC");
		allowedProvinceList.add("ON");

		Cookie cookie = new Cookie(SessionConstantsEnum.BROKER_LANGUAGE.getSessionConstant(), "French");
		request.setCookies(cookie);
		request.addHeader(TamTokensEnum.TAM_USER_NAME_CREDENTIAL.getTokenValue(), "testUserName");
		request.addHeader(TamTokensEnum.TAM_SECURITY_ROLES_ATTRIB.getTokenValue(), "testUserRole ldapGroupBroker -QC -ON");
		request.addHeader(TamTokensEnum.TAM_USER_CLASSIC_USER_ID.getTokenValue(), "testClassicId");
		session.setAttribute("province", "QC");

		ServletRequest servletRequest = request;

		MockHttpServletResponse response = new MockHttpServletResponse();
		ServletResponse servletResponse = response;

		FilterChain filterChain = new MockFilterChain();

		sessionTimeoutFilter.doFilter(servletRequest, servletResponse, filterChain);

		assertEquals("testRefNumber",
				session.getAttribute(SessionConstantsEnum.EMAIL_REFERENCE_NUMBER.getSessionConstant()));
		assertEquals("testUserName", session.getAttribute(TamTokensEnum.TAM_USER_NAME_CREDENTIAL.getTokenValue()));
		assertEquals("testClassicId", session.getAttribute(TamTokensEnum.TAM_USER_CLASSIC_USER_ID.getTokenValue()));
		assertEquals("QC", session.getAttribute(SessionConstantsEnum.PROVINCE.getSessionConstant()));
	}

	/**
	 * Language null isAjaxRequest false showTimeOutPage false
	 * userRole ldapGroupBrokerReassign
	 * @throws IOException
	 * @throws ServletException
	 * @throws BrokerServiceException
	 */
	@SuppressWarnings("unchecked")
	@Test
	public void testDoFilter3() throws IOException, ServletException, BrokerServiceException {
		when(accountsBusinessProcess.getDefaultProvince(anyString())).thenReturn(null);

		UserAccount userAccount = new UserAccount();
		BrokerWebOfficeAccess aBrokerWebOfficeAccess = new BrokerWebOfficeAccess();
		aBrokerWebOfficeAccess.setMasterOwnerCode("MasterOwnerCode");
		userAccount.addBrokerWebOfficeAccess(aBrokerWebOfficeAccess);
		when(accountsBusinessProcess.findByUId(anyString())).thenReturn(userAccount);

		MockHttpServletRequest request = new MockHttpServletRequest();
		MockHttpSession session = new MockHttpSession();

		List<String> provinceList = new ArrayList<String>();
		provinceList.add("QC");
		session.setAttribute(SessionConstantsEnum.AVAILABLE_PROVINCES.getSessionConstant(), provinceList);
		request.setSession(session);
		request.setRequestedSessionId("testRequestSessionId");
		request.setRequestURI("test/index.jsf");
		request.addParameter("referenceNo", "testRefNumber");
		List<String> allowedProvinceList = new ArrayList<String>();
		allowedProvinceList.add("QC");
		allowedProvinceList.add("ON");

		Cookie cookie = new Cookie(SessionConstantsEnum.BROKER_LANGUAGE.getSessionConstant(), "French");
		request.setCookies(cookie);
		request.addHeader(TamTokensEnum.TAM_USER_NAME_CREDENTIAL.getTokenValue(), "testUserName");
		request.addHeader(TamTokensEnum.TAM_SECURITY_ROLES_ATTRIB.getTokenValue(), "testUserRole BrokerReassign -QC -ON");
		request.addHeader(TamTokensEnum.TAM_USER_CLASSIC_USER_ID.getTokenValue(), "testClassicId");
		session.setAttribute("province", "QC");

		ServletRequest servletRequest = request;

		MockHttpServletResponse response = new MockHttpServletResponse();
		ServletResponse servletResponse = response;

		FilterChain filterChain = new MockFilterChain();

		sessionTimeoutFilter.doFilter(servletRequest, servletResponse, filterChain);

		assertEquals("testRefNumber",
				session.getAttribute(SessionConstantsEnum.EMAIL_REFERENCE_NUMBER.getSessionConstant()));
		assertEquals("testUserName", session.getAttribute(TamTokensEnum.TAM_USER_NAME_CREDENTIAL.getTokenValue()));
		assertEquals("testClassicId", session.getAttribute(TamTokensEnum.TAM_USER_CLASSIC_USER_ID.getTokenValue()));
		assertEquals("QC", session.getAttribute(SessionConstantsEnum.PROVINCE.getSessionConstant()));
	}

	/**
	 * Test method for
	 * {@link SessionTimeoutFilter#doFilter(ServletRequest, ServletResponse, FilterChain)}
	 * Case for broker language to be an invalid value
	 * @throws IOException
	 * @throws ServletException
	 * @throws BrokerServiceException
	 */
	@Test
	public void test_doFilter_invalidLanguage() {
		assertThrows(AssertionError.class, () -> {
			// General object setup
			MockHttpServletRequest mockRequest = new MockHttpServletRequest();
			ServletResponse mockResponse = new MockHttpServletResponse();
			FilterChain filterChain = new MockFilterChain();
			MockHttpSession mockSession = new MockHttpSession();


			mockRequest.setSession(mockSession);
			mockRequest.setRequestedSessionId("testRequestSessionID");

			// Setup to access language validation
			mockRequest.setRequestURI("index.jsf");

			// Create the whitelist for languages
			List<String> allowedLanguageList = new ArrayList<String>();
			allowedLanguageList.add("French");

			// Set the cookie with a value that is not in the language whitelist
			Cookie cookie = new Cookie(SessionConstantsEnum.BROKER_LANGUAGE.getSessionConstant(), "TEST");
			mockRequest.setCookies(cookie);
			sessionTimeoutFilter.setLanguageController(new LanguageController());
			// Execute the method and validate the results
			sessionTimeoutFilter.doFilter((ServletRequest) mockRequest, mockResponse, filterChain);
			fail("Servlet Exception should've been thrown - Invalid language");
		});
	}

	/**
	 * Test method for
	 * {@link SessionTimeoutFilter#doFilter(ServletRequest, ServletResponse, FilterChain)}
	 * Case for broker province to be an invalid value
	 * @throws IOException
	 * @throws ServletException
	 * @throws BrokerServiceException
	 */
	@Test
	public void test_doFilter_invalidProvince() throws IOException, ServletException, BrokerServiceException {
		// General object setup
		MockHttpServletRequest mockRequest = new MockHttpServletRequest();
		ServletResponse mockResponse = new MockHttpServletResponse();
		FilterChain filterChain = new MockFilterChain();
		MockHttpSession mockSession = new MockHttpSession();

		mockRequest.setSession(mockSession);
		mockRequest.setRequestedSessionId("testRequestSessionID");
		ReflectionTestUtils.setField(sessionTimeoutFilter, "spoeMode", true); // Set spoe mode to access province validation

		// Create the whitelist for provinces
		List<String> allowedProvinceList = new ArrayList<String>();
		allowedProvinceList.add("QC");
		allowedProvinceList.add("ON");

		// Set the cookie with a value that is not in the province whitelist
		Cookie cookie = new Cookie("brokerDefaultProvince", "TEST");
		mockRequest.setCookies(cookie);

		// Execute the method and validate the results
		sessionTimeoutFilter.doFilter((ServletRequest)mockRequest, mockResponse, filterChain);
		assertNull(mockSession.getAttribute(SessionConstantsEnum.PROVINCE.getSessionConstant()), "Province should not have been set in the session - invalid value");
	}

	/**
	 * Test method for
	 * {@link SessionTimeoutFilter#doFilter(ServletRequest, ServletResponse, FilterChain)}
	 * Case for a username (in this case the TAM username) to be of wrong format.
	 * @throws IOException
	 * @throws ServletException
	 * @throws BrokerServiceException
	 */
	@Test
	public void test_doFilter_wrongFormatUsername() {
		assertThrows(AssertionError.class, () -> {
			// General object setup
			MockHttpServletRequest mockRequest = new MockHttpServletRequest();
			ServletResponse mockResponse = new MockHttpServletResponse();
			FilterChain filterChain = new MockFilterChain();
			MockHttpSession mockSession = new MockHttpSession();

			mockSession.setAttribute("province", "QC");
			mockSession.setAttribute("company", "A");
			// Setup to access username validation
			mockRequest.setSession(mockSession);
			mockRequest.setRequestedSessionId("testRequestSessionID");
			mockRequest.setRequestURI("index.jsf");

			// Set a bad TAM username
			mockRequest.addHeader(TamTokensEnum.TAM_USER_NAME_CREDENTIAL.getTokenValue(), "ub1220");


			// Execute the method and validate the results
			sessionTimeoutFilter.doFilter((ServletRequest) mockRequest, mockResponse, filterChain);
			fail("Servlet Exception should've been thrown - Invalid user format");
		});
	}

}
