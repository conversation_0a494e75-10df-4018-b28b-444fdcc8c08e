package com.intact.brokeroffice.helper;

import static org.junit.jupiter.api.Assertions.*;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.Reader;
import java.util.ArrayList;
import java.util.List;

import org.apache.commons.io.FileUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

public class FileUtilTest {

	@TempDir
	public File tempFolder;

	private static final String LINE_SEPARATOR = System.getProperty("line.separator");
	
	private File startFile;

	private File endFile;

	@BeforeEach
	public void setUp() throws Exception {
		startFile = File.createTempFile("startFile.txt", null, tempFolder);
		endFile = File.createTempFile("endFile.txt", null, tempFolder);
	}

	@AfterEach
	public void tearDown() throws Exception {
		startFile.deleteOnExit();
		endFile.deleteOnExit();
	}

	/**
	 * Test that content is correctly written from inputStreams to a given file
	 * 
	 * @throws IOException
	 */
	@Test
	public void testWriteFromInput() throws IOException {
		FileUtils.writeStringToFile(startFile, "test content");
		InputStream input = new FileInputStream(startFile);

		FileUtil.write(endFile, input);

		String resultContent = FileUtils.readFileToString(endFile);

		assertEquals("test content", resultContent);
	}
	
	@Test
	public void testWrtieFromByteArray() throws IOException{
		String content = "test content";
		byte[] bytes = content.getBytes();
		
		FileUtil.write(endFile, bytes);
		
		assertEquals("test content", FileUtils.readFileToString(endFile));
	}
	
	@Test
	public void testWrtieFromByteArrayWithAppend() throws IOException{
		String content = "test content";
		byte[] bytes = content.getBytes();
		
		FileUtil.write(endFile, bytes, false);
		
		assertEquals("test content", FileUtils.readFileToString(endFile));
	}

	/**
	 * Test that content is correctly written from FileReader to a given file
	 * 
	 * @throws IOException
	 */
	@Test
	public void testWriteFromReader() throws IOException {
		FileUtils.writeStringToFile(startFile, "test content");
		Reader reader = new FileReader(startFile);

		FileUtil.write(endFile, reader);

		String resultContent = FileUtils.readFileToString(endFile);

		assertEquals("test content", resultContent);
	}
	
	@Test
	public void testWrtieFromCharArray() throws IOException{
		String content = "test content";
		char[] chars = content.toCharArray();
		
		FileUtil.write(endFile, chars);
		
		assertEquals("test content", FileUtils.readFileToString(endFile));
	}
	
	@Test
	public void testWrtieFromCharArrayWithAppend() throws IOException{
		String content = "test content";
		char[] chars = content.toCharArray();
		
		FileUtil.write(endFile, chars, false);
		
		assertEquals("test content", FileUtils.readFileToString(endFile));
	}

	/**
	 * Test that content is correctly written from List of string to a given file
	 * 
	 * @throws IOException
	 */
	@Test
	public void testWriteFromListOfString() throws IOException {
		FileUtils.writeStringToFile(startFile, "test content", true);
		
		File expectedResult = File.createTempFile("expect.txt", null, tempFolder);
		FileUtils.writeStringToFile(expectedResult, "test content" + LINE_SEPARATOR);
		String expected = FileUtils.readFileToString(expectedResult);

		List<String> records = new ArrayList<String>();
		records.add("test content");

		FileUtil.write(endFile, records);

		String resultContent = FileUtils.readFileToString(endFile);

		assertEquals(expected, resultContent);
	}
	
	@Test
	public void testWriteFromString() throws IOException{
		String content = "test content";
		
		FileUtil.write(endFile, content);
		
		assertEquals("test content", FileUtils.readFileToString(endFile));
	}
	
	@Test
	public void testWriteFromStringWithAppend() throws IOException{
		String content = "test content";
		
		FileUtil.write(endFile, content, false);
		
		assertEquals("test content", FileUtils.readFileToString(endFile));
	}
	
	/**
	 * Test that a file content is correctly transform into byte array
	 * 
	 * @throws IOException
	 */
	@Test
	public void testReadBytes() throws IOException{
		FileUtils.writeStringToFile(startFile, "test content");
	
		byte[] resultBytes = FileUtil.readBytes(startFile);
	
		String result = new String(resultBytes);
		
		assertEquals("test content", result);
	}
	

	/**
	 * Test that a file content is correctly transform into char array
	 * 
	 * @throws IOException
	 */
	@Test
	public void testReadChars() throws IOException{
		FileUtils.writeStringToFile(startFile, "test content");
		
		String resultString = FileUtil.readString(startFile);
		
		assertEquals("test content", resultString);
	}
	
	/**
	 * Test that a file content is correctly transform into list of string 
	 * 
	 * @throws IOException
	 */
	@Test
	public void testReadRecords() throws IOException{
		FileUtils.writeStringToFile(startFile, "test content");
		
		List<String> resultString = FileUtil.readRecords(startFile);
		
		assertEquals(1, resultString.size());
		assertEquals("test content", resultString.get(0));
	}
	
	/**
	 * Test that a file content is correctly copy to an other file
	 * 
	 * @throws IOException
	 */
	@Test
	public void testCopy() throws IOException{
		FileUtils.writeStringToFile(startFile, "test content");
		
		FileUtil.copy(startFile, endFile);
		
		String copiedString = FileUtils.readFileToString(endFile);
		
		assertEquals("test content", copiedString);
	}
	
	/**
	 * Destination file exist and overwrite is false
	 * suppose to throw exception
	 * 
	 * @throws IOException
	 */
	@Test  
	public void testCopyDestionationExist(){
		assertThrows(IOException.class, () -> {
			FileUtils.writeStringToFile(startFile, "test content");

			FileUtil.copy(startFile, endFile, false);
		});
	}
	
	/**
	 *test moving file endFile suppose to be deleted
	 * 
	 * @throws IOException
	 */
	@Test
	public void testMove() throws IOException{
		FileUtils.writeStringToFile(startFile, "test content");
		
		FileUtil.move(startFile, endFile);
		
		assertTrue(endFile.delete());
	}
	
	/**
	 *test moving file supposed to failed because overwrite is false
	 * 
	 * @throws IOException
	 */
	@Test
	public void testMoveFailed(){
		assertThrows(IOException.class, () -> {
			FileUtils.writeStringToFile(startFile, "test content");

			FileUtil.move(startFile, endFile, false);
		});
	}
	
	/**
	 *test moving file supposed to failed because file have same name
	 * 
	 * @throws IOException
	 */
	@Test
	public void testMoveFailedSameName(){
		assertThrows(IOException.class, () -> {
			FileUtils.writeStringToFile(startFile, "test content");

			FileUtil.move(startFile, startFile, false);
		});
	}
	
	@Test
	public void testTrimFilePath(){
		String fileName = "//test.txt";
		
		assertEquals("test.txt", FileUtil.trimFilePath(fileName));
	}
	
	@Test
	public void testUniqueFile() throws IOException{
		File resultFile = FileUtil.uniqueFile(startFile, "test.txt");
		assertFalse(resultFile.exists());
	}
}


















