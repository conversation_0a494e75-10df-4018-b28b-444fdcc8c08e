package com.intact.brokeroffice.web;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import java.util.ArrayList;
import java.util.Enumeration;
import java.util.List;

import jakarta.servlet.http.HttpSession;

import jakarta.faces.context.ExternalContext;
import jakarta.faces.context.FacesContext;

import com.intact.brokeroffice.web.WebSystemDAO;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.springframework.mock.web.MockHttpSession;
import org.springframework.test.util.ReflectionTestUtils;

import com.ing.canada.common.web.api.auth.TamTokensEnum;
import com.intact.brokeroffice.controller.enums.SessionConstantsEnum;


public class WebSystemDAOTest {

	private MockedStatic<FacesContext> mockedFacesContext;

	private WebSystemDAO webSystemDAO = new WebSystemDAO();

	private static String URL_TO_GOBRIO_B2E = "url.to.gobrio";

	//private static String URL_TO_GOBRIO_B2B = "url.to.gobrio.b2b";

	//private static String URL_TO_SAVERS_B2E = "url.to.savers";

	//private static String URL_TO_SAVERS_B2B = "url.to.savers.b2b";

	private String ldapGroupBroker = "ldapGroupBroker";

	private String ldapGroupBrokerReassign = "ldapGroupBrokerReassign";

	private FacesContext context;
	private ExternalContext externalContext;
	private MockHttpSession session;
	private MockHttpSession newSession;

	@BeforeEach
	public void setUp() throws Exception {
		mockedFacesContext = Mockito.mockStatic(FacesContext.class);
		context = mock(FacesContext.class);
		externalContext = mock(ExternalContext.class);
		session = new MockHttpSession();
		newSession = new MockHttpSession();

		mockedFacesContext.when(FacesContext::getCurrentInstance).thenReturn(context);
		when(context.getExternalContext()).thenReturn(externalContext);
		when(externalContext.getSession(false)).thenReturn(session);
		when(externalContext.getSession(true)).thenReturn(newSession);

		ReflectionTestUtils.setField(webSystemDAO, "ldapGroupBroker", ldapGroupBroker);
		ReflectionTestUtils.setField(webSystemDAO, "ldapGroupBrokerReassign", ldapGroupBrokerReassign);
	}

	@AfterEach
	public void tearDown() throws Exception {
		context = null;
		externalContext = null;
		session = null;
		newSession = null;
		mockedFacesContext.closeOnDemand();
	}

	@Test
	public void testGetSession(){
		initSessionAttributes();
		session.setNew(true);
		HttpSession resultSession = webSystemDAO.getSession();

		Enumeration<String> attributes = session.getAttributeNames();
		Enumeration<String> resultAttributes = resultSession.getAttributeNames();

		while(attributes != null && attributes.hasMoreElements()){
			String name = (String) attributes.nextElement();
			String resultName = (String) resultAttributes.nextElement();
			assertEquals((String) attributes.nextElement()
					, (String) resultAttributes.nextElement());
			assertEquals(session.getAttribute(name)
					, resultSession.getAttribute(resultName));
		}
	}

	@Test
	public void testGetCompanyQC(){
		session.setAttribute(SessionConstantsEnum.PROVINCE.getSessionConstant(), "QC");
		assertEquals("A", webSystemDAO.getCompany());
	}

	@Test
	public void testGetCompanyAB(){
		session.setAttribute(SessionConstantsEnum.PROVINCE.getSessionConstant(), "AB");
		assertEquals("3", webSystemDAO.getCompany());
	}

	@Test
	public void testGetCompanyON(){
		session.setAttribute(SessionConstantsEnum.PROVINCE.getSessionConstant(), "ON");
		assertEquals("6", webSystemDAO.getCompany());
	}

	@Test
	public void testGetCompanyOTHER(){
		session.setAttribute(SessionConstantsEnum.PROVINCE.getSessionConstant(), "XX");
		assertEquals("A", webSystemDAO.getCompany());
	}

	@Test
	public void testRetreiveMasterBroker(){
		List<String> brokers = new ArrayList<String>();
		brokers.add("Belairdirect");
		session.setAttribute(SessionConstantsEnum.AVAILABLE_MASTERS.getSessionConstant(), brokers);
		assertEquals("Belairdirect", webSystemDAO.getMasterBroker());
	}

	@Test
	public void testRetreiveRemoteSystemUrl(){
		session.setAttribute(SessionConstantsEnum.PROVINCE.getSessionConstant(), "QC");
		session.setAttribute(TamTokensEnum.TAM_USER_CLASSIC_USER_ID.getTokenValue(), "classicId");
		session.setAttribute(TamTokensEnum.TAM_USER_NAME_CREDENTIAL.getTokenValue(), "");
		session.setAttribute(TamTokensEnum.TAM_SECURITY_ROLES_ATTRIB.getTokenValue(), "CurrentSessionAccessLevelTest");
		String remotreSystemUrl = webSystemDAO.getRemoteSystemUrl();
		assertEquals(URL_TO_GOBRIO_B2E, remotreSystemUrl);
	}

	@Test
	public void testRetreiveUploadUser(){
		session.setAttribute(TamTokensEnum.TAM_USER_CLASSIC_USER_ID.getTokenValue(), "classicId - uploadUser");
		session.setAttribute(TamTokensEnum.TAM_USER_NAME_CREDENTIAL.getTokenValue(), "");
		String uploadUser = webSystemDAO.getUploadUser();
		assertEquals("classicId - uploadUser", uploadUser);
	}

	private void initSessionAttributes(){
		session.setAttribute("attribute1", "value1");
		session.setAttribute("attribute2", "value2");
		session.setAttribute("attribute3", "value3");
		session.setAttribute("attribute4", "value4");
	}

}















