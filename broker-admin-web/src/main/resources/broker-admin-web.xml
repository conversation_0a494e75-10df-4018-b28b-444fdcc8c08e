<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:jee="http://www.springframework.org/schema/jee"
	xmlns:context="http://www.springframework.org/schema/context"
	xsi:schemaLocation="http://www.springframework.org/schema/beans
						http://www.springframework.org/schema/beans/spring-beans.xsd
						http://www.springframework.org/schema/context
						http://www.springframework.org/schema/context/spring-context.xsd
						http://www.springframework.org/schema/jee
						http://www.springframework.org/schema/jee/spring-jee.xsd">

	<context:annotation-config />
	<context:component-scan base-package="com.intact.brokeroffice" />

	<!-- property placeholder post-processor -->
	<bean id="brokeroffice-placeholderConfig" class="org.springframework.context.support.PropertySourcesPlaceholderConfigurer">
		<property name="location" value="classpath:broker-admin-web.properties" />
		<property name="placeholderPrefix" value="$(" />
		<property name="placeholderSuffix" value=")" />
	</bean>

	<bean id="plp-groupLockManager" class="com.ing.canada.plp.lock.InsurancePolicyLockManager" scope="prototype">
		<property name="enabled" value="${optimistic.group.lock.enabled}" />
	</bean>

	<bean id="ldap-group-broker" class="java.lang.String">
		<constructor-arg value="$(ldap-group-broker)" />
	</bean>
	
	<bean id="ldap-group-broker-reassign" class="java.lang.String" >
		<constructor-arg value="$(ldap-group-broker-reassign)"/>
	</bean>

	<bean id="ldap-group-program-admins" class="java.lang.String">
		<constructor-arg value="$(ldap-group-program-admins)" />
	</bean>

	<bean id="ldap-group-admins" class="java.lang.String">
		<constructor-arg value="$(ldap-group-admins)" />
	</bean>

	<bean id="spoe-mode" class="java.lang.String">
		<constructor-arg value="$(spoe-mode)" />
	</bean>

	<bean id="logout-url-admin" class="java.lang.String">
		<constructor-arg value="$(logout-url-admin)" />
	</bean>

	<bean id="logout-admin" class="java.lang.String">
		<constructor-arg value="$(logout-b2e)" />
	</bean>

	<bean id="nfs-url-dir" class="java.lang.String">
		<constructor-arg value="$(nfs-url-dir)" />
	</bean>

	<bean id="nfs-server-mode" class="java.lang.String">
		<constructor-arg value="$(nfs-server-mode)" />
	</bean>
	
</beans>

