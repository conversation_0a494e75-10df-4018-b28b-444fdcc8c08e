<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:context="http://www.springframework.org/schema/context"
	xsi:schemaLocation="http://www.springframework.org/schema/beans
						http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
						http://www.springframework.org/schema/context
						http://www.springframework.org/schema/context/spring-context-3.0.xsd">

	<bean id="ldapPlaceholderConfig" class="org.springframework.context.support.PropertySourcesPlaceholderConfigurer">
		<property name="location" value="classpath:ldap.properties" />
		<property name="order" value="0" />
	</bean>

	<bean id="contextSource" class="org.springframework.ldap.core.support.LdapContextSource">
		<property name="url" value="${LDAP_URL}:${LDAP_PORT}" />
		<property name="userDn" value="${LDAP_USER_DN}" />
		<property name="password" value="${LDAP_PASSWORD}" />
	</bean>
	
	<bean id="ldapTemplate" class="com.intact.tools.ldap.util.LdapTemplate">
		<constructor-arg ref="contextSource" />
	</bean>
	
</beans>
