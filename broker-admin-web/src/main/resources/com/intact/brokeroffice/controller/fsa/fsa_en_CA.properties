title.access.mangement.fsa.report = Access Management - Manage FSA's - Upload Status report


title.add_fsa			 = Add New FSA Table
title.list_fsa			 = List of FSA Tables
title.report_fsa		 = Upload Status Report of FSA 
add.table.file			 = Import FSA Table File
add.table.owner 		 = Table Owner
add.table.effective 	 = Effective Date
add.table.return		 = Return to List of FSA Tables
add.button.browse		 = Browse
add.new.table			 = Add New Table
add.table.province		 = Province
add.table.lineOfBusiness = Line of Business
add.table.select		 = Select a value
add.table.businessContext  = Business Context

# PROVINCE ENUM
AB = Alberta
MB = Manitoba
NL = Newfoundland and Labrador
NS = Nova Scotia
ON = Ontario
QC = Quebec
YT = Yukon
BC = British Columbia
NB = New Brunswick
NT = Northwest Territories
NU = Nunavut
PE = Prince Edward Island
SK = Saskatchewan

#LINE OF BUSINESS ENUM
lineOfBusiness.code.C=Commercial Lines
lineOfBusiness.code.P=Personal Lines 
lineOfBusiness.code.=Personal Lines

#BUSINESS CONTEXT ENUM
businessContext.code.CL=Commercial Lines (Auto)
businessContext.code.PL=Personal Lines (Auto and Res.)
businessContext.code.=Personal Lines
businessContext.code.CL_CCQQ=Commercial Lines (P&C)

# LIST OF FSA TABLE
list.fsa.line.error		= Line error
list.fsa.fsa			= FSA/Post Cde
list.fsa.tableFilename  = Table Filename
list.fsa.pos			= Assigned POS
list.fsa.startDate		= Processing Date
list.fsa.endDate		= Expiry Date
list.fsa.processingDate	= Processing Date
list.fsa.province		= Province
list.fsa.lineOfBusiness	= Line of Business
list.fsa.result			= Result
list.fsa.refresh        = Refresh List
list.fsa.businessContext = Business Context
list.fsa.company         = Company

# UPLOAD STATUS REPORT 
fsa.report.total.error 				= Total number of errors : 
list.status.report.line.error		= Line<br>error
list.status.report.fsa      		= FSA<br>Postal code
list.status.report.company     		= Company<br>Code 
list.status.report.effective.date	= Effective<br>Date
list.status.report.expiry.date		= Expiry<br>Date 
list.status.report.origin   		= Origin
list.status.report.language 		= Language
list.status.report.message  		= Message
list.status.report.pos.nbr  		= POS No

FRENCH						= French
ENGLISH						= English
BOTH						= Both

title.status.report.failed  = Upload Status Report - File Update Failed 
title.status.report.succeed = Upload Status Report - File Update Successful 

error.validInfo 		= Please enter valid information.
error.missingInfo		= Please provide a response to the question.
error.file.name.prefix	= Please select a file with a naming convention that matches the business context selected
error.file.name.prefix.CL = The file name must begin with CL for commercial lines.
error.file.name.prefix.PL = The file name must begin with PL for personal lines.

fsa.load.status.SUCC       = Successful
fsa.load.status.FAIL	   = Failed
fsa.load.status.PROC       = Processing
fsa.load.status.KILL	   = Cancel
fsa.load.status.DEFAULT	   = 

fsa.nbr.fsa.processed	= Records processed
fsa.total.upload.time	= Total upload time

#MSGxxx
error.file.not.a.recognized.msg = The file must be in the format .dat.
#MSGxxx
error.date.greater.than.today.msg =	The date selected must be greater than or equal to today's date.
#MSGxxx
error.date.greater.than.effective.msg	=	The date selected must be greater than start date.
#MSGxxx
error.not.shearch.caracter.entry.msg =	You must enter at least two character to initiate a search.
#MSGxxx
error.shearch.caracter.not.format.msg =	You must enter the complete postal code following the format A9A9A9.
#MSGxxx
error.assigned.fsa.not.pos.msg =	You must assign a POS and enter an effective date when adding an FSA or Postal Code.
#MSGxxx
error.assigned.fsa.already.exist.msg =	An entry for this FSA, Postal Code or Table has an incorrect expiry date. When two or more entries exist for a specific FSA, Postal Code or Table, each entry, other than the most recent, must expire the day before the effective date of the subsequent entry. Please add/correct the expiry date for the prior entry.

error.date.InvalidFormat=Invalid format

#MSG6679
same.file.still.processing.error.msg= FSA file is already being processed for this province, please repeat your request in a few minutes.

#MSG6515
fsa.report.message.MSG.A = The FSA or Postal code not match the province.

#MSG6516
fsa.report.message.MSG.B = The format of FSA or postal code is invalid.

#MSG6517
fsa.report.message.MSG.C = At least one mandatory field is missing.

#MSG6518
fsa.report.message.MSG.D = The default is in error.

#MSG6519
fsa.report.message.MSG.E = The POS or its owner does not exist in the database 

#MSG6520
fsa.report.message.MSG.F = POS is not QI, QIB or QB

#MSG6521
fsa.report.message.MSG.G = POS information missing in Web Zone

#MSG6522
fsa.report.message.MSG.H = There is an overlap of dates for FSA / postal code.

#MSG6523  The Company not match the province.  
fsa.report.message.MSG.I = The Company not match the province. 

#MSG6524  Expiry date must be greater than effective date  
fsa.report.message.MSG.J = The expiry date must be greater than the effective date.

#MSG6525  Origin is invalid  
fsa.report.message.MSG.K = Origin is invalid.

#MSG6526  Language is invalid  
fsa.report.message.MSG.L = Language is invalid.

#MSG6527  Record format is incorrect 
fsa.report.message.MSG.M = Record format is incorrect

#MSG6528  Technical problem : 
fsa.report.forced.cancel.msg1 			= A technical problem forced the cancellation of the load of this FSA :
fsa.report.forced.cancel.load.id 		= LOAD ID : 
fsa.report.forced.cancel.file.name 		= FILE NAME :    
fsa.report.forced.cancel.process.date   = DATE :                           
fsa.report.forced.cancel.msg2 			= Please advise us if the problem persists.

#MSG27480  Application Mode is invalid
fsa.report.message.MSG.N  = Application mode is invalid.

#MSG6550 
fsa.report.message.MSG.O = The FSA / Postal code does not exist in Canada Post.

fsa.report.message.MSG.P = Only postal code or FSA is required.

#MSG27481  Validation between origin web site and application mode
fsa.report.message.MSG.Q  = Validation between origin Web Site and application mode is invalid.

fsa.report.message.MSG.DEFAULT = 

fsa.add.invalid.answer.field = Please provide a response to the question or correct the answer.