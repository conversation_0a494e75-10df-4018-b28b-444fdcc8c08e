synchro.title=Synchro indicator
synchro.text=The quoted insurance premium includes the Synchro discount. The customer has an existing homeowner contract with Intact Insurance or has requested an auto quote as if he had a homeowner policy with Intact Insurance.

status.title=Statuses
status.quoteIncomplete=Quote incomplete
status.quoteIncomplete.text=The customer has not completed the quote yet.
status.quoteIncomplete.nextSteps=<b>Next steps:</b> Look at contact indicator and contact the customer if he accepted to be contacted regarding this quote. 

status.quoteComplete=Quote complete
status.quoteComplete.text=An offer was provided to the customer.
status.quoteComplete.nextSteps=<b>Next steps:</b> Look at contact indicator and contact the customer if he accepted to be contacted regarding this quote.

status.purchaseAbandoned=Purchase incomplete
status.purchaseAbandoned.text=An offer was provided to the customer. Customer initiated the purchase process but did not finish.
status.purchaseAbandoned.nextSteps=<b>Next steps:</b> Contact the customer to confirm the coverage is not in effect.

status.purchaseCompleted=Purchase complete
status.purchaseCompleted.text=Customer clicked the purchase online button and finished the purchase process.
status.purchaseCompleted.nextSteps=<b>Next steps:</b> Upload the quote to goBRIO and contact the customer to make final arrangements.

status.expired=Expired
status.expired.text=Customer submitted this quote more than 30 days ago.
status.expired.nextSteps=<b>Next steps:</b> Contact the customer. Less than 10 days remain to upload the quote to goBRIO.

status.uploaded=Uploaded
status.uploaded.text=Quote was uploaded to goBRIO.
status.uploaded.nextSteps=<b>Next steps:</b> If final arrangements are not done, go to goBRIO and retrieve the quote from there.

status.accepted=Accepted
status.accepted.text=Quote has been uploaded to goBRIO and accepted for contract.
status.accepted.nextSteps=<b>Next steps:</b> : Ensure all final arrangements have been completed.

status.refused=Refused
status.refused.text=Quote has been uploaded to goBRIO but the contract issued has been refused by either the customer or Intact Insurance.
status.refused.nextSteps=<b>Next steps:</b> If refused by the customer, you may wish to follow-up in a few days time to see if their decision is final. If refused by Intact Insurance, no further submissions from this customer will be accepted. 

type.of.residence.title=Residential insurance indicators
type.of.residence.text.tenant= <b>Tenant :</b> Tenant
type.of.residence.text.condo= <b>Condo :</b> Condo owner
type.of.residence.text.home= <b>Home :</b> Home owner
type.of.residence.text.green.ind=Already Intact
type.of.residence.text.red.ind=Not interested

