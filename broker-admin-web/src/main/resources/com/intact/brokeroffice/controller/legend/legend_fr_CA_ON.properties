synchro.title=Indicateur MFMA
synchro.text=La tarification de cette soumission inclut le rabais Mon foyer, mon autoMD. Le client possède une police habitation active avec Intact Assurance ou a consenti à acheter une police habitation en plus de la soumission auto actuelle.

status.title=Statuts
status.quoteIncomplete=Soumission incomplète
status.quoteIncomplete.text=Le client n'a pas encore complété la soumission.
status.quoteIncomplete.nextSteps=<b>Prochaines étapes:</b> Vérifier l'indicateur de contact et communiquer avec le client s'il a donné son consentement.

status.quoteComplete=Soumission effectuée
status.quoteComplete.text=Une offre a été fournie au client.
status.quoteComplete.nextSteps=<b>Prochaines étapes:</b> Vérifier l'indicateur de contact et communiquer avec le client s'il a donné son consentement.

status.purchaseAbandoned=Achat incomplet
status.purchaseAbandoned.text=Une offre a été fournie au client. Le client a amorcé le processus d'achat mais ne l'a pas complété.
status.purchaseAbandoned.nextSteps=<b>Prochaines étapes:</b> Contacter le client pour lui confirmer que les protections ne sont pas en vigueur.

status.purchaseCompleted=Achat effectué
status.purchaseCompleted.text=Le client a sélectionné l'option d'achat en ligne et a complété le processus d'achat.
status.purchaseCompleted.nextSteps=<b>Prochaines étapes:</b> Vous pouvez commander MVR et Auto Plus. Contacter le client pour prendre les dispositions finales

status.expired=Expirée
status.expired.text=Le client a complété cette soumission il y a plus de 30 jours. 
status.expired.nextSteps=<b>Prochaines étapes:</b> Contacter le client. Il reste moins de 10 jours pour télécharger cette soumission dans Savers.

status.uploaded=Téléchargée
status.uploaded.text=La soumission a été téléchargée dans Savers.
status.uploaded.nextSteps=<b>Prochaines étapes:</b> Dans le cas où les dispositions finales ne sont pas complétées, vous pouvez récupérer la soumission dans Savers.

status.autoplus=Indicateur d'Autoplus
status.autoplus.text=A+
status.autoplus.value=Rapport Autoplus consulté.

vic.title=Client Intact de valeur (VIC)
vic.message=<b>Messages de souscription</b>
vic.vc=<b>VC</b>
vic.vc.value=Client Intact de valeur
vic.rc=<b>RC</b>
vic.rc.value=Référer à la compagnie
vic.slash=<b>/</b>
vic.slash.value=En présence de messages de souscriptions multiple, les messages seront séparés par une barre oblique (ex.: RC/VC)
