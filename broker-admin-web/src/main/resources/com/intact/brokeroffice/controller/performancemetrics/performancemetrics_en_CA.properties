tab.title=PERFORMANCE INDICATORS

title=Performance Indicators*
date.from=From
date.format=(YYYY-MM-DD)
performance.metric.label.end.period=Period To: 
performance.metric.label.start.period=Period From:
table.label.broker=Broker
table.label.quotes.number=# quotes
table.label.bound.quotes.number=# bind
table.label.upload.quotes.number=# upload
table.label.bound.quotes.percentage=Bind % 
table.label.upload.quotes.percentage=Upload %

table.label.average.elapsed.time=Avg time</br> Bind >Upload
table.label.average.premium=Avg Premium</br> (All Quotes)

performance.metric.grand.total=Grand Total
performance.metric.quote.status=Quote Status
performance.metric.followup=Follow-Up Status
performance.metric.quotes=Quotes
performance.metric.purchase.req=Purch. Requests
performance.metric.uploaded.quotes=Uploaded 
performance.metric.subbroker=Sub-Broker (POS) 
performance.metric.masterboker=Master Broker (Proprio)
performance.metric.purchase.req=Purch. Requests
performance.metric.incomplete=Incompl.
performance.metric.complete=Comp.
performance.metric.not.proceed=Not proc.
performance.metric.accepted=Accep.
performance.metric.refused=Refus.
performance.metric.exp=Exp.
performance.metric.total=Totals
performance.metric.never.contacted=Never Contacted
performance.metric.further.contact.needed=Contact, Follow-up Needed
performance.metric.no.contact.needed=Contact, No Follow-up Needed
performance.metric.never.contacted.no.contact.needed=No Contact, No Follow-up Needed
performance.metric.based.on.quotes.received=* Based on quotes received as of 23:59:59 on  
performance.metric.sub.total=S.total
performance.metric.sub.total=S.total

error.msg.performance.metric.period.start.date=A Period Start date must be selected.
error.msg.performance.metric.period.end.date=A Period End date must be selected.

#MSG500
error.msg.performance.metric.future.date=The date selected cannot be in the future.
#MSG499
error.msg.performance.metric.period.start.must.be.prior.or.equal.to.end.date=The "From" date selected must be less than or equal to the "To" date selected.
error.msg.performance.metric.period.end.must.be.after.or.equal.to.start.date=The "To" date selected must be greater than or equal to the "From" date selected.

error.date.InvalidFormat=Invalid format

INT=Intact web site
WEBBK=Broker web site
BOTH=Both
