synchro.title=my Home and Auto<sup>TM</sup> indicator
synchro.text=Customer quote includes the my Home and Auto<sup>TM</sup> discount. 

status.title=Statuses
status.quoteIncomplete=Quote incomplete
status.quoteIncomplete.text=The customer has not completed the quote yet.
status.quoteIncomplete.nextSteps=<b>Next steps:</b> Look at contact indicator and contact the customer if he accepted to be contacted regarding this quote. 

status.quoteComplete=Quote complete
status.quoteComplete.text=An offer was provided to the customer.
status.quoteComplete.nextSteps=<b>Next steps:</b> Look at contact indicator and contact the customer if he accepted to be contacted regarding this quote.

status.purchaseAbandoned=Purchase incomplete
status.purchaseAbandoned.text=An offer was provided to the customer. Customer initiated the purchase process but did not finish.
status.purchaseAbandoned.nextSteps=<b>Next steps:</b> Contact the customer to confirm the coverage is not in effect.

status.purchaseCompleted=Purchase complete
status.purchaseCompleted.text=Customer clicked the purchase online button and finished the purchase process.
status.purchaseCompleted.nextSteps=<b>Next steps:</b> You can order MVR & Auto Plus. Contact the customer to make final arrangements.

status.expired=Expired
status.expired.text=Customer submitted this quote more than 30 days ago.
status.expired.nextSteps=<b>Next steps:</b> Contact the customer. Less than 10 days remain to upload the quote to Savers.

status.uploaded=Uploaded
status.uploaded.text=Quote was uploaded to Savers.
status.uploaded.nextSteps=<b>Next steps:</b> If final arrangements are not done, go to Savers and retrieve the quote from there.

status.autoplus=Autoplus Indicator
status.autoplus.text=A+
status.autoplus.value=Autoplus report has been pulled.
