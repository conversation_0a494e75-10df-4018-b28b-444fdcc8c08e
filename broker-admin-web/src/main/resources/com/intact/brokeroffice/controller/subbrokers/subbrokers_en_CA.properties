title.list_subbroker=Point of Sale List
title.modify_subbroker=Modify Point of Sale
note.modify_subbroker=The update of all points of sales does not apply when we disable or enable a point of sale.
title.return.link.subbrokers=Return to Point of Sale List
 
table.owner       = Owner No
table.subBrokerNo = POS No
table.name=Name 
table.enable.attending =Active<br/>AQ/QQ/AS
table.city=City/Province
table.action=Action

return=Return to Point of Sale List

modify=Modify

form.subbrokerno=Number :
#DE709
form.name=Name :
#DE710
form.address=Address : 
form.program.access   = Type of access to Web program :
form.suppress.address = Suppress address display :
#DE696
form.online.name=Online Name :
form.online.name.display.ind=Display Online Name on the Web Site :
#DE697 
form.phone.intact=Online Telephone Number (INTACT Web Site) :
form.phone.pos.=Phone Number :
form.brokerage.firm.phone=Phone Number - Offer Page :
form.phone.pos.OFFPG=Phone Number - Offer Page :
form.quick.quote.phone=Auto Quick Quote - Phone Number :
form.quick.quote.phone.offer=Auto Quick Quote - Phone Number - Offer Page :
form.home.quick.quote.phone=Residential Quick Quote - Phone Number :
form.home.quick.quote.phone.offer=Res. Quick Quote - Phone Number - Offer Page :
form.irca.quick.quote.phone=IRCA Quick Quote - Phone Number :
form.irca.quick.quote.phone.offer=IRCA Quick Quote - Phone Number - Offer Page :
form.assignable=Assignable :
form.sales.P=Sales - Personal Lines
form.sales.C=Sales - Commercial Lines
application.C.CCQQ=Commercial property Quick Quote
application.C.AQQK=IRCA Quick Quote
application.P.WQ=Residential Quick Quote
application.P.AQQK=Auto Quick Quote
application.P.AQRG=Auto Quote
application.P.BQ=Bundle Quote
client.centre.yes=Yes
client.centre.no=No
client.centre=Client Centre
client.service=Service
service=Service
service.CLCT=Client Centre
client.centre.access=Program access
client.centre.phone.number=Phone Number
#DE700
form.chat=Chat I.D. :
#DE698
form.email=Online eMail Address :
form.email.sla.report=SLA Report eMail Address :
#DE699
form.url=Online Web Site url :
form.phone.number.broker.file=Phone number - Broker File
#LBL45586
form.client.scheduled.callback=Allow client Scheduled Callback
client.scheduled.callback.yes=Yes
client.scheduled.callback.no=No
#DE701
form.business.allow.client.notification.ind=Client eDoc Emails :
form.business.online.name.display.ind=Display Online Name :
form.business.hours.display.ind=Display Opening Hours : 
form.business.hour=Business Hours :
form.start.hour=Start :
form.end.hour=End :
form.sunday=Sunday
form.monday=Monday
form.tuesday=Tuesday
form.wednesday=Wednesday
form.thursday=Thursday
form.friday=Friday
form.saturday=Saturday
form.image.en=Image File 1 (English) :
form.logo.en=Image File 2 (English) :
form.image.fr=Image File 1 (French) :
form.logo.fr=Image File 2 (French) :
form.brokerage.image.french=Image - Brokerage Name (French) :
form.brokerage.image.english=Image - Brokerage Name (English) :
form.brokerage.image.svg.french=Image SVG - Brokerage Name (French) :
form.brokerage.image.svg.english=Image SVG - Brokerage Name (English) :
form.brokerage.image.printing.french=Image - Printing (French) :
form.brokerage.image.printing.english=Image - Printing (English) :
form.save=Update
form.cancel=Cancel
form.not.copy.email.to.client=Do not copy on emails to client

error.validInfo=Please enter valid information.
error.missingInfo=Please provide a response to the question.
error.online.name.mandatory.no.logo=The online name is mandatory in the absence of a logo.

#MSG504
error.file.size.too.large.msg=The image you are attempting to upload is too large. It must be less than 2Mb in size. Please resize the image or use a different file format.
#MSG503 
error.file.not.a.recognized.image.msg=The image you are attempting to upload is not a recognized image format. You are required to use a file with a format of bmp, gif, jpg, or png.
error.file.not.a.recognized.image.svg.msg=The image you are attempting to upload is not a recognized image format. You are required to use a file with a format of svg.
error.file.empty=The image you are attempting to upload is empty. Please select a file with non-zero weight.
error.email=The email address format is incorrect. It must contain 73 characters or less and follow the format [C]@[C].[C] where [C] represents at least 1 alphanumeric character (A-Z and 0-9).
error.emailSLA=The SLA Report email address format is incorrect. It must contain 73 characters or less and follow the format [C]@[C].[C] where [C] represents at least 1 alphanumeric character (A-Z and 0-9).
#MSG4410
error.fsa.assigned.to.POS.msg = This Point of Sale has entries in the FSA/Postal Code Assignment Table. These FSA's and/or Postal Codes must be reassigned before the POS can be deactivated from the web program.

#MSG4411  
error.active.quotes.assigned.POS.msg = This Point of Sale has active quotes assigned. Do you wish to proceed with the deactivation of the Point of Sale of the web program? Click Update to proceed with or Cancel to abort the deactivation.
 
#MSG502; MSG48634
error.phone.number.msg=You must enter a complete telephone number (i.e.: ************).
error.phone.number.msg.QB=Please provide a phone number for the Broker Web Site.
error.phone.number.msg.QI=Please provide a phone number for the Intact Web Site.

error.phone.number.msg.bundle.default=Please provide default phone number.
error.phone.number.msg.bundle.offpg=Please provide offer page phone number.

#MSG509
modify.confirmation=The point of sale {0} was modified successfully.

modify.update.modal.content = Do you want to apply the changes to all points of sales of the owner?

label.title.autoquote=Auto Quote
label.title.quickquote=Quick Quote

INT=Intact Web Site
WEBBK=Broker Web Site
CC=Client Centre Web Site

QI=Intact Web Site
QB=Broker Web Site
QIB=Broker and Intact Web Site

QUOTE_INTACT 			= QI
QUOTE_BROKER 			= QB
QUOTE_INTACT_AND_BROKER = QIB
NONE					= Inactive
toll.free=Toll-Free

#label for modify
subBrokerBean.onlineName=Online Name
subBrokerBean.url=Internet Address
subBrokerBean.linesOfBusiness[P].contact.emailAddress=Email Address - Personal
subBrokerBean.linesOfBusiness[P].contact.chatId=Chat identifier - Personal

subBrokerBean.images[LOGO-EN-SVG-UI].image.name=Logo SVG en ligne anglais
subBrokerBean.images[LOGO-FR-SVG-UI].image.name=Logo SVG en ligne anglais
subBrokerBean.images[LOGO-EN--UI].image.name=Logo en ligne anglais
subBrokerBean.images[LOGO-FR--UI].image.name=Logo en ligne français
subBrokerBean.images[LOGO-EN-PR-UI].image.name=Logo imprimée anglais
subBrokerBean.images[LOGO-FR-PR-UI].image.name=Logo imprimée français

subBrokerBean.linesOfBusiness[P].applications[AQQK].accessType=Web Access Quick Quote - Personal
subBrokerBean.linesOfBusiness[P].applications[AQQK].assignable=Assignable Quick Quote - Personal
subBrokerBean.linesOfBusiness[P].applications[AQQK].phones[].brokerPhone=Broker phone for Quick Quote - Personal
subBrokerBean.linesOfBusiness[P].applications[AQQK].phones[].brokerTollFree=Free broker phone call for Quick Quote - Personal
subBrokerBean.linesOfBusiness[P].applications[AQQK].phones[].intactPhone=Intact phone for Quick Quote - Personal
subBrokerBean.linesOfBusiness[P].applications[AQQK].phones[].intactTollFree=Free Intact phone call for Quick Quote - Personal
subBrokerBean.linesOfBusiness[P].applications[AQQK].phones[OFFPG].brokerPhone=Broker phone for offer page Quick Quote - Personal
subBrokerBean.linesOfBusiness[P].applications[AQQK].phones[OFFPG].brokerTollFree=Free broker phone call for offer page Quick Quote - Personal
subBrokerBean.linesOfBusiness[P].applications[AQQK].phones[OFFPG].intactPhone=Intact phone for offer page Quick Quote - Personal
subBrokerBean.linesOfBusiness[P].applications[AQQK].phones[OFFPG].intactTollFree=Free broker phone call for offer page Quick Quote - Personal

subBrokerBean.linesOfBusiness[P].applications[AQRG].accessType=Web Access Auto Quote - Personal
subBrokerBean.linesOfBusiness[P].applications[AQRG].assignable=Assignable Auto Quote - Personal
subBrokerBean.linesOfBusiness[P].applications[AQRG].phones[].brokerPhone=Broker phone for Auto Quote - Personal
subBrokerBean.linesOfBusiness[P].applications[AQRG].phones[].brokerTollFree=Free broker phone call for Auto Quote - Personal
subBrokerBean.linesOfBusiness[P].applications[AQRG].phones[].intactPhone=Intact phone for Auto Quote - Personal
subBrokerBean.linesOfBusiness[P].applications[AQRG].phones[].intactTollFree=Free Intact phone call for Auto Quote - Personal
subBrokerBean.linesOfBusiness[P].applications[AQRG].phones[OFFPG].brokerPhone=Broker phone for offer page Auto Quote - Personal
subBrokerBean.linesOfBusiness[P].applications[AQRG].phones[OFFPG].brokerTollFree=Free broker phone call for offer page Auto Quote - Personal
subBrokerBean.linesOfBusiness[P].applications[AQRG].phones[OFFPG].intactPhone=Intact phone for offer page Auto Quote - Personal
subBrokerBean.linesOfBusiness[P].applications[AQRG].phones[OFFPG].intactTollFree=Free broker phone call for offer page Auto Quote - Personal

subBrokerBean.linesOfBusiness[P].applications[WQ].accessType=Web Access Home Quote - Personal
subBrokerBean.linesOfBusiness[P].applications[WQ].assignable=Assignable Home Quote - Personal
subBrokerBean.linesOfBusiness[P].applications[WQ].phones[].brokerPhone=Broker phone for Home Quote - Personal
subBrokerBean.linesOfBusiness[P].applications[WQ].phones[].brokerTollFree=Free broker phone call for Home Quote - Personal
subBrokerBean.linesOfBusiness[P].applications[WQ].phones[].intactPhone=Intact phone for Home Quote - Personal
subBrokerBean.linesOfBusiness[P].applications[WQ].phones[].intactTollFree=Free Intact phone call for Home Quote - Personal
subBrokerBean.linesOfBusiness[P].applications[WQ].phones[OFFPG].brokerPhone=Broker phone for offer page Home Quote - Personal
subBrokerBean.linesOfBusiness[P].applications[WQ].phones[OFFPG].brokerTollFree=Free broker phone call for offer page Home Quote - Personal
subBrokerBean.linesOfBusiness[P].applications[WQ].phones[OFFPG].intactPhone=Intact phone for offer page Home Quote - Personal
subBrokerBean.linesOfBusiness[P].applications[WQ].phones[OFFPG].intactTollFree=Free broker phone call for offer page Home Quote - Personal

subBrokerBean.linesOfBusiness[C].contact.emailAddress=Email Address - Commercial
subBrokerBean.linesOfBusiness[C].contact.chatId=Chat identifier - Commercial

subBrokerBean.linesOfBusiness[C].applications[AQQK].accessType=Web Access Quick Quote - Commercial
subBrokerBean.linesOfBusiness[C].applications[AQQK].assignable=Assignable Quick Quote - Commercial
subBrokerBean.linesOfBusiness[C].applications[AQQK].phones[].brokerPhone=Broker phone for Quick Quote - Commercial
subBrokerBean.linesOfBusiness[C].applications[AQQK].phones[].brokerTollFree=Free broker phone call for Quick Quote - Commercial
subBrokerBean.linesOfBusiness[C].applications[AQQK].phones[].intactPhone=Intact phone for Quick Quote - Commercial
subBrokerBean.linesOfBusiness[C].applications[AQQK].phones[].intactTollFree=Free Intact phone call for Quick Quote - Commercial
subBrokerBean.linesOfBusiness[C].applications[AQQK].phones[OFFPG].brokerPhone=Broker phone for offer page Quick Quote - Commercial
subBrokerBean.linesOfBusiness[C].applications[AQQK].phones[OFFPG].brokerTollFree=Free broker phone call for offer page Quick Quote - Commercial
subBrokerBean.linesOfBusiness[C].applications[AQQK].phones[OFFPG].intactPhone=Intact phone for offer page Quick Quote - Commercial
subBrokerBean.linesOfBusiness[C].applications[AQQK].phones[OFFPG].intactTollFree=Free broker phone call for offer page Quick Quote - Commercial

subBrokerBean.linesOfBusiness[C].applications[CCQQ].accessType=Web Access Quick Quote - P&C
subBrokerBean.linesOfBusiness[C].applications[CCQQ].assignable=Assignable Quick Quote - P&C
subBrokerBean.linesOfBusiness[C].applications[CCQQ].phones[].brokerPhone=Broker phone for Quick Quote - P&C
subBrokerBean.linesOfBusiness[C].applications[CCQQ].phones[].brokerTollFree=Free broker phone call for Quick Quote - P&C
subBrokerBean.linesOfBusiness[C].applications[CCQQ].phones[].intactPhone=Intact phone for Quick Quote - P&C
subBrokerBean.linesOfBusiness[C].applications[CCQQ].phones[].intactTollFree=Free Intact phone call for Quick Quote - P&C
subBrokerBean.linesOfBusiness[C].applications[CCQQ].phones[OFFPG].brokerPhone=Broker phone for offer page Quick Quote - P&C
subBrokerBean.linesOfBusiness[C].applications[CCQQ].phones[OFFPG].brokerTollFree=Free broker phone call for offer page Quick Quote - P&C
subBrokerBean.linesOfBusiness[C].applications[CCQQ].phones[OFFPG].intactPhone=Intact phone for offer page Quick Quote - P&C
subBrokerBean.linesOfBusiness[C].applications[CCQQ].phones[OFFPG].intactTollFree=Free broker phone call for offer page Quick Quote - P&C


subBrokerBean.services[CLCT].contact.chatId=Chat identifier Client Centre
subBrokerBean.services[CLCT].contact.emailAddress=Email Address Client Centre
subBrokerBean.services[CLCT].application.accessType=Progam access Client Centre
subBrokerBean.services[CLCT].application.assignable=Display online Name Client Centre
subBrokerBean.services[CLCT].application.phones[].intactPhone=Phone Client Centre
subBrokerBean.services[CLCT].application.phones[].intactTollFree=Free phone call Client Centre

subBrokerBean.businessHours[1].openHour=Open hour monday
subBrokerBean.businessHours[1].closeHour=Closing hour monday
subBrokerBean.businessHours[2].openHour=Open hour tuesday
subBrokerBean.businessHours[2].closeHour=Closing hour tuesday
subBrokerBean.businessHours[3].openHour=Open hour wenesday
subBrokerBean.businessHours[3].closeHour=Closing hour wenesday
subBrokerBean.businessHours[4].openHour=Open hour thursday
subBrokerBean.businessHours[4].closeHour=Closing hour thursday
subBrokerBean.businessHours[5].openHour=Open hour friday
subBrokerBean.businessHours[5].closeHour=Closing hour friday
subBrokerBean.businessHours[6].openHour=Open hour saturday
subBrokerBean.businessHours[6].closeHour=Closing hour saturday
subBrokerBean.businessHours[7].openHour=Open hour sunday
subBrokerBean.businessHours[7].closeHour=Closing hour sunday
