tab.title=POINT OF SALE CHANGES

title=List of Point of Sale Changes
period=Period From
date.from=Period From:
date.to=Period To: 
date.format=(YYYY-MM-DD)
table.label.dateofchange=Date of change
table.label.client=Client
table.label.former.servicing.broker=Former Point of Sale
table.label.new.servicing.broker=New Point of Sale
table.label.reason.for.change=Reason for change

reasonofchange.brk_location=Broker Location
reasonofchange.brk_dissatis=Broker Dissatisfaction
reasonofchange.brk_reorg=Reassigned by
reasonofchange.sys_default=System Default Assignment
reasonofchange.exist_client=Existing Intact Client

reasonofchange.other=Other
brokerchange.office=Office

error.msg.broker.change.period.start.date=A Period Start date must be selected.
error.msg.broker.change.period.end.date=A Period End date must be selected.

#MSG500
error.msg.broker.change.future.date=The date selected cannot be in the future.
#MSG499
error.msg.broker.change.period.start.must.be.prior.or.equal.to.end.date=The "From" date selected must be less than or equal to the "To" date selected.
error.msg.broker.change.period.end.must.be.after.or.equal.to.start.date=The "End" date selected must be greater than or equal to the "From" date selected.

error.date.InvalidFormat=Invalid format
