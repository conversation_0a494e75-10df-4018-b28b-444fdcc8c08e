title.access.mangement.fsa.report = Access Management - Gestion des RTA - Rapport de téléchargement
title.access.mangement.add.fsa    = Access Management - Gestion des RTA - Ajouter nouveau tableau des RTA

title.add_fsa			 = Ajouter nouveau tableau des RTA
title.list_fsa			 = Liste de tableaux des RTA
add.table.file			 = Importer fichier de tableau des RTA
add.table.owner 		 = Propriétaire du tableau
add.table.effective 	 = Prise d'effet
add.table.return		 = Retourner à la liste des tableaux des RTA
add.button.browse		 = Parcourir
add.new.table			 = Ajouter nouveau tableau
add.table.province		 = Province
add.table.lineOfBusiness = Ligne d'affaires
add.table.select		 = Sélectionner une valeur
add.table.businessContext  = Contexte d'affaires
list.fsa.company         = Compagnie

# PROVINCE ENUM
AB = Alberta
MB = Manitoba
NL = Terre-Neuve-et-Labrador
NS = Nouvelle-Écosse
ON = Ontario
QC = Québec
YT = Yukon
BC = Colombie-Britannique
NB = Nouveau-Brunswick
NT = Territoires du Nord-Ouest
NU = Nunavut
PE = Île-du-Prince Édouard
SK = Saskatchewan

#LINE OF BUSINESS ENUM
lineOfBusiness.code.C=Assurance commerciale
lineOfBusiness.code.P=Assurance des particuliers
lineOfBusiness.code.=Assurance des particuliers

#BUSINESS CONTEXT ENUM
businessContext.code.CL=Ligne commerciale (Auto)
businessContext.code.PL=Ligne personnelle (Auto and Habit)
businessContext.code.=Ligne personnelle (Auto and Habit)
businessContext.code.CL_CCQQ=Ligne commerciale (Multirisques)

# LIST OF FSA TABLE
list.fsa.line.error		= Ligne d'erreur
list.fsa.fsa			= PDV assigné
list.fsa.tableFilename  = Nom du tableau 
list.fsa.pos			= Prise d'effet
list.fsa.startDate		= Date de traitement
list.fsa.endDate		= Date d'expiration
list.fsa.processingDate	= Date de traitement
list.fsa.province		= Province
list.fsa.lineOfBusiness	= Ligne d'affaires
list.fsa.result			= Résultat 
list.fsa.refresh        = Rafraîchir la liste
list.fsa.businessContext = Contexte d'affaires
list.fsa.company         = UW

# UPLOAD STATUS REPORT 
fsa.report.total.error 				= Nombre total d'erreurs : 
list.status.report.line.error		= Ligne<br>d'erreur
list.status.report.fsa      		= RTA<br>Code Postal
list.status.report.company     		= Code<br>Compagnie
list.status.report.effective.date	= Date<br>Effective
list.status.report.expiry.date		= Date<br>Expiration
list.status.report.origin   		= Source
list.status.report.language 		= Langue
list.status.report.message  		= Message
list.status.report.pos.nbr  		= No PDV 

FRENCH						= Français
ENGLISH						= Anglais
BOTH						= Les deux

title.status.report.failed = Rapport de téléchargement - Mise à jour du fichier a échoué
title.status.report.succeed = Rapport de téléchargement - Mise à jour du fichier réussie
 
error.validInfo			= Veuillez saisir une information valide.
error.missingInfo		= La valeur de ce champs est requis.
error.file.name.prefix	= Veuillez sélectionner un fichier avec une nomenclature qui correspond au contexte d''affaires sélectionné.
error.file.name.prefix.CL = Le nom du fichier doit commencer par CL pour l''assurance commerciale.
error.file.name.prefix.PL = Le nom du fichier doit commencer par PL pour l''assurance des particuliers.

fsa.load.status.SUCC       = Réussi
fsa.load.status.FAIL	   = Invalide
fsa.load.status.PROC       = En traitement
fsa.load.status.KILL	   = Annulé
fsa.load.status.DEFAULT	   = 

fsa.nbr.fsa.processed	= Entrées traitées
fsa.total.upload.time	= Durée de téléchargement

#MSGxxx
error.file.not.a.recognized.msg = Le fichier doit être dans le format .dat.
#MSGxxx
error.date.greater.than.today.msg =	La date sélectionnée doit être supérieure ou égale à la date d'aujourd'hui.
#MSGxxx
error.date.greater.than.effective.msg =	La date «d'expiration» sélectionnée doit être supérieure à la date «de prise d'effet» sélectionnée.
#MSGxxx
error.not.shearch.caracter.entry.msg =	Vous devez saisir au moins un caractère pour lancer une recherche.
#MSGxxx
error.shearch.caracter.not.format.msg =	Vous devez saisir le code postal au complet en suivant le format A9A9A9.
#MSGxxx
error.assigned.fsa.not.pos.msg =	Vous devez assigner un PDV et saisir une date prise d'effet lors de l'ajout d'une RTA ou code postal.
#MSGxxx
error.assigned.fsa.already.exist.msg =	Une entrée pour cette RTA, code postal ou tableau a une incorrecte date d'expiration. Lorsque deux ou plusieurs entrées existent pour une RTA, code postal ou tableau spécifique, chaque entrée, autre que la plus récente, doit expirer le jour avant la date de prise d'effet de l'entrée subséquente. Veuillez ajouter / corriger la date d'expiration de l'entrée précédente.

error.date.InvalidFormat = Format Invalide

#MSG6679
same.file.still.processing.error.msg = Un fichier de RTA est déjà en traitement pour cette province, veuillez refaire votre demande dans quelques minutes.

#MSG6515
fsa.report.message.MSG.A = Le RTA ou code postal ne correspond pas à la province.

#MSG6516
fsa.report.message.MSG.B = Le format de RTA ou de code postal est invalide.

#MSG6517
fsa.report.message.MSG.C = Au moins un champ obligatoire est manquant.

#MSG6518
fsa.report.message.MSG.D = Le défaut est en erreur.

#MSG6519
fsa.report.message.MSG.E = Le PDV ou son propriétaire n'existe pas dans la table des courtiers

#MSG6520
fsa.report.message.MSG.F = Le PDV n'est pas QI, QIB ou QB

#MSG6521
fsa.report.message.MSG.G = Le PDV n'est pas défini correctement dans WebZone

#MSG6522
fsa.report.message.MSG.H = Il y a un chevauchement de dates pour ce RTA / code postal.

#MSG6523  The Company not match the province.  
fsa.report.message.MSG.I = La compagnie ne correspond pas à la province.

#MSG6524  Expiry date must be greater than effective date  
fsa.report.message.MSG.J = La date d'expiration doit être supérieure à la date effective.

#MSG6525  Origin is invalid  
fsa.report.message.MSG.K = L'origine est invalide.

#MSG6526  Language is invalid  
fsa.report.message.MSG.L = La langue est invalide.

#MSG6527  Record format is incorrect 
fsa.report.message.MSG.M = Le format de l'enregistrement est incorrect

#MSG6528  Technical problem : 
fsa.report.forced.cancel.msg1 			= Un problème technique a dû forcer l'annulation du téléchargement RTA suivant :
fsa.report.forced.cancel.load.id 		= LOAD ID : 
fsa.report.forced.cancel.file.name 		= NOM DU FICHIER :    
fsa.report.forced.cancel.process.date   = DATE :                           
fsa.report.forced.cancel.msg2 			= Veuillez nous aviser si le problème persiste.

#MSG27480  Application Mode is invalid
fsa.report.message.MSG.N  = Le code de l'application est invalide. "Application Mode".

#MSG6550 
fsa.report.message.MSG.O = Le RTA / Code postal n'existe pas dans Poste Canada.

fsa.report.message.MSG.P = Seulement le code postal ou le RTA est requis.

#MSG27481  Validation between origin web site and application mode
fsa.report.message.MSG.Q  = La validation entre l'origine du site WEB et l'application est invalide.

fsa.report.message.MSG.DEFAULT = 

fsa.add.invalid.answer.field = Veuillez répondre à la question ou corriger la réponse.