#
# ----------------------------------------------------------
# This file will contains labels common to both provinces.
# ----------------------------------------------------------

# Common buttons
# ----------------------------------------------------------
button.add               = Add
button.back              = Back
button.cancel            = Cancel
button.close             = Close
button.confirm           = Confirm
button.continue          = Continue &gt;
button.signout           = Sign Out
button.modify            = Modify
button.no                = No
button.ok                = Ok
button.print			 = Print
button.previous          = &lt; Previous
button.remove            = Remove
button.reset             = Reset
button.return            = Return
button.save				 = Save
button.submit            = Submit
button.yes               = Yes
button.quit				 = Exit
button.delete			 = Delete

common.windowTitle    = Broker Office
common.tab.quotes	  = QUOTES
common.tab.brokers	  = ACCESS MANAGEMENT
common.tab.reports	  = REPORTS
common.tab.subbroker  = MANAGE POINT OF SALE (POS)
common.tab.account    = MANAGE USERS
common.tab.fsa	      = MANAGE FSA'S


global.close          = Close
global.default.select = Select ...
global.yes            = Yes
global.no             = No
global.true			  = Yes
global.false		  = No
global.female         = Female
global.male           = Male

logout=You have been successfully logged out.
logout.link=Click here to login again

# Error page
# ----------------------------------------------------------
global.copyright.footer   = Intact Insurance Company - All rights reserved.

# Icons alt text
# ----------------------------------------------------------
help.alt = Help

# Validation error
# ----------------------------------------------------------
validation.error.title=*Please verify the field(s) annotated in red.

# Error page (error.jsp)
# ----------------------------------------------------------
error.title=Technical Error
error.line.1=We are currently experiencing technical difficulties that prevent us from continuing. We will try to rectify the problem as soon as possible. Please try again later.
error.line.2=Thank-you for your understanding.

# Session expired page (sessionExpired.jsp)
# ----------------------------------------------------------
sessionExpired.title=Session Expired
sessionExpired.message=Your session is expired.

# Language
# ----------------------------------------------------------
suffix = _en
language_full = english
language.english = EN
language.french = FR
language.switch = Français
en=English
fr=French

# Data scroller
# ----------------------------------------------------------
ds.header.display=Display
ds.header.perPage=per page
ds.footer.pageOf=Page {0} of {1}
ds.footer.next=Next >>
ds.footer.previous=<< Previous
ds.footer.first=<<
ds.footer.last=>>


# Quote status
# ----------------------------------------------------------
QUOTE_INCOMPLETE=Quote Incomplete
QUOTE_COMPLETE=Quote Complete
BIND_INCOMPLETE=Purchase Incomplete
BOUND=Purchase Complete
EXPIRED=Expired
UPLOADED=Uploaded
UPLOADED_ACCEPTED=Accepted
UPLOADED_REFUSED=Refused

global.image.relative=/image/en
global.image=/image/
form.button.search=Search
form.button.update=Update
form.button.updateAll=Update all Points of Sales
form.button.cancel=Cancel
form.button.back=Back
form.button.reassign=Reassign Quotes

ONTARIO	= Ontario
QUEBEC 	= Quebec
ALBERTA = Alberta
BRITISH_COLUMBIA = British Columbia
MANITOBA = Manitoba
NEW_BRUNSWICK = New Brunswick
NEWFOUNDLAND_AND_LABRADOR = Newfoundland And Labrador 
NOVA_SCOTIA = Nova Scotia
PRINCE_EDWARD_ISLAND = Prince Edward Island
SASKATCHEWAN = Saskatchewan
NORTHWEST_TERRITORIES = Northwest Territories
YUKON = Yukon
NUNAVUT = Nunavut
region	= Region

list.ONTARIO  = 6 - HALIFAX (daily halcion)
list.QUEBEC   = A - INTACT Québec
list.ALBERTA  = 3 - Western Union (daily halcion)

month.0=January
month.1=February
month.2=March
month.3=April
month.4=May
month.5=June
month.6=July
month.7=August
month.8=September
month.9=October
month.10=November
month.11=December
region	= Region

driver.type.residence.HO=Home
driver.type.residence.TN=Tenant
driver.type.residence.CO=Condo

global.alternate.waiting=Please wait.
quotes.window.title.search=Search #{0}
quotes.window.title.brokeradmin=Broker Admin

subscription.company=Underwriting Company
