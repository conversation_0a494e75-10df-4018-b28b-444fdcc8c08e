#################################################################################
# SecurityAntiCSRFFilter Config File
#
# This Filter make sure that anti CSRF token is present in the call
#
# Usage:
#       PATH|METHOD|PARAM|PARAMVALUE
#
# PATH  : Relative path from app name
# METHOD: GET, POST, HEAD, PUT, DELETE, TRACE, CONNECT, *
# PARAM : Parameter that must be in the request
# PARAMVALUE: Value of the PARAM - * = anything even nothing
#
# Example: /index.jsf|*|accountForm|*
#          Means any call to /index.jsp where the param accountForm is present
#################################################################################

#BROKER - ACCOUNT
BROKER_ACCOUNT_URL1=/index.jsf|*|accountListForm|*
BROKER_ACCOUNT_URL2=/index.jsf|*|accountForm|*
BROKER_ACCOUNT_URL3=/index.jsf|*|usersByMasterForm|*

#BROKER - FSA
BROKER_FSA_URL1=/index.jsf|*|fsaAddForm|*
BROKER_FSA_URL2=/index.jsf|*|fsaListForm|*
BROKER_FSA_URL3=/index.jsf|*|fsaModifiyForm|*
BROKER_FSA_URL4=/index.jsf|*|uploadMsgRepForm|*


#BROKER - SubBroker
BROKER_SUBBROKER_URL2=/index.jsf|*|subBrokerForm|*
BROKER_SUBBROKER_URL1=/index.jsf|*|subBrokerListForm|*

#QUOTES
QUOTES_URL1=/index.jsf|*|quoteListForm|*
QUOTES_URL2=/index.jsf|*|quoteSearchForm|*
QUOTES_URL3=/index.jsf|*|reassignForm|*


#REPORTS
REPORTS_URL1=/index.jsf|*|brokerChangesForm|*
REPORTS_URL2=/index.jsf|*|performanceMetricForm|*

#VIEW QUOTES
VIEWQUOTE_URL1=/pages/quotes/viewQuote/viewQuote.jsf|*|languageForm|*
VIEWQUOTE_URL2=/pages/quotes/viewQuote/viewQuote.jsf|*|viewQuoteForm|*
VIEWQUOTE_URL3=/pages/quotes/viewQuote/viewQuote.jsf|*|viewQuoteFormDenied|*
