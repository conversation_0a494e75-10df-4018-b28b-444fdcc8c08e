package util;

import org.primefaces.util.LangUtils;

public class StringUtil {

  public static boolean isStringValueContainsLowerCaseText(String fieldValue, String filter) {
    String filterText = (filter == null) ? null : filter.trim().toLowerCase();
    if (LangUtils.isBlank(filterText)) {
      return false;
    }
    if (LangUtils.isBlank(fieldValue)){
      return false;
    }
    return fieldValue.toLowerCase().contains(filterText);
  }

}
