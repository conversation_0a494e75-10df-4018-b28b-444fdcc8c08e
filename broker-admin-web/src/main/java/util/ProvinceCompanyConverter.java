/**
 * 
 */
package util;

import com.ing.canada.cif.domain.enums.CifCompanyEnum;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;

/**
 * Class that provides conversion functions for going from company to province and vice versa.
 * 
 * <AUTHOR>
 *
 */
public class ProvinceCompanyConverter {
	
	/**
	 * Function to convert a company String to a province String using the following rules : 
	 *  - A -> QC
	 *  - 6 -> ON
	 *  - 3 -> AB
	 *  - Default -> QC
	 * @param company
	 * TODO : remove default. An error should be managed if no company or unmanaged company is received
	 * @return The corresponding province String
	 */
	public static String convertSubBrokerCompanyToProvince(String company) {
		String province = null;
		
		if (company != null) {
			if (company.equals(CifCompanyEnum.INTACT_QC.getSubBrokerCompanyNumber())) {
				province = ProvinceCodeEnum.QUEBEC.getCode();
			} else if (company.equals(CifCompanyEnum.INTACT_HALIFAX.getSubBrokerCompanyNumber())) {
				province = ProvinceCodeEnum.ONTARIO.getCode();
			} else if (company.equals(CifCompanyEnum.INTACT_AB.getSubBrokerCompanyNumber())) {
				province = ProvinceCodeEnum.ALBERTA.getCode();
			} else {
				province = ProvinceCodeEnum.QUEBEC.getCode();
			}
		} else {
			province = ProvinceCodeEnum.QUEBEC.getCode();
		}
		
		return province;
	}
	
	/**
	 * Function to convert a province String to a company String using the following rules : 
	 *  - QC -> A
	 *  - ON -> 6
	 *  - AB -> 3
	 *  - Default -> A
	 * @param province
	 * TODO : remove default. An error should be managed if no province or unmanaged province is received
	 * @return The corresponding company String
	 */
	public static String convertProvinceToSubBrokerCompany(String province) {
		String company = null;

		if (province != null) {
			if (province.equals(ProvinceCodeEnum.QUEBEC.getCode())) {
				company = CifCompanyEnum.INTACT_QC.getSubBrokerCompanyNumber();
			} else if (province.equals(ProvinceCodeEnum.ONTARIO.getCode())) {
				company = CifCompanyEnum.INTACT_HALIFAX.getSubBrokerCompanyNumber();
			} else if (province.equals(ProvinceCodeEnum.ALBERTA.getCode())) {
				company = CifCompanyEnum.INTACT_AB.getSubBrokerCompanyNumber();
			} else {
				company = CifCompanyEnum.INTACT_QC.getSubBrokerCompanyNumber();
			}
		} else {
			company = CifCompanyEnum.INTACT_QC.getSubBrokerCompanyNumber();
		}
		
		return company;
	}
}
