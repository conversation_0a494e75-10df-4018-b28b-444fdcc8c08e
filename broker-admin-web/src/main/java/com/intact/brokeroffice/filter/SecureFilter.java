package com.intact.brokeroffice.filter;

import java.io.IOException;

import jakarta.servlet.Filter;
import jakarta.servlet.FilterChain;
import jakarta.servlet.FilterConfig;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import com.intact.brokeroffice.filter.util.SecureHttpServletRequestWrapper;
import com.intact.brokeroffice.filter.util.SecureHttpServletResponseWrapper;

public class SecureFilter implements Filter {

	@Override
	public void init(FilterConfig paramFilterConfig) throws ServletException {
	}

	@Override
	public void doFilter(ServletRequest request, ServletResponse response, Filter<PERSON>hain filterChain) throws IOException,
			ServletException {
		
		ServletRequest newRequest = request;
		ServletResponse newResponse = response;

		if (!(request instanceof SecureHttpServletRequestWrapper)) {
			newRequest = new SecureHttpServletRequestWrapper((HttpServletRequest) request);
		}
		
		if (!(response instanceof SecureHttpServletResponseWrapper)) {
			newResponse = new SecureHttpServletResponseWrapper((HttpServletResponse) response);
		}

		filterChain.doFilter(newRequest, newResponse);
	}

	@Override
	public void destroy() {
	}
}
