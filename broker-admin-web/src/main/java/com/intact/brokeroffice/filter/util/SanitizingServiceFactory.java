package com.intact.brokeroffice.filter.util;

import com.intact.tools.common.exception.FactoryException;
import com.intact.tools.common.util.Factory;
import com.intact.tools.sanitization.service.SanitizationApplicationService;

public abstract class SanitizingServiceFactory extends Factory {

	protected static String factoryKey = "com.intact.tools.sanitization.factory"; 

	public static synchronized SanitizingServiceFactory getInstance() throws FactoryException {
		return (SanitizingServiceFactory) Factory.getFactory("Sanitizing Service Factory", factoryKey, "com.intact.brokeroffice.filter.util.SpringSanitizingServiceFactory");
	}

	public abstract SanitizationApplicationService getSanitizingApplicationService(String sanitizingService) throws FactoryException;

}