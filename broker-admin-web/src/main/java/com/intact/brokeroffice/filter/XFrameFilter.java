package com.intact.brokeroffice.filter;

import org.springframework.web.filter.OncePerRequestFilter;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * Custom filter to prevent the application to be loaded in an iframe on other websites.
 */
public class XFrameFilter extends OncePerRequestFilter {

  private static final String X_FRAME_OPTIONS_HEADER = "X-Frame-Options";

  @Override
  protected void doFilterInternal(HttpServletRequest httpRequest, HttpServletResponse httpResponse,
      FilterChain filterChain) throws ServletException, IOException {
    httpResponse.setHeader(X_FRAME_OPTIONS_HEADER, "DENY");
    filterChain.doFilter(httpRequest, httpResponse);
  }
}
