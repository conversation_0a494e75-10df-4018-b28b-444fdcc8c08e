package com.intact.brokeroffice.filter;

import java.io.IOException;

import jakarta.servlet.Filter;
import jakarta.servlet.FilterChain;
import jakarta.servlet.FilterConfig;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;

/**
 * This class is used to filter out all access to any file of type XHTML and redirect the user to the index.jsf of the site
 * 
 * <AUTHOR>
 *
 */
public class FilterUTF8 implements Filter {
	/**
	 * @see jakarta.servlet.Filter#destroy()
	 */
	public void destroy() {
	}

	/**
	 * @see jakarta.servlet.Filter#init(jakarta.servlet.FilterConfig)
	 */
	public void init(FilterConfig arg0) throws ServletException {
	}
	
	/**
	 * @see jakarta.servlet.Filter#doFilter(jakarta.servlet.ServletRequest, jakarta.servlet.ServletResponse, jakarta.servlet.FilterChain)
	 */
	public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException,
			ServletException {
		
		request.setCharacterEncoding("UTF-8");       
        chain.doFilter(request, response);
	}

}
