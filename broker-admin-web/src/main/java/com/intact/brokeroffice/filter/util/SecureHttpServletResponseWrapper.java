package com.intact.brokeroffice.filter.util;

import com.intact.tools.common.exception.FactoryException;
import com.intact.tools.sanitization.exception.SanitizingServiceException;
import com.intact.tools.sanitization.service.SanitizationApplicationService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpServletResponseWrapper;
import java.util.ArrayList;
import java.util.List;

public class SecureHttpServletResponseWrapper extends HttpServletResponseWrapper {

	private static Logger logger = LoggerFactory.getLogger(SecureHttpServletResponseWrapper.class);

	private static final List<String> ignoredHeaders = new ArrayList<String>() {{
		add("ETag");
	}};

	public SecureHttpServletResponseWrapper(HttpServletResponse response) {
		super(response);
	}

	@Override
	public void addHeader(String name, String value) {
		try {
			String[] safeValues = sanitizeNameValue(name, value);
			super.addHeader(safeValues[0], safeValues[1]);
		} catch (Exception ex) {
			throw new RuntimeException("Error while adding header to the http response using the " + this + ".  The cause is :" + ex, ex);
		}
	}

	@Override
	public void setHeader(String name, String value) {
		try {
			String[] safeValues = sanitizeNameValue(name, value);
			super.setHeader(safeValues[0], safeValues[1]);
		} catch (Exception ex) {
			throw new RuntimeException("Error while setting header to the http response using the " + this + ".  The cause is :" + ex, ex);
		}
	}

	@Override
	public void addCookie(Cookie cookie) {
		try {
			String[] safeValues = sanitizeNameValue(cookie.getName(), cookie.getValue());

			Cookie newCookie = new Cookie(safeValues[0], safeValues[1]);
			newCookie.setPath(cookie.getPath());
			newCookie.setMaxAge(cookie.getMaxAge());
			String path = newCookie.getPath() != null ? newCookie.getPath() : "";
			newCookie.setPath(path);
			if (cookie.getDomain() != null) {
				newCookie.setDomain(cookie.getDomain());
			}

			newCookie.setSecure(true);
			newCookie.setHttpOnly(true);

			super.addCookie(newCookie);
		} catch (Exception ex) {
			throw new RuntimeException("Error while adding cookie to the http response using the " + this + ".  The cause is :" + ex, ex);
		}
	}

	public String toString() {
		return "Secure Http Servlet Response Wrapper";
	}

	private String[] sanitizeNameValue(String name, String value) throws FactoryException, SanitizingServiceException {
		SanitizationApplicationService sanitizer = SanitizingServiceFactory.getInstance().getSanitizingApplicationService("default");

		if (logger.isDebugEnabled()) {
			logger.debug("Adding Header in the http response using " + this + " with [name=" + name + ", value=" + value);
		}

		String[] newValues;
		if (!ignoredHeaders.contains(name)) {
			newValues = sanitizer.sanitizeValues("header", name, value);
		} else {
			newValues = new String[]{name, value};
		}

		if (logger.isDebugEnabled() && !StringUtils.equalsIgnoreCase(name, newValues[0]) || !StringUtils.equalsIgnoreCase(value, newValues[1])) {
			logger.debug("THE HTTP HEADER NAMED [" + name + "] HAS BEEN ENCODED FOR SECURITY RESTRICTIONS USING THE " + this + ".  YOU SHOULD REVISE IT IF APPLICATION DOES NOT RUN WELL.");
		}

		return newValues;
	}
}
