package com.intact.brokeroffice.viewhandler;

import org.primefaces.application.DialogViewHandler;

import jakarta.faces.application.ViewHandler;
import jakarta.faces.component.UIViewRoot;
import jakarta.faces.context.FacesContext;

public class CustomViewHandler extends DialogViewHandler {
  public CustomViewHandler(ViewHandler parent) {
    super(parent);
  }

  @Override
  public UIViewRoot restoreView(FacesContext facesContext, String viewId) {
    UIViewRoot root = super.restoreView(facesContext, viewId);
    // prevent ViewExpiredException
    if (root == null) {
      root = createView(facesContext, "/viewExpired.xhtml");
      facesContext.renderResponse();
    }
    return root;
  }

}
