package com.intact.brokeroffice.web.listener;

import org.slf4j.bridge.SLF4JBridgeHandler;

import jakarta.servlet.ServletContextEvent;
import jakarta.servlet.ServletContextListener;
import java.util.logging.Level;
import java.util.logging.Logger;


public class LoggingServletContextListener implements ServletContextListener {

  @Override
  public void contextInitialized(ServletContextEvent arg) {
    SLF4JBridgeHandler.removeHandlersForRootLogger();
    SLF4JBridgeHandler.install();
    Logger.getLogger("").setLevel(Level.FINEST);
  }

  @Override
  public void contextDestroyed(ServletContextEvent servletContextEvent) {
    // do nothing
  }
}
