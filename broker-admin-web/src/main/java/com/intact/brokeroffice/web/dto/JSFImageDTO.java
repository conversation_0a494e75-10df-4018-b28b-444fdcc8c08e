package com.intact.brokeroffice.web.dto;

import com.intact.brokeroffice.business.dto.ImageDTO;
import com.intact.brokeroffice.controller.subbrokers.SubBrokerImageHelper;
import com.intact.tools.comparison.domain.Ignored;
import org.primefaces.model.DefaultStreamedContent;
import org.primefaces.model.StreamedContent;
import org.primefaces.model.file.UploadedFile;
import org.springframework.util.MimeTypeUtils;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;

public class JSFImageDTO extends ImageDTO {

	private String context = null;
	private UploadedFile image = null;
	private byte[] previousImage = null;
	private String language = null;
	private String usage = null;
	private String type = null;

	public JSFImageDTO() {
	}

	@Ignored
	public String getContext() {
		return this.context;
	}
	public void setContext(String context) {
		this.context = context;
	}
	public UploadedFile getImage() {
		return this.image;
	}
	public void setImage(UploadedFile image) {
		if (image != null) {
			this.clear();
			this.image = image;
			//we do not need the returned value of image.getContent(), we just need to
			//read the uploaded file content and keep it in the image object (cached) for later access- see image.getContent() code
			//the uploaded content seems to be request scoped:
			//https://stackoverflow.com/questions/8875818/how-to-use-primefaces-pfileupload-listener-method-is-never-invoked-or-uploaded
			this.image.getContent();
		}
	}

	public void clearImage() {
		this.image = null;
	}

	@Ignored
	public byte[] getPreviousImage() {
		return this.previousImage;
	}
	public void setPreviousImage(byte[] previousImage) {
		this.previousImage = previousImage;
	}

	@Ignored
	public String getType() {
		return this.type;
	}

	public void setType(String type) {
		this.type = type;
	}

	@Ignored
	public String getUsage() {
		return this.usage;
	}

	public void setUsage(String usage) {
		this.usage = usage;
	}

	@Ignored
	public String getLanguage() {
		return this.language;
	}

	public void setLanguage(String language) {
		this.language = language;
	}

	public String toString() {
		return "[context=" + this.getContext() + "]";
	}

	public void clear() {
		this.setPreviousImage(null);
		this.image = null;
	}

	@Ignored
	public boolean isEmpty() {
		return this.getImage() == null && this.getPreviousImage() == null;
	}

	public void displayPreview(OutputStream stream, Object object) throws IOException {
		this.writeStream(this.getType(),
				this.getImage() != null ? this.getImage().getContent() : this.getPreviousImage(), stream);
	}

	public StreamedContent getImagePreviewAsStream() {
		String imageType = "SVG".equalsIgnoreCase(this.getType())? MimeTypeUtils.IMAGE_PNG_VALUE : this.getType();

		return DefaultStreamedContent.builder()
				.contentEncoding(StandardCharsets.UTF_8.name())
				.contentType(imageType).stream(() -> {
			try {
				ByteArrayOutputStream os = new ByteArrayOutputStream();
				this.writeStream(this.getType(),
						this.getImage() != null ? this.getImage().getContent() : this.getPreviousImage(), os);
				return new ByteArrayInputStream(os.toByteArray());
			} catch (IOException e) {
				return null;
			}
		}).build();
	}

	protected void writeStream(String type, byte[] graphic, OutputStream stream) throws IOException {
		byte[] newGraphic = graphic;

		if (graphic != null) {
			if ("SVG".equals(type)) {
				newGraphic = SubBrokerImageHelper.transcodeSvgToJpeg(graphic);
			}

			if (newGraphic != null) {
				stream.write(newGraphic);
			}
		}
	}

}
