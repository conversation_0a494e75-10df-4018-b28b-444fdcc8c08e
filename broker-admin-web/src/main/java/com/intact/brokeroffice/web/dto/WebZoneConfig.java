package com.intact.brokeroffice.web.dto;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.ing.canada.cif.domain.enums.ApplicationIdEnum;
import com.ing.canada.cif.domain.enums.LineOfBusinessEnum;

public class WebZoneConfig {

	private List<LineOfBusinessEnum> lines = null;
	private Map<LineOfBusinessEnum, List<ApplicationIdEnum>> applications = null;
	private List<ApplicationIdEnum> services = null;

	public WebZoneConfig() {
		this.setLines(new ArrayList<LineOfBusinessEnum>());
		this.setServices(new ArrayList<ApplicationIdEnum>());
		this.setApplications(new HashMap<LineOfBusinessEnum, List<ApplicationIdEnum>>());

		this.getLines().add(LineOfBusinessEnum.PERSONAL_LINE);
		this.getLines().add(LineOfBusinessEnum.COMMERCIAL_LINE);

		this.getApplications().put(LineOfBusinessEnum.PERSONAL_LINE, new ArrayList<ApplicationIdEnum>());
		this.getApplications().put(LineOfBusinessEnum.COMMERCIAL_LINE, new ArrayList<ApplicationIdEnum>());

		this.getApplications().get(LineOfBusinessEnum.PERSONAL_LINE).add(ApplicationIdEnum.AUTO_REGULAR_QUOTE);
		this.getApplications().get(LineOfBusinessEnum.PERSONAL_LINE).add(ApplicationIdEnum.AUTO_QUICKQUOTE);
		this.getApplications().get(LineOfBusinessEnum.PERSONAL_LINE).add(ApplicationIdEnum.WEB_QUOTE);
		this.getApplications().get(LineOfBusinessEnum.COMMERCIAL_LINE).add(ApplicationIdEnum.AUTO_QUICKQUOTE);
		this.getApplications().get(LineOfBusinessEnum.COMMERCIAL_LINE).add(ApplicationIdEnum.COMMERCIAL_CLIENT_QUICKQUOTE);
		
		this.getServices().add(ApplicationIdEnum.CLIENT_CENTRE);
	}

	public List<LineOfBusinessEnum> getLines() {
		return this.lines;
	}

	public void setLines(List<LineOfBusinessEnum> lines) {
		this.lines = lines;
	}

	public Map<LineOfBusinessEnum, List<ApplicationIdEnum>> getApplications() {
		return this.applications;
	}

	public void setApplications(Map<LineOfBusinessEnum, List<ApplicationIdEnum>> applications) {
		this.applications = applications;
	}

	public List<ApplicationIdEnum> getServices() {
		return this.services;
	}

	public void setServices(List<ApplicationIdEnum> services) {
		this.services = services;
	}

}
