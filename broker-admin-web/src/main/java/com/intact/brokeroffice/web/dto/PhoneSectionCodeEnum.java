package com.intact.brokeroffice.web.dto;

import java.util.HashMap;
import java.util.Map;

public enum PhoneSectionCodeEnum {

	DEFAULT(""), 
	OFFER("OFFPG");
	
	
	private static Map<String, PhoneSectionCodeEnum> sectionCodes = null;

	private String code = null;

	private PhoneSectionCodeEnum(String code) {
		this.setCode(code);
	}

	public String getCode() {
		return this.code;
	}

	protected void setCode(String code) {
		this.code = code;
	}
	
	public static PhoneSectionCodeEnum fromUsage(String section) {
		if (PhoneSectionCodeEnum.sectionCodes == null) {
			Map<String, PhoneSectionCodeEnum> newUsages = new HashMap<String, PhoneSectionCodeEnum>();

			for (PhoneSectionCodeEnum temp : PhoneSectionCodeEnum.values()) {
				newUsages.put(temp.getCode(), temp);
			}

			PhoneSectionCodeEnum.sectionCodes = newUsages;
		}

		return PhoneSectionCodeEnum.sectionCodes.get(section);
	}
}
