package com.intact.brokeroffice.converter;

//import org.apache.myfaces.custom.calendar.HtmlCalendarRenderer;

import jakarta.faces.application.FacesMessage;
import jakarta.faces.component.UIComponent;
import jakarta.faces.context.FacesContext;
import jakarta.faces.convert.ConverterException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.regex.Pattern;

public class CalendarConverter {}
//		extends HtmlCalendarRenderer.CalendarDateTimeConverter {
//
//	private static Pattern DATE_REGEXP = Pattern.compile("^(20)\\d\\d-(0[1-9]|1[012])-(0[1-9]|[12][0-9]|3[01])$");
//
//	@Override
//	public Object getAsObject(FacesContext context, UIComponent component, String value) {
//
//		Object objDate = super.getAsObject(context, component, value);
//		if (objDate != null) {
//			SimpleDateFormat simpleDateFormat = new SimpleDateFormat((String) component.getAttributes().get("pattern"));
//			if (!DATE_REGEXP.matcher(simpleDateFormat.format((Date) objDate)).matches()) {
//				throw new ConverterException(new FacesMessage());
//			}
//		}
//
//		return objDate;
//	}
//
//	@Override
//	public String getAsString(FacesContext context, UIComponent component, Object value) {
//		return super.getAsString(context, component, value);
//	}
//}
