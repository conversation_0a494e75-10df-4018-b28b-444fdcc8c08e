package com.intact.brokeroffice.converter;

import jakarta.faces.component.UIComponent;
import jakarta.faces.context.FacesContext;
import jakarta.faces.convert.Converter;

public class VehicleNameConverter implements Converter {	
	
	private int nameLength ;
		
	public int getNameLength() {
		return this.nameLength;
	}

	public void setNameLength(int namelength) {
		this.nameLength = namelength;
	}

	/* (non-Javadoc)
	 * @see jakarta.faces.convert.Converter#getAsObject(jakarta.faces.context.FacesContext, jakarta.faces.component.UIComponent, java.lang.String)
	 */
	public Object getAsObject(FacesContext arg0, UIComponent arg1, String arg2) {
		// Nothing to implement here
		return null;
	}

	/* (non-Javadoc)
	 * @see jakarta.faces.convert.Converter#getAsString(jakarta.faces.context.FacesContext, jakarta.faces.component.UIComponent, java.lang.Object)
	 */
	public String getAsString(FacesContext arg0, UIComponent arg1, Object arg2) {
		String str = String.valueOf(arg2);
		this.setNameLength(22);
		if(str.length() > this.nameLength){
			return str.substring(0, this.nameLength) + "...";
		}
		return str;
	}
}
