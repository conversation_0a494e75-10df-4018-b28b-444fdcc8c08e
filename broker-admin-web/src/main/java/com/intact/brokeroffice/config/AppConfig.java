package com.intact.brokeroffice.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import org.springframework.context.annotation.PropertySources;
import org.springframework.core.annotation.Order;

@Order(0)
@Configuration
@PropertySources(
    {
        @PropertySource("classpath:plp-services.properties"),
        @PropertySource("classpath:brm-services.properties")
    }
)
public class AppConfig {

}
