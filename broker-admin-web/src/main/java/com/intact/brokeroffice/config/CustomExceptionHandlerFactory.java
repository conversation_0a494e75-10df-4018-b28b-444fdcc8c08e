package com.intact.brokeroffice.config;

import jakarta.faces.context.ExceptionHandler;
import jakarta.faces.context.ExceptionHandlerFactory;


public class CustomExceptionHandlerFactory extends ExceptionHandlerFactory {
    private final ExceptionHandlerFactory wrapped;

    public CustomExceptionHandlerFactory(ExceptionHandlerFactory wrapped) {
        this.wrapped = wrapped;
    }

    public ExceptionHandler getExceptionHandler() {
        return new CustomExceptionHandler(this.wrapped.getExceptionHandler());
    }

    public ExceptionHandlerFactory getWrapped() {
        return this.wrapped;
    }
}
