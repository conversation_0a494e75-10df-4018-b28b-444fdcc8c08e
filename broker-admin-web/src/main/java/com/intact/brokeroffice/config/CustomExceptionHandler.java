package com.intact.brokeroffice.config;

import java.util.Iterator;
import java.util.logging.Level;
import java.util.logging.Logger;

import jakarta.faces.FacesException;
import jakarta.faces.context.ExceptionHandler;
import jakarta.faces.context.FacesContext;
import jakarta.faces.event.ExceptionQueuedEvent;

import org.primefaces.application.exceptionhandler.ExceptionInfo;
import org.primefaces.application.exceptionhandler.PrimeExceptionHandler;

public class CustomExceptionHandler extends PrimeExceptionHandler {

    private final Logger LOGGER = Logger.getLogger(this.getClass().getName());
    
    public CustomExceptionHandler(ExceptionHandler wrapped) {
        super(wrapped);
    }

    @Override
    public void handle() throws FacesException {
        FacesContext context = FacesContext.getCurrentInstance();
        if (context != null && !context.getResponseComplete()) {
            Iterable<ExceptionQueuedEvent> exceptionQueuedEvents = this.getUnhandledExceptionQueuedEvents();
            if (exceptionQueuedEvents != null && exceptionQueuedEvents.iterator() != null) {
                Iterator<ExceptionQueuedEvent> unhandledExceptionQueuedEvents = this.getUnhandledExceptionQueuedEvents().iterator();
                if (unhandledExceptionQueuedEvents.hasNext()) {
                    try {
                        Throwable throwable = ((ExceptionQueuedEvent)unhandledExceptionQueuedEvents.next()).getContext().getException();
                        unhandledExceptionQueuedEvents.remove();
                        Throwable rootCause = this.getRootCause(throwable);
                        ExceptionInfo info = this.createExceptionInfo(rootCause);
                        
                        rootCause.printStackTrace();

                        if (this.isLogException(context, rootCause)) {
                            this.logException(rootCause);
                        }

                        if (context.getPartialViewContext().isAjaxRequest()) {
                            this.handleAjaxException(context, rootCause, info);
                        } else {
                            this.handleRedirect(context, rootCause, info, false);
                        }
                    } catch (Exception var7) {
                        LOGGER.log(Level.SEVERE, "Could not handle exception!", var7);
                    }
                }

                while(unhandledExceptionQueuedEvents.hasNext()) {
                    unhandledExceptionQueuedEvents.next();
                    unhandledExceptionQueuedEvents.remove();
                }
            }

        }
    }
    
}
