package com.intact.brokeroffice.controller.subbrokers;

import java.util.Comparator;
import java.util.HashMap;
import java.util.Map;

import com.ing.canada.cif.domain.enums.ApplicationIdEnum;

public class ApplicationComparator implements Comparator<String> {
	
	private static Map<String, Integer> values = null;
	
	public ApplicationComparator() {
		if (ApplicationComparator.values == null) {
			ApplicationComparator.values = new HashMap<String, Integer>();
			ApplicationComparator.values.put(ApplicationIdEnum.AUTO_REGULAR_QUOTE.getCode(), 1);
			ApplicationComparator.values.put(ApplicationIdEnum.AUTO_QUICKQUOTE.getCode(), 2);
			ApplicationComparator.values.put(ApplicationIdEnum.WEB_QUOTE.getCode(), 3);
			ApplicationComparator.values.put(ApplicationIdEnum.COMMERCIAL_CLIENT_QUICKQUOTE.getCode(), 4);
			ApplicationComparator.values.put(ApplicationIdEnum.CLIENT_CENTRE.getCode(), 5);
		}
	}
	

	@Override
	public int compare(String object1, String object2) {
		return ApplicationComparator.values.get(object1) == ApplicationComparator.values.get(object2) ? 0 : ((ApplicationComparator.values.get(object1) < ApplicationComparator.values.get(object2)) ? -1 : 1);
	}

}
