package com.intact.brokeroffice.controller.tabs;

import org.apache.commons.lang.StringUtils;
import org.primefaces.component.tabview.Tab;
import org.primefaces.component.tabview.TabView;
import org.primefaces.event.TabChangeEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.intact.brokeroffice.controller.AntiCSRFController;
import com.intact.brokeroffice.controller.province.ProvinceController;

import jakarta.faces.component.UIComponent;
import jakarta.faces.context.FacesContext;
import java.util.Map;

/**
 * The Class SubBrokersTabController manages the SubBrokers tab.
 */
@Component
@Scope("session")
public class SubBrokersTabController extends AntiCSRFController{

	/** The brokers tab controller. */
	@Autowired
	private BrokersTabController brokersTabController;

	@Autowired
	private ProvinceController provinceController;

	private int activeTabIndex;

	/**
	 * The Enum Page.
	 */
	public static enum Page {
		LIST_SUBBROKER, MODIFY_SUBBROKER
	}

	/** The current page. */
	private Page page = Page.LIST_SUBBROKER;

	/**
	 * Gets the current page.
	 *
	 * @return the page
	 */
	public Page getPage() {
		return this.page;
	}

	/**
	 * Sets the current page.
	 *
	 * @param aPage the new page
	 */
	public void setPage(Page aPage) {
		this.page = aPage;
	}

	/**
	 * Gets the page path.
	 *
	 * @return the page path
	 */
	public String getPagePath() {

		// FIX to compensate an architectural issue between Ontario and Alberta
		String province = this.provinceController.getExtension();
		String extension = (ProvinceCodeEnum.ONTARIO.getCode().equals(province)) ? province : "";

		switch (this.page) {
		case LIST_SUBBROKER:
			return "/pages/brokers/subbroker/list" + extension + ".xhtml";
		case MODIFY_SUBBROKER:
			return "/pages/brokers/subbroker/modify.xhtml";

		default:
			return "no page";
		}

	}

	/**
	 * Gets the sub broker page title key.
	 *
	 * @return the sub broker page title key
	 */
	public String getSubBrokerPageTitleKey() {
		return "title." + this.page.name().toLowerCase();
	}

	/**
	 * List.
	 */
	public void list() {
		this.brokersTabController.subBrokers();
		this.page = Page.LIST_SUBBROKER;
	}

	/**
	 * Modify.
	 */
	public void modify() {
		this.page = Page.MODIFY_SUBBROKER;
	}

	/**
	 * Gets the checks if is modify.
	 *
	 * @return the checks if is create or modify
	 */
	public Boolean getIsModify() {
		return this.brokersTabController.isSubBrokersPage() && (this.page == Page.MODIFY_SUBBROKER);
	}

	public int getActiveTabIndex() {
		return this.activeTabIndex;
	}

	public void setActiveTabIndex(int activeTabIndex) {
		this.activeTabIndex = activeTabIndex;
	}

	public void onTabChange(TabChangeEvent event) {
		FacesContext context = FacesContext.getCurrentInstance();
		Map<String, String> params = context.getExternalContext().getRequestParameterMap();
		TabView tabView = (TabView) event.getComponent();
		String activeIndexValue = params.get(tabView.getClientId(context) + "_tabindex");

		if (StringUtils.isEmpty(activeIndexValue)){
			activeIndexValue = "0";
		}
		this.activeTabIndex = Integer.parseInt(activeIndexValue);
	}

}
