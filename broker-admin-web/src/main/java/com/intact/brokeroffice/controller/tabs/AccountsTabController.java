package com.intact.brokeroffice.controller.tabs;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.intact.brokeroffice.controller.province.ProvinceController;

/**
 * The Class AccountsTabController manages the Account tab.
 */
@Component
@Scope("session")
public class AccountsTabController {

	/** The brokers tab controller. */
	@Autowired
	private BrokersTabController brokersTabController;

	@Autowired
	private ProvinceController provinceController;

	/**
	 * The Enum Page.
	 */
	public static enum Page {
		LIST_ACCOUNT, MODIFY_ACCOUNT
	}

	/** The current page. */
	private Page page = Page.LIST_ACCOUNT;

	/**
	 * Gets the current page.
	 * 
	 * @return the page
	 */
	public Page getPage() {
		return this.page;
	}

	/**
	 * Sets the current page.
	 * 
	 * @param aPage the new page
	 */
	public void setPage(Page aPage) {
		this.page = aPage;
	}

	/**
	 * Gets the sub broker page title key.
	 * 
	 * @return the sub broker page title key
	 */
	public String getAccountPageTitleKey() {
		return "title." + this.page.name().toLowerCase();
	}

	/**
	 * List.
	 */
	public void list() {
		this.brokersTabController.accounts();
		this.page = Page.LIST_ACCOUNT;
	}

	/**
	 * Modify.
	 */
	public void modify() {
		this.page = Page.MODIFY_ACCOUNT;
	}

	/**
	 * Gets the page path.
	 * 
	 * @return the page path
	 */
	public String getPagePath() {

		// FIX to compensate an architectural issue between Ontario and Alberta
		String province = this.provinceController.getExtension();
		String extension = (ProvinceCodeEnum.ONTARIO.getCode().equals(province)) ? province : "";

		switch (this.page) {
		case LIST_ACCOUNT:
			return "/pages/brokers/account/list" + extension + ".xhtml";
		case MODIFY_ACCOUNT:
			return "/pages/brokers/account/modify.xhtml";

		default:
			return "no page";
		}
	}

	/**
	 * Gets the checks if is modify.
	 * 
	 * @return the checks if is create or modify
	 */
	public Boolean getIsModify() {
		return this.brokersTabController.isAccountsPage() && (this.page == Page.MODIFY_ACCOUNT);
	}
}
