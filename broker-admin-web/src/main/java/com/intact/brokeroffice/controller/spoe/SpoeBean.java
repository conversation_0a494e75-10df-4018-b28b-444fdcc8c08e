/**
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance 
 * Copyright (c) 2010, Intact Insurance, All rights reserved.<br>
 */
package com.intact.brokeroffice.controller.spoe;

public class SpoeBean {
	private String selectedAccessLevel;
	
	private String userId;
	
	private String selectedProvince;
	
	private String selectedCompany;

	/**
	 * @return the selectedAccessLevel
	 */
	public String getSelectedAccessLevel() {
		return this.selectedAccessLevel;
	}

	/**
	 * @param aSelectedAccessLevel the selectedAccessLevel to set
	 */
	public void setSelectedAccessLevel(String aSelectedAccessLevel) {
		this.selectedAccessLevel = aSelectedAccessLevel;
	}

	/**
	 * Gets the user id
	 * @return the user id
	 */
	public String getUserId() {
		return this.userId;
	}

	/**
	 * Sets the user id
	 * @param userId the user id to set
	 */
	public void setUserId(String userId) {
		this.userId = userId;
	}

	/**
	 * Gets the selected province
	 * @return the selected province
	 */
	public String getSelectedProvince() {
		return this.selectedProvince;
	}

	/**
	 * Sets the selected province
	 * @param aSelectedProvince the selected province to set
	 */
	public void setSelectedProvince(String aSelectedProvince) {
		this.selectedProvince = aSelectedProvince;
	}
	
	/**
	 * checks if broker access 
	 * @return the broker access
	 */
	public boolean isBrokerAccess(){
		return this.selectedAccessLevel == null || this.selectedAccessLevel.contains("WebZone-Broker-Advisors")|| this.selectedAccessLevel.contains("WebZone-Broker-Reassign");
	}
	
	/**
	 * Gets the selected company
	 * @return the selected company
	 */
	public String getSelectedCompany() {
		return this.selectedCompany;
	}
	
	/**
	 * Sets the selected company
	 * @param aSelectedCompany the selected company to set
	 */
	public void setSelectedCompany(String aSelectedCompany) {
		this.selectedCompany = aSelectedCompany;
	}
}
