package com.intact.brokeroffice.controller.fsa;

import java.util.List;

import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import com.ing.canada.cif.domain.enums.CifCompanyEnum;
import com.ing.canada.cif.domain.enums.FsaLoadStatusCodeEnum;
import com.ing.canada.plp.domain.enums.LineOfBusinessCodeEnum;
import com.intact.brokeroffice.business.common.LineOfBusinessUtils;
import com.intact.brokeroffice.business.dto.BusinessContextEnum;
import com.intact.brokeroffice.helper.FaceMessageHelper;

@Component
public class FsaValidator {

	private static final String UPLOAD_FIELD = "fsaAddForm:fsaFileUpload";
	private static final String PROVINCE_FIELD = "fsaAddForm:province";
	private static final String BUSINESS_CONTEXT_FIELD = "fsaAddForm:businessContext";

	private static final String ERR_MISSING_INFO = "error.missingInfo";
	private static final String ERR_INVALID_ANSWER = "fsa.add.invalid.answer.field";
	private static final String ERR_FILE_NAME_PREFIX = "error.file.name.prefix";
	private static final String ERR_INVALID_FILE_NAME_EXTENSION = "error.file.not.a.recognized.msg";
	private static final String ERR_SAME_FILE_IN_PROCESS = "same.file.still.processing.error.msg";

	private static final String[] VALID_FILE_EXTENSIONS = new String[] {".dat"};

	private boolean showErrorMessage = true;

	/**
	 * @param showErrorMessage the showErrorMessage to set
	 */
	public void setShowErrorMessage(boolean showErrorMessage) {
		this.showErrorMessage = showErrorMessage;
	}

	/**
	 * @return the showErrorMessage
	 */
	public boolean isShowErrorMessage() {
		return this.showErrorMessage;
	}

	/**
	 * @param file
	 * @param aProvince
	 * @return
	 */
	public boolean validate(final FsaUploadBean fsaUploadBean) {
		//BR5552  Mandatory fields for FSA table

		boolean valid = validateFile(fsaUploadBean);
		valid = valid & validateBusinessContext(fsaUploadBean.getBusinessContext());
		valid = valid & validateCompany(fsaUploadBean.getCompany());
		valid = valid & validateLineOfBusiness(fsaUploadBean.getLineOfBusiness());


		return valid;
	}

	/**
	 * BR5552  Mandatory fields for FSA table
	 * @param file
	 * @return
	 */
	private boolean validateFile(final FsaUploadBean fsaUploadBean) {
		if (fsaUploadBean.getFile() == null) {
			if (this.showErrorMessage) {
				FaceMessageHelper.addErrorMessage(UPLOAD_FIELD, ERR_INVALID_ANSWER, this);
			}
			return false;
		}

		return validateFileName(fsaUploadBean);
	}

	/**
	 * Validates if file exists, respect naming convention and contains a valid extension
	 * @param fsaUploadBean the uploaded content
	 * @return true, if the file name starts with the matching line of business prefix, and ends with a valid file extension
	 */
	private boolean validateFileName(final FsaUploadBean fsaUploadBean) {

		boolean valid = true;

		if (StringUtils.isBlank(fsaUploadBean.getFile().getFileName())) {
			if (this.showErrorMessage) {
				FaceMessageHelper.addErrorMessage(UPLOAD_FIELD, ERR_MISSING_INFO, this);
			}
			valid = false;
		} else if(!StringUtils.startsWithIgnoreCase(FilenameUtils.getName(fsaUploadBean.getFile().getFileName()), LineOfBusinessUtils.toStringValue(fsaUploadBean.getLineOfBusiness()))) {
			if (this.showErrorMessage) {
				String lineOfBusiness = LineOfBusinessUtils.toStringValue(fsaUploadBean.getLineOfBusiness());
				FaceMessageHelper.addErrorMessage(UPLOAD_FIELD, ERR_FILE_NAME_PREFIX + (StringUtils.isEmpty(lineOfBusiness) ? "" : "." + lineOfBusiness), this);
			}
			valid = false;
		} else if(!StringUtils.endsWithAny(fsaUploadBean.getFile().getFileName(), VALID_FILE_EXTENSIONS)) {
			//BR5542  User must select a valid table file
			if (this.showErrorMessage) {
				FaceMessageHelper.addErrorMessage(UPLOAD_FIELD, ERR_INVALID_FILE_NAME_EXTENSION, this);
			}
			valid = false;
		}

		return valid;
	}

	/**
	 * BR5552  Mandatory fields for FSA table
	 * @param aProvince
	 * @return true if province is valid, otherwise false
	 */
	private boolean validateProvince(String aProvince){

		if (StringUtils.isBlank(aProvince)) {
			if (this.showErrorMessage) {
				FaceMessageHelper.addErrorMessage(PROVINCE_FIELD, ERR_INVALID_ANSWER, this);
			}
			return false;
		}
		return true;
	}

	/**
	 * Mandatory field for FSA Table.
	 * @return true if the line of business is not null and different than OTHER otherwise false.
	 */
	private boolean validateLineOfBusiness(final String lineOfBusiness) {
		boolean valid = true;

		try{
			LineOfBusinessCodeEnum val = LineOfBusinessCodeEnum.valueOfCode(lineOfBusiness);

			if(val == null || val == LineOfBusinessCodeEnum.OTHER) {
				if(showErrorMessage) {
					FaceMessageHelper.addErrorMessage(BUSINESS_CONTEXT_FIELD, ERR_INVALID_ANSWER, this);
				}
				valid = false;
			}
		} catch (IllegalArgumentException e) {
			valid = false;
			if(showErrorMessage) {
				FaceMessageHelper.addErrorMessage(BUSINESS_CONTEXT_FIELD, ERR_INVALID_ANSWER, this);
			}
		}

		return valid;
	}

	/**
	 * Mandatory field for FSA Table.
	 * @return true if the line of business is not null and different than OTHER otherwise false.
	 */
	private boolean validateCompany(final String company) {
		boolean valid = true;

		try{
			CifCompanyEnum val = CifCompanyEnum.fromCompanyCode(company);

			if (val == null) {
				if(showErrorMessage) {
					FaceMessageHelper.addErrorMessage(BUSINESS_CONTEXT_FIELD, ERR_INVALID_ANSWER, this);
				}
				valid = false;
			}
		} catch (IllegalArgumentException e) {
			valid = false;
			if(showErrorMessage) {
				FaceMessageHelper.addErrorMessage(BUSINESS_CONTEXT_FIELD, ERR_INVALID_ANSWER, this);
			}
		}

		return valid;
	}

	/**
	 * Mandatory field for FSA Table.
	 * @return true if the business is not null and different than null otherwise false.
	 */
	private boolean validateBusinessContext(final String businessContext) {
		boolean valid = true;

		try{
			BusinessContextEnum val = BusinessContextEnum.valueOfCode(businessContext);

			if(val == null) {
				if(showErrorMessage) {
					FaceMessageHelper.addErrorMessage(BUSINESS_CONTEXT_FIELD, ERR_INVALID_ANSWER, this);
				}
				valid = false;
			}
		} catch (IllegalArgumentException e) {
			valid = false;
			if(showErrorMessage) {
				FaceMessageHelper.addErrorMessage(BUSINESS_CONTEXT_FIELD, ERR_INVALID_ANSWER, this);
			}
		}

		return valid;
	}


	/**
	 * CHECK IF SAME FILE FOR GIVEN PROVINCE IS NOT CURRENTLY PROCESSING
	 * BR6208  One file to process at a time
	 * <AUTHOR>
	 * @param someBeans
	 * @return
	 */
	protected boolean validateSameFileNameStatus(List<FsaLoadBean> someBeans){

		for(FsaLoadBean anFsaLoadBean: someBeans){
			if(FsaLoadStatusCodeEnum.PROCESSING.equals(anFsaLoadBean.getStatus())){
				FaceMessageHelper.addErrorMessage(UPLOAD_FIELD, ERR_SAME_FILE_IN_PROCESS, this);
				return false;
			}
		}
		return true;
	}
}
