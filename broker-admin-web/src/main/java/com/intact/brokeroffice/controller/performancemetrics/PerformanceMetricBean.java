package com.intact.brokeroffice.controller.performancemetrics;

import java.math.BigDecimal;
import java.math.RoundingMode;

import com.ing.canada.plp.report.performancemetric.PerformanceMetricInfo;

public class PerformanceMetricBean {
	
	private String brokerName;
	private String subBrokerName;
	private BigDecimal nbrOfQuotesIncomplete;
	private BigDecimal nbrOfQuotesComplete;	
	
	private BigDecimal nbrOfPurchaseRequestIncomplete;
	private BigDecimal nbrOfPurchaseRequestComplete;	
	
	private BigDecimal nbrOfUploadedQuotesAccepted;
	private BigDecimal nbrOfUploadedQuotesRefused;
	private BigDecimal nbrOfUploadedQuotesNotProceed;
	private BigDecimal nbrOfQuotesExpired;
	
	private BigDecimal nbrOfQuotesTotal;
	
	private BigDecimal nbrOfFollowUpNeverContacted;
	private BigDecimal nbrOfFollowUpContactNeeded;
	private BigDecimal nbrOfFollowUpNoContactNeeded;	
	private BigDecimal nbrOfFollowUpNoneRequired;
	private BigDecimal nbrOfFollowUpTotal;
		
	private String subBrokerNbr;
	private String brokerNbr;
	
	/**
	 * 1st PerformanceMetricBean CONSTRUCTOR
	 */
	public PerformanceMetricBean(){
		initializeValues();		
	}	
	
	
	/**
	 *	PerformanceMetricBean CONSTRUCTOR
	 *  inits the performance metric info bean with DB datas 
	 *
	 * @param anInfo the PerformanceMetricInfo from DB
	 */
	public PerformanceMetricBean(PerformanceMetricInfo anInfo){
		
		initializeValues();
		
		setBrokerName(anInfo.getMasterBrokerName());
		setSubBrokerName(anInfo.getSubBrokerName());		
		
		// QUOTES PART
		buildQuotesInfo(anInfo);
		
		// FOLLOW UP PART
		buildFollowupInfo(anInfo);
		
		setSubBrokerNbr(anInfo.getSubBrokerNbr());
		setBrokerNbr(anInfo.getMasterBrokerNbr());
					
	}
	
	
	/**
	 * BUILD QUOTE INFORMATION
	 * @param anInfo the PerformanceMetricInfo from DB
	 */
	private void buildQuotesInfo(PerformanceMetricInfo anInfo){		
		
		//QUOTES INCOMPLETE
		setNbrOfQuotesIncomplete(anInfo.getQuotesIncomplete());		
		//QUOTES COMPLETE		
		setNbrOfQuotesComplete(anInfo.getQuotesComplete());
		//QUOTES PURCHASE REQ INCOMPLETE
		//BR5295  Displays the total number of quotes having a Quote Status of Purchase Incomplete for the requested reporting period.
		setNbrOfPurchaseRequestIncomplete(anInfo.getPurchaseRequestIncomplete());		
		//QUOTES PURCHASE REQ COMPLETE
		//BR5296  Displays the total number of quotes having a Quote Status of Purchase Complete for the requested reporting period.
		setNbrOfPurchaseRequestComplete(anInfo.getPurchaseRequestComplete());		
		//QUOTES UPLOADED NOT PROCEED
		// BR5297  Displays the total number of quotes having a Quote Status of Uploaded for the requested reporting period.
		setNbrOfUploadedQuotesNotProceed(anInfo.getUploadedQuotesNotProceed());	
		//QUOTES UPLOADED ACCEPTED
		//BR5298  Displays the total number of quotes having a Quote Status of Accepted for the requested reporting period.
		setNbrOfUploadedQuotesAccepted(anInfo.getUploadedQuotesAccepted());
		//QUOTES UPLOADED REFUSED
		//BR5299  Displays the total number of quotes having a status of Refused for the requested reporting period.
		setNbrOfUploadedQuotesRefused(anInfo.getUploadedQuotesRefused());		
		//QUOTES EXPIRED
		//BR5300  Displays the total number of quotes having a Quote Status of Expired for the requested reporting period.
		setNbrOfQuotesExpired(anInfo.getQuotesExpired());
		
		//QUOTES TOTAL
		//BR5301  Displays the row Total for all entries in field Nos. 7 14.: 
		setNbrOfQuotesTotal(anInfo.getQuotesTotal());
	}
	
	/**
	 * BUILD FOLLOWUP NUMBER INFORMATION
	 * 
	 * @param anInfo the PerformanceMetricInfo from DB
	 * 
	 * 
	 */
	private void buildFollowupInfo(PerformanceMetricInfo anInfo)
	{						
		//BR5302  Displays the total number of quotes having a Follow-up Status of Customer not yet contacted for the requested reporting period
		setNbrOfFollowUpNeverContacted(anInfo.getFollowUpNeverContacted());	
		// BR5303  Displays the total number of quotes having a Follow-up Status of Customer contacted but a Follow-up is required for the requested reporting period.			 
		setNbrOfFollowUpContactNeeded(anInfo.getFollowUpContactNeeded());		
		// BR5304  Displays the total number of quotes having a Follow-up Status of Customer contacted but no follow-up is required for the requested reporting period.
		setNbrOfFollowUpNoContactNeeded(anInfo.getFollowUpNoContactNeeded());
		// BR5305  Displays the row Total for all entries in field Nos. 16 18. 
		setNbrOfFollowUpNoneRequired(anInfo.getFollowUpNoneRequired());
		calculateTotalFollowup();
	}	
	
	/**
	 *  initiates the bigDecimal values to BigDecimal.ZERO value
	 *  
	 */
	public void initializeValues(){
		this.nbrOfQuotesIncomplete =  BigDecimal.ZERO;
		this.nbrOfQuotesComplete = BigDecimal.ZERO;
		
		this.nbrOfPurchaseRequestIncomplete = BigDecimal.ZERO;
		this.nbrOfPurchaseRequestComplete = BigDecimal.ZERO;		
		this.nbrOfUploadedQuotesAccepted = BigDecimal.ZERO;
		this.nbrOfUploadedQuotesRefused = BigDecimal.ZERO;
		this.nbrOfUploadedQuotesNotProceed = BigDecimal.ZERO;	
		this.nbrOfQuotesExpired = BigDecimal.ZERO;		
		this.nbrOfQuotesTotal = BigDecimal.ZERO;			
		
		this.nbrOfFollowUpNeverContacted = BigDecimal.ZERO;
		this.nbrOfFollowUpContactNeeded = BigDecimal.ZERO;
		this.nbrOfFollowUpNoContactNeeded = BigDecimal.ZERO;
		this.nbrOfFollowUpNoneRequired = BigDecimal.ZERO;
		this.nbrOfFollowUpTotal = BigDecimal.ZERO;		
	}	
	
	

	/**
	 * Gets the broker name
	 * @return (String) the broker name
	 */
	public String getBrokerName() {
		return this.brokerName;
	}

	/**
	 * Sets the broker name
	 * @param aBrokerName the brokerName to set
	 */
	public void setBrokerName(String aBrokerName) {
		this.brokerName = aBrokerName;
	}
	
	

	/**
	 * Gets the number of followUp contact needed
	 * BR5303
	 * @return BigDecimal
	 */
	public BigDecimal getNbrOfFollowUpContactNeeded() {
		return this.nbrOfFollowUpContactNeeded;
	}
	
	/**
	 * Sets the number of followUp contact needed
	 * @param aNbrOfFollowUpContactNeeded the nbrOfFollowUpContactNeeded to set
	 */
	public void setNbrOfFollowUpContactNeeded(BigDecimal aNbrOfFollowupContactNeeded) {
		this.nbrOfFollowUpContactNeeded = aNbrOfFollowupContactNeeded;
	}

	/**
	 * Gets the number of followUp never needed
	 * BR5302
	 * @return BigDecimal
	 */
	public BigDecimal getNbrOfFollowUpNeverContacted() {
		return this.nbrOfFollowUpNeverContacted;
	}

	/**
	 * Sets the number of followUp never needed
	 * @param aNbrOfFollowUpNeverContacted the nbrOfFollowUpNeverContacted to set
	 */
	public void setNbrOfFollowUpNeverContacted(BigDecimal aNbrOfFollowUpNeverContacted) {
		this.nbrOfFollowUpNeverContacted = aNbrOfFollowUpNeverContacted;
	}

	/**
	 * Gets the number of followUp no contact needed
	 * BR5304
	 * @return BigDecimal
	 */
	public BigDecimal getNbrOfFollowUpNoContactNeeded() {
		return this.nbrOfFollowUpNoContactNeeded;
	}

	/**
	 * Sets the number of followUp no contact needed
	 * @param aNbrOfFollowupNoContactNeeded the nbrOfFollowUpNoContactNeeded to set
	 */
	public void setNbrOfFollowUpNoContactNeeded(BigDecimal aNbrOfFollowupNoContactNeeded) {
		this.nbrOfFollowUpNoContactNeeded = aNbrOfFollowupNoContactNeeded;
	}
	
	/**
	 * Gets the number of followUp none required
	 * @return BigDecimal
	 */
	public BigDecimal getNbrOfFollowUpNoneRequired() {
		return this.nbrOfFollowUpNoneRequired;
	}

	/**
	 * Sets the number of followUp none required
	 * @param aNbrOfFollowupNoneRequired the nbrOfFollowUpNoneRequired to set
	 */
	public void setNbrOfFollowUpNoneRequired(BigDecimal aNbrOfFollowupNoneRequired) {
		this.nbrOfFollowUpNoneRequired = aNbrOfFollowupNoneRequired;
	}

	/**
	 * Gets the total number of followUp
	 * BR5305
	 * @return BigDecimal
	 */
	public BigDecimal getNbrOfFollowUpTotal() {
		return this.nbrOfFollowUpTotal;
	}

	/**
	 * Sets the total number of followUp
	 * @param aNbrOfFollowupTotal the nbrOfFollowUpTotal to set
	 */
	public void setNbrOfFollowUpTotal(BigDecimal aNbrOfFollowupTotal) {
		this.nbrOfFollowUpTotal = aNbrOfFollowupTotal;
	}

	/**
	 * Gets the number of purchase request complete
	 * BR5296
	 * @return BigDecimal
	 */
	public BigDecimal getNbrOfPurchaseRequestComplete() {
		return this.nbrOfPurchaseRequestComplete;
	}

	/**
	 * Sets the number of purchase request complete
	 * @param aNbrOfPurchaseRequestComplete the nbrOfPurchaseRequestComplete to set
	 */
	public void setNbrOfPurchaseRequestComplete(BigDecimal aNbrOfPurchaseRequestComplete) {
		this.nbrOfPurchaseRequestComplete = aNbrOfPurchaseRequestComplete;
	}

	/**
	 * Sets the number of purchase request incomplete
	 * BR5295
	 * @return BigDecimal 
	 * 
	 */
	public BigDecimal getNbrOfPurchaseRequestIncomplete() {
		return this.nbrOfPurchaseRequestIncomplete;
	}

	/**
	 * Sets the number of purchase request incomplete
	 * @param aNbrOfPurchaseRequestIncomplete the nbrOfPurchaseRequestIncomplete to set
	 */
	public void setNbrOfPurchaseRequestIncomplete(
			BigDecimal aNbrOfPurchaseRequestIncomplete) {
		this.nbrOfPurchaseRequestIncomplete = aNbrOfPurchaseRequestIncomplete;
	}

	/**
	 * Gets the number of quotes complete
	 * BR5294
	 * @return BigDecimal
	 */
	public BigDecimal getNbrOfQuotesComplete() {
		return this.nbrOfQuotesComplete;
	}

	/**
	 * Gets the number of quotes complete
	 * @param aNbrOfQuotesComplete the nbrOfQuotesComplete to set
	 */
	public void setNbrOfQuotesComplete(BigDecimal aNbrOfQuotesComplete) {
		this.nbrOfQuotesComplete = aNbrOfQuotesComplete;
	}

	/**
	 * Gets the number of quotes incomplete
	 * BR5293
	 * @return BigDecimal
	 */
	public BigDecimal getNbrOfQuotesIncomplete() {
		return this.nbrOfQuotesIncomplete;
	}

	/**
	 * Sets the number of quotes incomplete
	 * @param aNbrOfQuotesIncomplete the nbrOfQuotesIncomplete to set
	 */
	public void setNbrOfQuotesIncomplete(BigDecimal aNbrOfQuotesIncomplete) {
		this.nbrOfQuotesIncomplete = aNbrOfQuotesIncomplete;
	}

	/**
	 * Gets the number of quotes total
	 * BR5292
	 * @return BigDecimal
	 */
	public BigDecimal getNbrOfQuotesTotal() {
		return this.nbrOfQuotesTotal;
	}

	/**
	 * Sets the number of quotes total
	 * @param aNbrOfQuotesTotal the nbrOfQuotesTotal to set
	 */
	public void setNbrOfQuotesTotal(BigDecimal aNbrOfQuotesTotal) {
		this.nbrOfQuotesTotal = aNbrOfQuotesTotal;
	}

	/**
	 * Gets the number of uploaded quotes accepted
	 * BR5298
	 * @return BigDecimal
	 */
	public BigDecimal getNbrOfUploadedQuotesAccepted() {
		return this.nbrOfUploadedQuotesAccepted;
	}

	/**
	 * Sets the number of uploaded quotes accepted
	 * @param aNbrOfUploadedQuotesAccepted the nbrOfUploadedQuotesAccepted to set
	 */
	public void setNbrOfUploadedQuotesAccepted(BigDecimal aNbrOfUploadedQuotesAccepted) {
		this.nbrOfUploadedQuotesAccepted = aNbrOfUploadedQuotesAccepted;
	}

	/**
	 * Gets the number of uploaded quotes not proceed
	 * BR5297
	 * @return BigDecimal
	 */
	public BigDecimal getNbrOfUploadedQuotesNotProceed() {
		return this.nbrOfUploadedQuotesNotProceed;
	}

	/**
	 * Sets the number of uploaded quotes not proceed
	 * @param aNbrOfUploadedQuotesNotProceed the nbrOfUploadedQuotesNotProceed to set
	 */
	public void setNbrOfUploadedQuotesNotProceed(
			BigDecimal aNbrOfUploadedQuotesNotProceed) {
		this.nbrOfUploadedQuotesNotProceed = aNbrOfUploadedQuotesNotProceed;
	}

	/**
	 * Gets the number of uploaded quotes refused
	 * BR5299
	 * @return BigDecimal
	 */
	public BigDecimal getNbrOfUploadedQuotesRefused() {
		return this.nbrOfUploadedQuotesRefused;
	}

	/**
	 * Sets the number of uploaded quotes refused
	 * @param aNbrOfUploadedQuotesRefused the nbrOfUploadedQuotesRefused to set
	 */
	public void setNbrOfUploadedQuotesRefused(BigDecimal aNbrOfUploadedQuotesRefused) {
		this.nbrOfUploadedQuotesRefused = aNbrOfUploadedQuotesRefused;
	}
	
	

	/**
	 * Gets the sub broker name 
	 * @return String
	 */
	public String getSubBrokerName() {
		return this.subBrokerName;
	}

	/**
	 * Sets the sub broker name
	 * @param aSubBrokerName the subBrokerName to set
	 */
	public void setSubBrokerName(String aSubBrokerName) {
		this.subBrokerName = aSubBrokerName;
	}	
	
	/**
	 * Method for the calculation of total quote as
	 * nbrOfQuotesIncomplete + nbrOfQuotesComplete + nbrOfPurchaseRequestIncomplete + nbrOfPurchaseRequestComplete
	 * nbrOfUploadedQuotesAccepted + nbrOfUploadedQuotesRefused + nbrOfUploadedQuotesNotProceed
	 * 
	 * BR5301  Displays the row Total for all entries in field Nos. 7 14.: 
	 * 
	 */
	public void calculateTotalQuote(){		
		this.setNbrOfQuotesTotal(this.getNbrOfQuotesTotal().add(this.getNbrOfQuotesIncomplete()));
		this.setNbrOfQuotesTotal(this.getNbrOfQuotesTotal().add(this.getNbrOfQuotesComplete()));
		
		this.setNbrOfQuotesTotal(this.getNbrOfQuotesTotal().add(this.getNbrOfPurchaseRequestIncomplete()));
		this.setNbrOfQuotesTotal(this.getNbrOfQuotesTotal().add(this.getNbrOfPurchaseRequestComplete()));
		
		this.setNbrOfQuotesTotal(this.getNbrOfQuotesTotal().add(this.getNbrOfUploadedQuotesAccepted()));
		this.setNbrOfQuotesTotal(this.getNbrOfQuotesTotal().add(this.getNbrOfUploadedQuotesRefused()));
		this.setNbrOfQuotesTotal(this.getNbrOfQuotesTotal().add(this.getNbrOfUploadedQuotesNotProceed()));		
	}
	
	/**
	 * Method for the calculation of total followup as 
	 * nbrOfFollowUpContactNeeded + nbrOfFollowUpNeverContacted + nbrOfFollowUpNoContactNeeded + nbrOfFollowUpNoneRequired 
	 * 
	 */
	private void calculateTotalFollowup(){		
		this.setNbrOfFollowUpTotal(this.getNbrOfFollowUpTotal().add(this.getNbrOfFollowUpContactNeeded()));
		this.setNbrOfFollowUpTotal(this.getNbrOfFollowUpTotal().add(this.getNbrOfFollowUpNeverContacted()));
		this.setNbrOfFollowUpTotal(this.getNbrOfFollowUpTotal().add(this.getNbrOfFollowUpNoContactNeeded()));			
		this.setNbrOfFollowUpTotal(this.getNbrOfFollowUpTotal().add(this.getNbrOfFollowUpNoneRequired()));
	}

	/**
	 * Gets the number of quotes expired
	 * @return BigDecimal
	 */
	public BigDecimal getNbrOfQuotesExpired() {
		return this.nbrOfQuotesExpired;
	}

	/**
	 * Sets the number of quotes expired  
	 * @param aNbrOfQuotesExpired the nbrOfQuotesExpired
	 */
	public void setNbrOfQuotesExpired(BigDecimal aNbrOfQuotesExpired) {
		this.nbrOfQuotesExpired = aNbrOfQuotesExpired;
	}

	/**
	 * Gets the sub broker number
	 * @return the sub broker nbr
	 */
	public String getSubBrokerNbr() {
		return this.subBrokerNbr;
	}


	/**
	 * Sets the sub broker number
	 * @param aSubBrokerNbr
	 */
	public void setSubBrokerNbr(String aSubBrokerNbr) {
		this.subBrokerNbr = aSubBrokerNbr;
	}

	/**
	 * Gets the broker number
	 * @return the broker number
	 */
	public String getBrokerNbr() {
		return this.brokerNbr;
	}

	/**
	 * Sets the broker number
	 * @param aBrokerNbr the brokerNbr to set
	 */
	public void setBrokerNbr(String aBrokerNbr) {
		this.brokerNbr = aBrokerNbr;
	}
	
	/**
	 * Calculate ratio a per b
	 * @param a
	 * @param b
	 * @return BigDecimal
	 */
	public BigDecimal calculatePercentage(Integer a, Integer b)
	{		
		return (new BigDecimal(a).multiply(new BigDecimal(100))).divide(new BigDecimal(b), 1, RoundingMode.HALF_UP);		
	}	
	
}
