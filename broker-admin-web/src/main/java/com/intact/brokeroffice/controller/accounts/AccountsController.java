/**
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance
 * Copyright (c) 2010, Intact Insurance, All rights reserved.<br>
 */
package com.intact.brokeroffice.controller.accounts;

import com.intact.brokeroffice.business.accounts.IAccountsBusinessProcess;
import com.intact.brokeroffice.business.exception.BrokerServiceException;
import com.intact.brokeroffice.controller.AbstractScrollerController;
import com.intact.brokeroffice.controller.authentification.AuthentificationController;
import com.intact.brokeroffice.controller.province.ProvinceController;
import com.intact.brokeroffice.controller.tabs.AccountsTabController;
import com.intact.brokeroffice.helper.FaceMessageHelper;
import com.intact.canada.brm.domain.broker.BrokerWebOfficeAccess;
import com.intact.canada.brm.domain.profile.AccessProfile;
import com.intact.canada.brm.domain.profile.AccessProfileEnum;
import com.intact.canada.brm.domain.user.UserAccount;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.ldap.LimitExceededException;
import org.springframework.stereotype.Component;
import util.StringUtil;

import jakarta.faces.context.FacesContext;
import java.util.*;

/**
 * The AccountsController class controls the list and modification page for broker accounts
 */
@Component
@Scope("session")
public class AccountsController extends AbstractScrollerController {

	private static Logger LOGGER = LoggerFactory.getLogger(AccountsController.class);

	@Autowired
	private AccountsTabController accountsTabController;

	@Autowired
	private IAccountsBusinessProcess accountsBusinessProcess;

	@Autowired
	private AccountsAdapter accountsAdapter;

	private List<AccountBean> accountBeans = new ArrayList<AccountBean>();

	private AccountBean accountBean = null;

	private String selectedUser;

	private String userNameFilter;

	private String fullUserNameFilter;

	private Map<String, String> masterBrokerMap;

	private Map<AccessProfileEnum, AccessProfile> accessProfileMap = new HashMap<AccessProfileEnum, AccessProfile>();

	@Autowired
	private AuthentificationController authentificationController;

	/** The province controller. */
	@Autowired
	private ProvinceController provinceController;

	private boolean pageAccessed = false;


	/**
	 * Gets the selected user.
	 *
	 * @return the selected user
	 */
	public String getSelectedUser() {
		return this.selectedUser;
	}

	/**
	 * Sets the selected user.
	 *
	 * @param selectedUser the new selected user
	 */
	public void setSelectedUser(String aSelectedUser) {
		this.selectedUser = aSelectedUser;
	}


	/**
	 * BR5805  Open Accounts by Owner Display in New Tab/Window
	 *
	 * BR5804  Redirect User to Accounts by Owner Display:
	 *
	 * Gets the list of accounts that have broker access only.
	 *
	 * @return the account beans
	 * @throws BrokerServiceException
	 */
	public List<AccountBean> getAccountBeans() throws BrokerServiceException {
		List<UserAccount> list = new ArrayList<UserAccount>();
		FaceMessageHelper.removeErrorMessage("accountListForm");

		// We only load this resource if we have not loaded it before
		if ((this.fullUserNameFilter != null && this.fullUserNameFilter.trim().length() >= 2) || (this.userNameFilter != null && this.userNameFilter.trim().length() >= 2)) {
			try {
				list = this.accountsBusinessProcess.getUsers(this.authentificationController.getCurrentAccountUId(),
						this.provinceController.getProvinceCode(), this.fullUserNameFilter, this.userNameFilter);
			} catch (LimitExceededException e) {
				LOGGER.warn(e.getMessage());
				FaceMessageHelper.addErrorMessage("accountListForm", "error.msg.search.accounts.limit.exceeded", this);
				return new ArrayList<>();
			}

			//this.pageAccessed = true;
			this.masterBrokerMap = this.accountsBusinessProcess.getAssignedMasterBrokers(this.provinceController.getCompanyEnumCode(), this.provinceController.getProvince(), null, null);
			this.accountBeans = new ArrayList<AccountBean>();

			if (list.size() > 0) {

				for (UserAccount userAccount : list) {

					String masterOwnersAccess = "";
					for (BrokerWebOfficeAccess brokerWebOfficeAccess : userAccount.getBrokerWebOfficeAccesses()) {

						if(this.masterBrokerMap.get(brokerWebOfficeAccess.getMasterOwnerCode())!=null) {
							if (StringUtils.isNotEmpty(masterOwnersAccess)) {
								masterOwnersAccess += "<br />";
							}
							masterOwnersAccess += "<a href='pages/brokers/account/usersByMaster.jsf?selectedOwnerNo=" + brokerWebOfficeAccess.getMasterOwnerCode() + "' target='_blank'>";
							masterOwnersAccess += this.masterBrokerMap.get(brokerWebOfficeAccess.getMasterOwnerCode()) + " (" +brokerWebOfficeAccess.getMasterOwnerCode() + ")";
							masterOwnersAccess += "</a>";
						}
					}

					this.accountBeans.add(new AccountBean(userAccount.getUid(), userAccount.getName(),  masterOwnersAccess));

				}
			}
		}
		else {
			this.accountBeans = new ArrayList<AccountBean>();
		}

		return this.accountBeans;
	}

	/**
	 * This action is called when the application wants to modify a account.
	 *
	 * BR5244 - Displays a message when the point of sale has been successfully modified
	 *
	 * BR5829 - Displays a message when the point of sale has been successfully modified
	 *
	 * @throws Exception the exception
	 */
	public void modify() throws Exception {
		if (AccountsTabController.Page.MODIFY_ACCOUNT
				.equals(this.accountsTabController.getPage())) {
			UserAccount userAccount = this.accountsBusinessProcess.findByUId(this.selectedUser);
			this.accountsAdapter.loadModel(userAccount,
										   this.accountBean,
										   this.provinceController.getCompanyEnumCode(),
										   this.accessProfileMap);
			this.accountsBusinessProcess.update(userAccount);
		}
		listPageWithModificationNotification();
	}


	/**
	 * BR5268  This message is displayed when the User has been successfully modified
	 *
	 *
	 *
	 *
	 *
	 * This action is called when the application wants to display the modify a account page.
	 * @throws BrokerServiceException
	 */
	public void modifyPage() throws BrokerServiceException {

		UserAccount userAccount = this.accountsBusinessProcess.findByUId(this.selectedUser);

		List<MasterBrokerBean> masterBrokerBeans = new ArrayList<MasterBrokerBean>();

		for (String key : this.masterBrokerMap.keySet()) {
			String value = this.masterBrokerMap.get(key);
			MasterBrokerBean masterBrokerBean = new MasterBrokerBean(key, value);
			masterBrokerBeans.add(masterBrokerBean);
		}

		List<AccessProfile >accessProfileList = this.accountsBusinessProcess.findAccessProfilesByName(AccessProfileEnum.PL, AccessProfileEnum.IRCA);
		this.accessProfileMap.clear();

		List<AccessProfileBean>  accessProfileBeans = new ArrayList<AccessProfileBean>();
		for (AccessProfile ap : accessProfileList) {
			AccessProfileBean accessProfileBean = new AccessProfileBean();
			accessProfileBean.setProfile(ap.getName());
			accessProfileBeans.add(accessProfileBean);
			this.accessProfileMap.put(ap.getName(), ap);
		}

		this.accountBean = new AccountBean();
		this.accountBean.setMasterBrokerBeans(masterBrokerBeans);
		this.accountBean.setAccessProfileBeans(accessProfileBeans);

		this.accountsAdapter.loadForm(userAccount, this.accountBean);

		this.accountsTabController.modify();
	}

	/**
	 * this action is called from the modify an account page when clicking on return to the list of account page.
	 */
	public void listPage() {
		this.setScrollerPerPage(10);
		this.setScrollerPage(1);
		this.accountBean = null;
		this.pageAccessed = true;
		//this.userNameFilter = null;
		//this.fullUserNameFilter = null;
		//this.accountBeans = null;
		this.accountsTabController.list();
	}

	/**
	 * This is called following a modification to a account, the accountBean is not cleared so we can display a message
	 * to the user about what as been modified.
	 */
	public void listPageWithModificationNotification() {
		//this.accountBeans = null;
		this.pageAccessed = true;
		this.accountsTabController.list();
	}

	/**
	 * Gets the account bean (this is the bean used to display the modify an account page).
	 *
	 * @return the accountBean
	 */
	public AccountBean getAccountBean() {
		return this.accountBean;
	}

	/**
	 * AccountBean Comparator
	 *
	 */
	class AccountComparator implements Comparator<AccountBean> {
		  public int compare(AccountBean o1, AccountBean o2) {
			  if (o1 == null || o1.getUserName() == null) {
				  return 1;
			  } else if (o2 == null || o2.getUserName() == null) {
				  return -1;
			  }

			  return - (o1.getUserName().toUpperCase()).compareTo(o2.getUserName().toUpperCase());
		  }
	}

	public boolean isPageAccessed() {
		return this.pageAccessed;
	}

	public void setPageAccessed(boolean aPageAccessed) {
		this.pageAccessed = aPageAccessed;
	}

	public String getUserNameFilter() {
		return userNameFilter;
	}

	public void setUserNameFilter(String userNameFilter) {
		this.userNameFilter = userNameFilter;
	}

	public String getFullUserNameFilter() {
		return fullUserNameFilter;
	}

	public void setFullUserNameFilter(String fullUserNameFilter) {
		this.fullUserNameFilter = fullUserNameFilter;
	}

//	TODO: remove once all usages are replaced
	public boolean filterUserName(Object current) {
		AccountBean aAccountBean = (AccountBean)current;
        if (StringUtils.isBlank(this.userNameFilter) || StringUtils.isBlank(aAccountBean.getUserName())) {
            return true;
        }

        return aAccountBean.getUserName().toLowerCase().contains(this.userNameFilter.toLowerCase());
    }

	//	TODO: remove once all usages are replaced
	public boolean filterFullUserName(Object current) {
		AccountBean aAccountBean = (AccountBean)current;
        if (StringUtils.isBlank(this.fullUserNameFilter) || StringUtils.isBlank(aAccountBean.getUserFullName())) {
            return true;
        }

        return aAccountBean.getUserFullName().toLowerCase().contains(this.fullUserNameFilter.toLowerCase());
    }

	public String getLanguage() {
		return FacesContext.getCurrentInstance().getViewRoot().getLocale().getLanguage();
	}

	public void setLanguage(String language) {}

	public boolean filterUserName(String aUserName, String filterText, Locale locale) {
		return StringUtil.isStringValueContainsLowerCaseText(aUserName, filterText);
	}

	public boolean filterFullUserName(String aFullUserName, String filterText, Locale locale) {
		return StringUtil.isStringValueContainsLowerCaseText(aFullUserName, filterText);
	}

}
