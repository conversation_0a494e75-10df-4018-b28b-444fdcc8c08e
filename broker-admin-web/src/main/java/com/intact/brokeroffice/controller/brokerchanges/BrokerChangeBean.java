package com.intact.brokeroffice.controller.brokerchanges;

import java.util.Date;

import org.apache.commons.lang.WordUtils;

/**
 * <AUTHOR>
 *
 */
public class BrokerChangeBean {	
	
	private static final String PREFIX_BROKER_CHANGE_REASON = "reasonofchange.";
	
	private Date dateOfChange;
	private String clientFullName;	
	private String previousBrokerName;
	private String newBrokerName;
	private String reasonForChange;	
	private String previousBrokerCity;
	private String newBrokerCity;
	private String assignorUserId;
	
	/**
	 * the BrokerChangeBean Constructor 
	 * 
	 */
	
	public BrokerChangeBean(){
		//default constructor
	}
	
	
	/**
	 * the BrokerChangeBean Constructor 
	 * 
	 * @param aDateOfChange
	 * @param aClientName
	 * @param aClientFirstName
	 * @param aPreviousBrokerName
	 * @param aNewBrokerName
	 * @param aPreviousBrokerCity
	 * @param aNewBrokerCity 
	 * @param aReasonForChange
	 */
	public BrokerChangeBean(Date aDateOfChange, String aClientName, String aPreviousBrokerName, String aNewBrokerName, String aPreviousBrokerCity, String aNewBrokerCity, String aReasonForChange, String anAssignorUserId){
		
		this.setDateOfChange(aDateOfChange);
		this.setClientFullName(aClientName);		
		
		if(aPreviousBrokerName!=null){
			this.setPreviousBrokerName(WordUtils.capitalize(aPreviousBrokerName.toLowerCase()));
		}
		if(aNewBrokerName!=null){
			this.setNewBrokerName(WordUtils.capitalize(aNewBrokerName.toLowerCase()));
		}
		if(aPreviousBrokerCity!=null){
			this.setPreviousBrokerCity(WordUtils.capitalize(aPreviousBrokerCity.toLowerCase()));
		}
		if(aNewBrokerCity!=null){
			this.setNewBrokerCity(WordUtils.capitalize(aNewBrokerCity.toLowerCase()));
		}
		
		this.setReasonForChange(aReasonForChange);
		this.setAssignorUserId(anAssignorUserId);
	}	
	
	/**
	 * Gets the client full name.
	 * 
	 * BR5188  Concatenate the Policyholders First Name and Last Name.
	 * 
	 * @return the client full Name
	 */
	
	public String getClientFullName() {
		return this.clientFullName;
	}
	
	/**
	 * Sets the client full name.
	 * 
	 * @param aClientName the client full name to set
	 */
	public void setClientFullName(String aClientName) {
		this.clientFullName = aClientName;
	}
	
	/** 
	 * Gets the new broker name.
	 * 
	 * BR5283 - If quote was reassigned, displays the name 
	 * of the new point of sale reassigned by the Web Zone administrator.
	 * BR5282  If the client changed points of sale from the Offer page, 
	 * displays the name of new point of sale selected by the client.: 
	 * 
	 * @return the newBrokerName
	 */
	
	public String getNewBrokerName() {
		return this.newBrokerName;
	}
	
	/**
	 * Sets the new broker name.
	 * @param aNewBrokerName the newBrokerName to set
	 */
	
	public void setNewBrokerName(String aNewBrokerName) {
		this.newBrokerName = aNewBrokerName;
	}
	
	/**
	 * Gets the previous broker name
	 * 
	 * BR5281  Name of previously assigned point of sale.
	 * 
	 * @return the previousBrokerName
	 */
	
	public String getPreviousBrokerName() {
		return this.previousBrokerName;
	}
	
	/**
	 * Sets the previous broker name
	 * @param aPreviousBrokerName
	 */
	
	public void setPreviousBrokerName(String aPreviousBrokerName) {
		this.previousBrokerName = aPreviousBrokerName;
	}
	
	/**
	 * 
	 * Gets the reason for change
	 * 
	 * BR5285  If the client changed point of sale from the Offer page, displays the reason for the change indicated by the client.
	 * 
	 * @return the reasonForChange
	 */
	
	public String getReasonForChange() {
		return this.reasonForChange;
	}	
	
	/**
	 * Sets the reason for change
	 * @param aReasonForChange the reasonForChange to set
	 */
	public void setReasonForChange(String aReasonForChange) {
		this.reasonForChange = aReasonForChange;
	}

	/**
	 * Gets the date of change
	 * 
	 * BR5280  Displays the date the point of sale change was made.: 
	 * 
	 * @return the dateOfChange
 	 */
	
	public Date getDateOfChange() {
		return this.dateOfChange;
	}

	/**
	 * Sets the date of change.
	 * @param aDateOfChange
	 */
	public void setDateOfChange(Date aDateOfChange) {
		this.dateOfChange = aDateOfChange;
	}	
		
	/**
	 * Gets the reason of change key .
	 * BR5285 
	 * @see brokerchanges_en_CA.properties (brokerchanges_fr_CA.properties)
	 * @return String
	 */	
	public String getReasonForChangeKey(){
		if(this.reasonForChange!=null) {
			return PREFIX_BROKER_CHANGE_REASON + this.reasonForChange.toLowerCase();
		}
		return "";		
	}

	/**
	 * Gets the new broker city
	 * @return new broker city
	 */
	public String getNewBrokerCity() {
		return this.newBrokerCity;
	}

	/**
	 * Sets the new broker city
	 * @param aNewBrokerCity the new broker cityb to set
	 */
	public void setNewBrokerCity(String aNewBrokerCity) {
		this.newBrokerCity = aNewBrokerCity;
	}

	/**
	 * Gets the previous broker city
	 * BR5281  Name of previously assigned point of sale.
	 * @return the previous broker city
	 */
	public String getPreviousBrokerCity() {
		return this.previousBrokerCity;
	}

	/**
	 * Set the previous broker city
	 * @param aPreviousBrokerCity  the previous broker city to set
	 */
	public void setPreviousBrokerCity(String aPreviousBrokerCity) {
		this.previousBrokerCity = aPreviousBrokerCity;
	}

	/**
	 * Gets the assignor user id
	 * BR5286  If quote was reassigned, displays a message in the context Reassigned by .
	 * @return the assignor user id
	 */
	public String getAssignorUserId() {
		return this.assignorUserId;
	}

	/**
	 * Sets the assignor user id
	 * @param aAssignorUserId the assignor user id
	 */
	public void setAssignorUserId(String aAssignorUserId) {
		this.assignorUserId = aAssignorUserId;
	}	
	
}
