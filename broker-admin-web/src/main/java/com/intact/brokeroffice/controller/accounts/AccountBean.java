/**
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance 
 * Copyright (c) 2010, Intact Insurance, All rights reserved.<br>
 */
package com.intact.brokeroffice.controller.accounts;

import java.util.List;

/**
 * The Class AccountBean.
 */
public class AccountBean {

	//userName for uId
	private String userName;
	
	private String userFullName;

	// Will contain a sting representing the list of brokers associated to an user to be displayed in the account list page
	private String masterBrokers;
	
	/**
	 *  DE704  Available Master Brokers 
	 *  Will contain the list of masterbrokers assoicated to an account being modified in the modify an account page	
	 */
	private List<MasterBrokerBean> masterBrokerBeans;
	
	private List<AccessProfileBean> accessProfileBeans;

	//private Date effectiveDate;
	//private Date expiryDate;
	
	/**
	 * Instantiates a new account bean.
	 */
	public AccountBean() {
		//default constructor
	}

	/**
	 * Instantiates a new account bean.
	 * 
	 * @param aUserName the a user name
	 * @param aMasterBrokers the a master brokers
	 */
	public AccountBean(String aUserName, String aMasterBrokers) {
		this.userName = aUserName;
		this.masterBrokers = aMasterBrokers;
	}
	
	/**
	 * Instantiates a new account bean.
	 * 
	 * @param aUserName the a user name
	 * @param aUserFullName the user full name
	 * @param aMasterBrokers the a master brokers
	 */
	public AccountBean(String aUserName, String aUserFullName, String aMasterBrokers) {
		this.userName = aUserName;
		this.userFullName = aUserFullName;
		this.masterBrokers = aMasterBrokers;
	}
	

	/**
	 * Gets the master brokers.
	 * 
	 * @return the masterBrokers
	 */
	public String getMasterBrokers() {
		return this.masterBrokers;
	}

	/**
	 * Sets the master brokers.
	 * 
	 * @param aMasterBrokers the masterBrokers to set
	 */
	public void setMasterBrokers(String aMasterBrokers) {
		this.masterBrokers = aMasterBrokers;
	}

	/**
	 * Gets the user name.
	 * 
	 * @return the userName
	 */
	public String getUserName() {
		return this.userName;
	}

	/**
	 * Sets the user name.
	 * 
	 * @param aUserName the userName to set
	 */
	public void setUserName(String aUserName) {
		this.userName = aUserName;
	}

	/**
	 * Gets the master broker beans.
	 * 
	 * @return the masterBrokerBeans
	 */
	public List<MasterBrokerBean> getMasterBrokerBeans() {
		return this.masterBrokerBeans;
	}

	/**
	 * Sets the master broker beans.
	 * 
	 * @param aMasterBrokerBeans the masterBrokerBeans to set
	 */
	public void setMasterBrokerBeans(List<MasterBrokerBean> aMasterBrokerBeans) {
		this.masterBrokerBeans = aMasterBrokerBeans; 
	}

	/**
	 * Gets the user full name
	 * @return the user full name
	 */
	public String getUserFullName() {
		return this.userFullName;
	}

	/**
	 * Sets the user full name
	 * @param aUserFullName the user full name to set
	 */
	public void setUserFullName(String aUserFullName) {
		this.userFullName = aUserFullName;
	}

	public List<AccessProfileBean> getAccessProfileBeans() {
		return accessProfileBeans;
	}

	public void setAccessProfileBeans(List<AccessProfileBean> accessProfileBeans) {
		this.accessProfileBeans = accessProfileBeans;
	}
	
	/*
	public Date getEffectiveDate() {
		return this.effectiveDate;
	}
	public void setEffectiveDate(Date aEffectiveDate) {
		this.effectiveDate = aEffectiveDate;
	}
	public Date getExpiryDate() {
		return this.expiryDate;
	}
	public void setExpiryDate(Date aExpiryDate) {
		this.expiryDate = aExpiryDate;
	}*/
	
}
