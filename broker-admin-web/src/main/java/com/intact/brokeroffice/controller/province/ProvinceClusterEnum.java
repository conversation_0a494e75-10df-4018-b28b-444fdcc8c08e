package com.intact.brokeroffice.controller.province;

import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;

public enum ProvinceClusterEnum {
	
	ALL("ALL",
			ProvinceCodeEnum.QUEBEC,
			ProvinceCodeEnum.ONTARIO,
			ProvinceCodeEnum.ALBERTA),
	WEST("WEST", 
			ProvinceCodeEnum.ALBERTA,
			ProvinceCodeEnum.BRITISH_COLUMBIA,
			ProvinceCodeEnum.MANITOBA,
			ProvinceCodeEnum.SASKATCHEWAN,
			ProvinceCodeEnum.NORTHWEST_TERRITORIES,
			ProvinceCodeEnum.NUNAVUT,
			ProvinceCodeEnum.YUKON),
	CAR("CAR", 
			ProvinceCodeEnum.ONTARIO,
			ProvinceCodeEnum.PRINCE_EDWARD_ISLAND,
			ProvinceCodeEnum.NEW_BRUNSWICK,
			ProvinceCodeEnum.NOVA_SCOTIA,
			ProvinceCodeEnum.NEWFOUNDLAND_AND_LABRADOR);
	
	private ProvinceCodeEnum[] provinces;
	private String code;
	
	private ProvinceClusterEnum(String code, ProvinceCodeEnum ... provinces) {
		this.code = code;
		this.provinces = provinces;
	}
	
	public  ProvinceCodeEnum[] getProvinces() {
		return this.provinces;
	}
	
	public String getCode() {
		return this.code;
	}

}
