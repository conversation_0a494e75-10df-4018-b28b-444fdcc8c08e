package com.intact.brokeroffice.controller.tabs;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.intact.brokeroffice.controller.AntiCSRFController;

/**
 * The Class FsaTabController manages the FSA tab.
 */
@Component
@Scope("session")
public class FsaTabController extends AntiCSRFController{

	/**
	 * The Enum Page.
	 */
	public static enum Page {
		LIST_FSA, ADD_FSA
	}

	/** The current page. */
	private Page page = Page.LIST_FSA;

	/**
	 * Gets the current page.
	 * 
	 * @return the page
	 */
	public Page getPage() {
		return this.page;
	}

	/**
	 * Sets the current page.
	 * 
	 * @param aPage the new page
	 */
	public void setPage(Page aPage) {
		this.page = aPage;
	}

	/**
	 * Gets the sub broker page title key.
	 * 
	 * @return the sub broker page title key
	 */
	public String getFsaPageTitleKey() {
		return "title." + this.page.name().toLowerCase();
	}

	/*** LoadFile.
	 */
	public void add() {
		this.page = Page.ADD_FSA;
	}

	/**
	 * List.
	 */
	public void list() {
		this.page = Page.LIST_FSA;
	}	

	/**
	 * Gets the page path.
	 * 
	 * @return the page path
	 */
	public String getPagePath() {
		switch (this.page) {
		case ADD_FSA:
			return "/pages/brokers/fsa/add.xhtml";
		case LIST_FSA:
			return "/pages/brokers/fsa/list.xhtml";		
		default:
			return "no page";
		}		
	}	
}