/*
 * 
 */
package com.intact.brokeroffice.controller.tabs;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

/**
 * The Class GeneralTabController manages the global tabs.
 */
@Component
@Scope("session")
public class GeneralTabController {	
		
	/**
	 * The Enum Page.
	 */
	private static enum Page { BROKER, REPORTS }
	
	/** The current page. */
	private Page page = Page.BROKER; 

	
	/**
	 * Gets the current page.
	 * 
	 * @return the page
	 */
	public Page getPage() {
		return this.page;
	}

	/**
	 * Sets the current page.
	 * 
	 * @param aPage the new page
	 */
	public void setPage(Page aPage) {
		this.page = aPage;
	}
	
	/**
	 * Brokers.
	 */
	public void brokers(){
		this.page = Page.BROKER;
	}
	
	/**
	 * Reports.
	 */
	public void reports(){
		this.page = Page.REPORTS;
	}	
	
	/**
	 * Checks if is brokers.
	 * 
	 * @return the boolean
	 */
	public Boolean isBrokers(){
		return this.page == Page.BROKER;
	}

	/**
	 * Checks if is brokers.
	 * 
	 * @return the boolean
	 */
	public Boolean isReports(){
		return this.page == Page.REPORTS;
	}

}
