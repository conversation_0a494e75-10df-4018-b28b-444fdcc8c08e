package com.intact.brokeroffice.controller.province;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import jakarta.faces.context.FacesContext;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.web.util.WebUtils;

import com.ing.canada.cif.domain.enums.CifCompanyEnum;
import com.ing.canada.common.web.api.auth.TamTokensEnum;
import com.ing.canada.plp.domain.enums.ManufacturerCompanyCodeEnum;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.intact.brokeroffice.controller.AntiCSRFController;
import com.intact.brokeroffice.controller.enums.SessionConstantsEnum;

@Component
@Scope("session")
public class ProvinceController extends AntiCSRFController {

	private static int NUMBER_DAYS_LEFT_CIE_6 = 61;

	private static int NUMBER_DAYS_LEFT_DEFLT = 31;

	// Cookie age set to 1 month 2592000
	private static int DEFAULT_COMPANY_COOKIE_AGE = 2592000;

	private String province;
	
	private String company;

	private String extension;

	private Integer nbrOfDayThreshold;

	private ProvinceCodeEnum provinceCode;

	private List<ProvinceCodeEnum> provinces = null;
	
	private List<SubscriptionCompanyEnum> companies = null;

	private SubscriptionCompanyEnum selectedCompany;

	private static String BROKER_DEFAULT_COMPANY = "brokerDefaultCompany";
	
	private static final String COMPANY_FILTER = "filter.company";
	
	private Map<String, Map<String, String>> filters = null;

	public ProvinceController() {
		super();
		this.setFilters(new HashMap<String, Map<String, String>>());
		this.getFilters().put(ProvinceController.COMPANY_FILTER, new HashMap<String, String>());
		this.getFilters().get(ProvinceController.COMPANY_FILTER).put("A", "A");
		this.getFilters().get(ProvinceController.COMPANY_FILTER).put("6", "6");
		this.getFilters().get(ProvinceController.COMPANY_FILTER).put("3", "3");
	}
	
	public Map<String, Map<String, String>> getFilters() {
		return this.filters;
	}

	public void setFilters(Map<String, Map<String, String>> filters) {
		this.filters = filters;
	}

	/**
	 * Gets the company code saved in session attribute province BR5828 Items
	 * Displayed per Access
	 * 
	 * @return the company code
	 */
	public String getCompany() {
		if (this.company == null) {
			FacesContext context = FacesContext.getCurrentInstance();
			HttpSession session = (HttpSession) context.getExternalContext().getSession(true);

			HttpServletRequest httpServletRequest = (HttpServletRequest) context.getExternalContext().getRequest();
			
			//PM11018 - Get defaultCompany cookie when the session contains null
			if ( httpServletRequest.getSession().getAttribute(SessionConstantsEnum.COMPANY.getSessionConstant()) == null ){
				String defaultCompany = null;
				if (ProvinceController.searchForBrokerDefaultCompany(httpServletRequest) != null) {
					Cookie aCookie = ProvinceController.searchForBrokerDefaultCompany(httpServletRequest);
					defaultCompany = aCookie.getValue();
				}
				httpServletRequest.getSession().setAttribute(SessionConstantsEnum.COMPANY.getSessionConstant(),defaultCompany);
			}
						
			this.company = (String) session.getAttribute(SessionConstantsEnum.COMPANY.getSessionConstant()) == null ? CifCompanyEnum.INTACT_QC
					.getSubBrokerCompanyNumber() : (String) session.getAttribute(SessionConstantsEnum.COMPANY.getSessionConstant());
		}
		return this.company;
	}
	
	public String getProvince() {
		this.province = this.getProvinceCode().getCode();
		return this.province;
	}
	
	public void modifyCompany() throws Exception {

		FacesContext context = FacesContext.getCurrentInstance();

		Cookie newCookie = this.buildBrokerDefaultCompany(this.getSelectedCompany().getCifCompany().getSubBrokerCompanyNumber());
		HttpServletResponse response = (HttpServletResponse) context.getExternalContext().getResponse();
		response.addCookie(newCookie);

		HttpSession session = (HttpSession) context.getExternalContext().getSession(true);
		session.setAttribute(SessionConstantsEnum.COMPANY.getSessionConstant(), this.getSelectedCompany().getCifCompany().getSubBrokerCompanyNumber());

		this.reset();
	}
	

	/**
	 * Gets the subscription companies enum list
	 * @return the subscription companies enum list
	 */
	@SuppressWarnings("unchecked")
	public List<SubscriptionCompanyEnum> getCompanies() {
		if (this.companies == null) {
			FacesContext context = FacesContext.getCurrentInstance();
			HttpSession session = (HttpSession) context.getExternalContext().getSession(true);
			this.companies = new ArrayList<SubscriptionCompanyEnum>();
			
			List<String> CompaniesStr = (List<String>) session.getAttribute(SessionConstantsEnum.AVAILABLE_COMPANIES
					.getSessionConstant());
			if (CompaniesStr != null) {
				for (String aCie : CompaniesStr) {
					this.companies.add(SubscriptionCompanyEnum.fromCode(aCie));
				}
				// Display Current company
				this.selectedCompany = SubscriptionCompanyEnum.fromCode((String) session.getAttribute(SessionConstantsEnum.COMPANY
						.getSessionConstant()));
			}
		}
		return this.companies;
	}

	public SubscriptionCompanyEnum getSelectedCompany() {
		return this.selectedCompany;
	}

	public void setSelectedCompany(SubscriptionCompanyEnum selectedCompany) {
		this.selectedCompany = selectedCompany;
	}
	
	/**
	 * Sets the list of provinces
	 * 
	 * @param aProvinces the list of provinces to set
	 */
	public void setProvinces(List<ProvinceCodeEnum> aProvinces) {
		this.provinces = aProvinces;
	}
	
	/**
	 * Gets the provinces code enum
	 * 
	 * BR5791 Display Select Region List: Display the Select Region drop down list only when the user has access to two
	 * or more regions within Web Zone.
	 * 
	 * @return the province code enum
	 */
	@SuppressWarnings("unchecked")
	public List<ProvinceCodeEnum> getProvinces() {
		if (this.provinces == null) {
			FacesContext context = FacesContext.getCurrentInstance();
			HttpSession session = (HttpSession) context.getExternalContext().getSession(true);
			this.provinces = new ArrayList<ProvinceCodeEnum>();

			List<String> ProvincesStr = (List<String>) session.getAttribute(SessionConstantsEnum.AVAILABLE_PROVINCES
					.getSessionConstant());
			if (ProvincesStr != null) {
				for (String aProv : ProvincesStr) {
					this.provinces.add(ProvinceCodeEnum.valueOfCode(aProv));
				}
				// BR5793 - Display Current Region
//				this.selectedProvince = (String) session.getAttribute(SessionConstantsEnum.PROVINCE
//						.getSessionConstant());
			}
		}
		return this.provinces;
	}
	
	/**
	 * Sets the list of companies
	 * 
	 * @param aCompanies the list of companies to set
	 */
	public void setCompanies(List<SubscriptionCompanyEnum> aCompanies) {
		this.companies = aCompanies;
	}

	
	/**
	 * Gets the extension for page display BR5828 Items Displayed per Regional Access
	 * 
	 * @return the extension for xhtml page (driverON.xhtml, driver.xhtml)
	 */
	public String getExtension() {
		if (this.extension == null) {
			String aCompany = this.getCompany();
			this.extension = (CifCompanyEnum.INTACT_QC.getSubBrokerCompanyNumber().equals(aCompany) || aCompany == null) ? ""
					: aCompany;
		}
		return this.extension;
	}
	
	/**
	 * Checks if underwriting company is INTACT QUEBEC
	 * 
	 * @return boolean if company is HALIFAX
	 */
	public boolean isCompanyA() {
		return CifCompanyEnum.INTACT_QC.getSubBrokerCompanyNumber().equals(this.getCompany());
	}
	
	/**
	 * Checks if underwriting company is HALIFAX
	 * 
	 * @return boolean if company is HALIFAX
	 */
	public boolean isCompany6() {
		return CifCompanyEnum.INTACT_HALIFAX.getSubBrokerCompanyNumber().equals(this.getCompany());
	}
	
	/**
	 * Checks if underwriting company is WESTERN UNION
	 * 
	 * @return boolean if company is WESTERN UNION
	 */
	public boolean isCompany3() {
		return CifCompanyEnum.INTACT_AB.getSubBrokerCompanyNumber().equals(this.getCompany());
	}
	

	/**
	 * Gets the number of days left
	 * 
	 * @return the number of days left
	 */
	public Integer getNumberOfDaysLeft() {
		if (this.nbrOfDayThreshold == null) {
			String aCompany = this.getCompany();
			if (CifCompanyEnum.INTACT_QC.getSubBrokerCompanyNumber().equals(aCompany)) {
				this.nbrOfDayThreshold = NUMBER_DAYS_LEFT_DEFLT;
			} else if (CifCompanyEnum.INTACT_HALIFAX.getSubBrokerCompanyNumber().equals(aCompany)) {
				this.nbrOfDayThreshold = NUMBER_DAYS_LEFT_CIE_6;
			}
			// TODO number_days_AB
			else {
				this.nbrOfDayThreshold = NUMBER_DAYS_LEFT_DEFLT;
			}
		}

		return this.nbrOfDayThreshold;
	}

	/**
	 * Gets the cif company code enum
	 * 
	 * BR5828 Items Displayed per Regional Access
	 * 
	 * @return the cif company code enum based on province location
	 */
	public CifCompanyEnum getCompanyEnumCode() {
		CifCompanyEnum company = CifCompanyEnum.fromCompanyCode(this.getCompany());	
		return company == null ? CifCompanyEnum.INTACT_QC : company;
	}

	/**
	 * Gets the manufacturer company code enum
	 * 
	 * BR5828 Items Displayed per Regional Access
	 * 
	 * @return the manufacturer company code enum based on province location
	 */
	public ManufacturerCompanyCodeEnum getManufacturerCompanyCode() {
		ManufacturerCompanyCodeEnum manufacturer = ManufacturerCompanyCodeEnum.valueOfCode(getCompanyEnumCode().getSubBrokerCompanyNumber());
		
		return manufacturer == null ? ManufacturerCompanyCodeEnum.INTACT_QUEBEC_REGION : manufacturer; 
	}

	/**
	 * Gets the province code enum
	 * 
	 * @return the province code enum
	 */
	public ProvinceCodeEnum getProvinceCode() {
		if (this.provinceCode == null) {
			if (isCompanyA()) {
				this.provinceCode = ProvinceCodeEnum.QUEBEC;
			}
			else if	(isCompany6()) {
				this.provinceCode = ProvinceCodeEnum.ONTARIO;
			}
			else if (isCompany3()) {	
				this.provinceCode = ProvinceCodeEnum.ALBERTA;
			}
			else {
				this.provinceCode = ProvinceCodeEnum.QUEBEC;
			}
		}
		return this.provinceCode;
	}

	/**
	 * reset attributes
	 */
	public void reset() {
		this.extension = null;
		this.company = null;
		this.provinceCode = null;
		this.provinces = null;
		this.nbrOfDayThreshold = null;
	}
	
	/**
	 * Retrieves the broker default underwriting company
	 * 
	 * @param request
	 * @return
	 */
	public static Cookie searchForBrokerDefaultCompany(HttpServletRequest request) {
		return WebUtils.getCookie(request, BROKER_DEFAULT_COMPANY);
	}

	/**
	 * Initialize a cookie
	 * 
	 * @param theDefaultCompanySelected
	 * @return
	 */
	public Cookie buildBrokerDefaultCompany(String theDefaultCompanySelected)
			throws Exception {

		theDefaultCompanySelected = this.filterValue(theDefaultCompanySelected);

		Cookie aCookie = new Cookie(BROKER_DEFAULT_COMPANY, theDefaultCompanySelected);
		aCookie.setMaxAge(DEFAULT_COMPANY_COOKIE_AGE);
		aCookie.setPath("/");
		return aCookie;
	}

	/**
	 * Checks if upload link should be displayed
	 * 
	 * @return
	 */
	public boolean getShowUpload() {

		FacesContext context = FacesContext.getCurrentInstance();
		HttpSession session = (HttpSession) context.getExternalContext().getSession(true);

		String classicId = (String) session.getAttribute(TamTokensEnum.TAM_USER_CLASSIC_USER_ID.getTokenValue());
		String halcionId = (String) session.getAttribute(TamTokensEnum.TAM_USER_NAME_CREDENTIAL.getTokenValue());

		if (this.isCompany6() && halcionId != null) {
			return true;
		} else if (this.isCompanyA() && classicId != null) {
			return true;
		}
		else if (this.isCompany3() && halcionId != null) {
			return true;
		} else {
			return false;
		}
	}

	public String filterValue(String value) {
		return this.getFilters().get(ProvinceController.COMPANY_FILTER).get(value);
	}
}
