/**
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance 
 * Copyright (c) 2010, Intact Insurance, All rights reserved.<br>
 */
package com.intact.brokeroffice.controller.province;

/**
 * The Class MasterBrokerBean.
 */
public class ProvinceBean {

	private String name;
	private String code;

	private Boolean selected;

	/**
	 * Instantiates a new master broker bean.
	 * 
	 * @param aNumber the a number
	 * 
	 */
	public ProvinceBean(String aName, String aCode) {
		this.name = aName;
		this.code = aCode;
		
	}

	/**
	 * @return the name
	 */
	public String getName() {
		return this.name;
	}

	/**
	 * @param aName the name to set
	 */
	public void setName(String aName) {
		this.name = aName;
	}

	/**
	 * @return the selected
	 */
	public Boolean getSelected() {
		return this.selected;
	}

	/**
	 * @param aSelected the selected to set
	 */
	public void setSelected(Boolean aSelected) {
		this.selected = aSelected;
	}

	/**
	 * Gets the code
	 * @return the code
	 */
	public String getCode() {
		return this.code;
	}

	/**
	 * Sets the code
	 * @param aCode the code to set
	 */
	public void setCode(String aCode) {
		this.code = aCode;
	}	
	
}
