package com.intact.brokeroffice.controller.fsa;

import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Random;

import jakarta.faces.context.FacesContext;
import jakarta.faces.event.ActionEvent;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.ing.canada.cif.domain.IFsaLoad;
import com.ing.canada.cif.domain.impl.FsaParameters;
import com.ing.canada.cif.service.IFsaLoadService;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.intact.brokeroffice.business.common.LineOfBusinessUtils;
import com.intact.brokeroffice.business.dto.BusinessContextEnum;
import com.intact.brokeroffice.business.fsa.FsaUploadDTO;
import com.intact.brokeroffice.business.fsa.IFsaBusinessProcess;
import com.intact.brokeroffice.controller.AbstractScrollerController;
import com.intact.brokeroffice.controller.permission.PermissionController;
import com.intact.brokeroffice.controller.province.ProvinceController;
import com.intact.brokeroffice.controller.tabs.FsaTabController;
import com.intact.brokeroffice.helper.FaceMessageHelper;

/**
 * <AUTHOR>
 *
 */
@Component
@Scope("session")
@Deprecated
public class FsaController extends AbstractScrollerController {

	private static Logger logger = LoggerFactory.getLogger(FsaController.class);

	private static final String USER_SESSION_ID_LIST_MATCH_ATTRIBUTE = "SECURITY_ID_LIST_MATCH_ATTRIBUTE";

	@Autowired
	private FsaTabController fsaTabController;

	@Autowired
	private PermissionController permissionController;

	@Autowired
	private IFsaLoadService fsaLoadService;

	@Autowired
	private FsaAdapter fsaAdapter;

	@Autowired
	private FsaValidator fsaValidator;

	@Autowired
	private IFsaBusinessProcess fsaBusinessProcess;

	private List<FsaLoadBean> fsaTableBeans = null;

	@Autowired
	@Qualifier("nfs-url-dir")
	private String nsfDirectory;

	@Autowired
	@Qualifier("nfs-server-mode")
	private String nsfServerMode;

	/** The province controller. */
	@Autowired
	private ProvinceController provinceController;

	/** Instance containing the details of a newly uploaded fsa file */
	private FsaUploadBean fsaUploadBean;

	private static final String DATE_FORMAT_YYYYMMDD = "yyyyMMdd";

	private static final String DAT_FILE_EXSTENSION = ".dat";

	private SimpleDateFormat sdf = new SimpleDateFormat(DATE_FORMAT_YYYYMMDD);

	/**
	 * Obtain the {@link FsaUploadBean} for the upload of a new file.
	 *
	 * @return {@link FsaUploadBean} for the upload of a new file.
	 */
	public FsaUploadBean getFsaUploadBean() {
		return fsaUploadBean;
	}

	/**
	 * Assign the {@link FsaUploadBean} for the upload of a new file.
	 *
	 * @param fsaUploadBean
	 */
	public void setFsaUploadBean(FsaUploadBean fsaUploadBean) {
		this.fsaUploadBean = fsaUploadBean;
	}

	/**
	 * this action is called to open load fsa file screen. BR5587 Redirects the user
	 * to the Add a New FSA Table page
	 */
	public void addPage() {
		fsaUploadBean = new FsaUploadBean();
		fsaUploadBean.setCompanyItems(provinceController.getCompanies());
		fsaUploadBean.setCompany(provinceController.getCompanyEnumCode().getSubBrokerCompanyNumber());
		fsaUploadBean.setProvince(provinceController.getProvince());
		fsaUploadBean.setBusinessContextItems(Arrays.asList(BusinessContextEnum.values()));
		this.fsaTabController.add();
	}

	/**
	 * this is call to list the fsa tables BR5546 Redirects the user to the List of
	 * Tables page (Add)
	 */
	public void listPage() {
		this.setScrollerPerPage(Integer.valueOf(10));
		resetPage();
		this.fsaTabController.list();
	}

	/**
	 * BR6141 When clicked, refreshes the FSA list
	 */
	public void refreshListPage() {
		this.setScrollerPage(Integer.valueOf(1));
		this.fsaTableBeans = null;
		this.fsaTabController.list();
	}

	/**
	 * reset some parameters of controller
	 */
	private void resetPage() {
		this.setScrollerPage(Integer.valueOf(1));
		this.fsaTableBeans = null;
		this.fsaUploadBean = null;
	}

	/**
	 * FILE UPLOAD Method that call when you click on browse... BR5574 Add New FSA
	 * Table:
	 *
	 */
	public void fileUploaded() {
		if (FsaTabController.Page.ADD_FSA
				.equals(this.fsaTabController.getPage())) {
			try {
				if (this.fsaValidator.validate(this.fsaUploadBean)) {
					if (this.preUploadedProcess()) {
						resetPage();
						this.fsaTabController.list();
					}
				}
			} catch (Exception e) {
				logger.error(ExceptionUtils.getStackTrace(e));
			}
		}

	}

	/**
	 * BR5574 Add New FSA Table: PRE-UPLOAD PHASE AND VALIDATION PHASES
	 */
	private boolean preUploadedProcess() {

		Date today = new Date();

		String fileNameWithoutNbr = this.generateFileName(today, fsaUploadBean.getCompany(),
				fsaUploadBean.getBusinessContext());

		List<IFsaLoad> somefsaLoads = this.fsaLoadService.findAllSameFiles(fileNameWithoutNbr + "%");

		boolean valid = true;

		List<FsaLoadBean> someBeans = Collections.<FsaLoadBean>emptyList();

		String fileName = null;

		if (CollectionUtils.isNotEmpty(somefsaLoads)) {
			someBeans = this.fsaAdapter.loadFsaTableBeans(somefsaLoads);
			// BR6208 One file to process at a time per province
			valid = this.fsaValidator.validateSameFileNameStatus(someBeans);
		}

		if (valid) {
			// BR5818 Convert the filename to be uploaded in a standard name
			fileName = this.fsaAdapter.buildNextFileExtension(someBeans.size(), fileNameWithoutNbr);

			if (writeInFsaFile(fileName)) {
				try {

					FsaUploadDTO uploadDTO = new FsaUploadDTO();
					uploadDTO.setInputStream(this.fsaUploadBean.getFile().getInputStream());
					uploadDTO.setFilePath(generatePath());
					uploadDTO.setFileName(fileName + DAT_FILE_EXSTENSION);
					uploadDTO.setProvince(this.fsaUploadBean.getProvince());
					uploadDTO.setLineOfBusiness(this.fsaUploadBean.getLineOfBusiness());
					uploadDTO.setCompanyCode(this.fsaUploadBean.getCompany());
					uploadDTO.setApplicationId(BusinessContextEnum.valueOfCode(this.fsaUploadBean.getBusinessContext()).getApplicationId());
					// METHOD: CONTROL-M
					this.fsaBusinessProcess.executeWithoutThread(uploadDTO);

					/*
					 * METHOD : CALL DIRECT TO SP
					 * this.fsaBusinessProcess.execute(this.file.getInputStream(),
					 * this.generatePath(), fileName + DAT_FILE_EXSTENSION,
					 * this.provinceController.getProvinceCode(), todaysDate, Long.valueOf(30));***
					 */
				} catch (IOException e) {
					logger.error(e.getMessage());
					logger.info(ExceptionUtils.getStackTrace(e));
				}
			} else {
				valid = false;
			}
		}
		return valid;
	}

	/**
	 * BR5574 Add New FSA Table: Methods that generate the filename without the
	 * extension
	 *
	 * @param todaysDate
	 *            current date
	 * @param province
	 *            province to which the upload should be applied
	 * @param lineOfBusiness
	 *            line of business to which the upload should be applied
	 * @return
	 */
	private String generateFileName(Date todaysDate, String company, String businessContext) {

		return "%s_%s_%s_".formatted(businessContext, company,
				sdf.format(todaysDate));
	}

	@SuppressWarnings("unchecked")
	public String getSecurityLabel(FsaLoadBean bean) {

		FacesContext context = FacesContext.getCurrentInstance();
		HttpServletRequest request = (HttpServletRequest) context.getExternalContext().getRequest();
		HttpSession userSession = request.getSession(false);
		Random random = new Random();

		long f = (long) (random.nextDouble() * 1000000000L);

		String toReturn = "webzone." + System.currentTimeMillis() + "." + f;

		HashMap<String, String> theMap = (HashMap<String, String>) userSession.getAttribute(USER_SESSION_ID_LIST_MATCH_ATTRIBUTE);

		if (theMap == null) {
			theMap = new HashMap<String, String>();
			userSession.setAttribute(USER_SESSION_ID_LIST_MATCH_ATTRIBUTE, theMap);
		}

		theMap.put(toReturn, "" + bean.getLoadId());

		return toReturn;

	}

	/**
	 * Gets the fsa table beans BR5537 The default sort order for the list is by
	 * Effective Date, in descending order
	 *
	 * @return the fsa table beans
	 */
	public List<FsaLoadBean> getFsaTableBeans() {

		// CHECK FOR FSA LOAD TO ABORT AFTER 60 MIN
		this.fsaLoadService.killLoads(this.provinceController.getProvinceCode().getCode(), 60);

		if (CollectionUtils.isEmpty(this.fsaTableBeans)) {

			FsaParameters fsaParameters = new FsaParameters();
			fsaParameters.setCompany(this.provinceController.getCompany());
			fsaParameters
					.setLineOfBusiness(LineOfBusinessUtils.join(permissionController.getAvailableLinesOfBusiness()));

			this.fsaTableBeans = this.fsaAdapter.loadFsaTableBeans(this.fsaLoadService.getFsaTableList(fsaParameters));
		}

		return this.fsaTableBeans;
	}

	/**
	 * Sets the fsa table beans
	 *
	 * @param aFsaTableBeans
	 */
	public void setFsaTableBeans(List<FsaLoadBean> aFsaTableBeans) {
		FacesContext context = FacesContext.getCurrentInstance();
		HttpServletRequest request = (HttpServletRequest) context.getExternalContext().getRequest();

		request.getSession(false).removeAttribute(USER_SESSION_ID_LIST_MATCH_ATTRIBUTE);

		this.fsaTableBeans = aFsaTableBeans;
	}

	/**
	 * creates the list of quote status used in the UI checkboxes selection BR6110
	 * Only display provinces within the selected region
	 *
	 * BR6110 Only display provinces within the selected region Gets the province
	 * items enum
	 *
	 * @return the province items enum
	 */
	private List<ProvinceCodeEnum> getProvinceItems() {

		List<ProvinceCodeEnum> provinceItems = new ArrayList<ProvinceCodeEnum>();

		for (ProvinceCodeEnum aProvince : ProvinceCodeEnum.values()) {
			// Select only canadian provinces where AQ exists
			if (!aProvince.equals(ProvinceCodeEnum.EUROPE) && !aProvince.equals(ProvinceCodeEnum.OTHER)
					&& !aProvince.equals(ProvinceCodeEnum.USA)) {
				if (aProvince.equals(this.provinceController.getProvinceCode())) {
					provinceItems.add(aProvince);
				}
				// TODO MODIFY CODE FOR MARITIMES AND CAP BRETON
			}
		}

		return provinceItems;
	}

	/**
	 * DOWNLOAD FILE to txt when clicking on file URL BR5576 Display FSA table data
	 *
	 * @param event
	 * @throws Exception
	 */
	@SuppressWarnings("unchecked")
	public void downloadFile(ActionEvent event) throws Exception {
		// getting param file ID, file Name
		FacesContext context = FacesContext.getCurrentInstance();
		HttpServletResponse response = (HttpServletResponse) context.getExternalContext().getResponse();
		HttpServletRequest request = (HttpServletRequest) context.getExternalContext().getRequest();

		String fileNameParam = request.getParameter("fileName");

		String originalId = ((HashMap<String, String>) request.getSession(false).getAttribute(USER_SESSION_ID_LIST_MATCH_ATTRIBUTE)).get(request.getParameter("loadId"));
		Long loadIdParam = Long.valueOf(originalId);

		InputStream fileContent = this.fsaLoadService.getFileContent(loadIdParam);
		response.setContentType("application/text; charset=utf-8");

		response.addHeader("Content-Disposition", "attachment;filename=" + fileNameParam);

		ServletOutputStream out = response.getOutputStream();

		if (fileContent != null) {
			int readByte = -1;
			while ((readByte = fileContent.read()) != -1) {
				out.write(readByte);
			}
		}

		out.flush();
		out.close();

		FacesContext.getCurrentInstance().responseComplete();
	}

	/**
	 * WRITE Uploaded File content in the server path directory (fsa_files)
	 *
	 * @param fileName
	 */
	public boolean writeInFsaFile(String fileName) {
		throw new RuntimeException("Method not implemented");
/*

		// Validate the file name to prevent path traversal attacks
		if (fileName == null || fileName.contains("..") || fileName.contains("/") || fileName.contains("\\")) {
			logger.error("Invalid file name: " + fileName);
			return false;
		}

		// - STEP I) Sends FILE ON NFS SERVER

		OutputStream fOut = null;
		File newFile = null;
		InputStream fIn = null;
		boolean writeOK = false;

		String complete_path = this.generatePath();

		try {
			fIn = this.fsaUploadBean.getFile().getInputStream();
			newFile = new File(complete_path, fileName + DAT_FILE_EXSTENSION);

			// FSA feature is commented out as per business request
			//commenting out these two lines because of
			//Synopsys giving a high CVE in this part of the coode.
//			if (!newFile.exists()) {
//				newFile.createNewFile();
//			}

			fOut = new BufferedOutputStream(new FileOutputStream(newFile));
			int bufferSize = 1024;
			byte[] buffer = new byte[bufferSize];
			int len = 0;
			while ((len = fIn.read(buffer)) != -1) {
				fOut.write(buffer, 0, len);
			}
			writeOK = true;
		} catch (IOException e) {
			FaceMessageHelper.addErrorMessage("fsaAddForm:fsaFileUpload", "fsa.report.message.MSG.N", this);
			logger.error(e.getMessage());
			logger.info(ExceptionUtils.getStackTrace(e));
		} finally {
			// close the stream to release system resources
			try {
				if (fOut != null) {
					fOut.close();
				}
			} catch (Exception e) {
				logger.error(e.getMessage());
				logger.info(ExceptionUtils.getStackTrace(e));
			}
		}
		return writeOK;*/
	}

	/**
	 * Generate path to nfs server
	 *
	 * @return
	 */
	public String generatePath() {

		String the_path;
		FacesContext context = FacesContext.getCurrentInstance();
		if ("false".equals(this.nsfServerMode)) {
			the_path = context.getExternalContext().getRealPath("/");
			the_path += "\\fsa_files\\";
		} else {
			the_path = this.nsfDirectory;
		}
		return the_path;
	}
}
