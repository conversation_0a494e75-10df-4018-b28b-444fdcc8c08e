/**
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance
 * Copyright (c) 2010, Intact Insurance, All rights reserved.<br>
 */

package com.intact.brokeroffice.controller.subbrokers;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

import javax.inject.Inject;
import javax.inject.Named;

import com.ing.canada.cif.domain.IBrandVisual;
import com.ing.canada.cif.domain.IContextualElectronicLocators;
import com.ing.canada.cif.domain.IContextualPhoneNumbers;
import com.ing.canada.cif.domain.IContextualSubBrokerGnInfos;
import com.ing.canada.cif.domain.ILocatorTimeRange;
import com.ing.canada.cif.domain.ISubBrokers;
import com.ing.canada.cif.domain.enums.ApplicationIdEnum;
import com.ing.canada.cif.domain.enums.ApplicationOriginEnum;
import com.ing.canada.cif.domain.enums.BrokerWebAccessTypeEnum;
import com.ing.canada.cif.domain.enums.CifCompanyEnum;
import com.ing.canada.cif.domain.enums.ElectronicAddressPurposeEnum;
import com.ing.canada.cif.domain.enums.ElectronicAddressTypeEnum;
import com.ing.canada.cif.domain.enums.ImageMimeTypeEnum;
import com.ing.canada.cif.domain.enums.LineOfBusinessEnum;
import com.ing.canada.cif.domain.enums.PhoneNumberUsageEnum;
import com.ing.canada.cif.domain.enums.WeekDayCodeEnum;
import com.ing.canada.cif.domain.impl.BrandVisuals;
import com.ing.canada.cif.domain.impl.ContextualElectronicLocators;
import com.ing.canada.cif.domain.impl.ContextualPhoneNumbers;
import com.ing.canada.cif.domain.impl.ContextualSubBrokerGnInfos;
import com.ing.canada.cif.domain.impl.LocatorTimeRanges;
import com.ing.canada.cif.domain.impl.SubBrokers;
import com.intact.brokeroffice.business.dto.ApplicationDTO;
import com.intact.brokeroffice.business.dto.BusinessHourDTO;
import com.intact.brokeroffice.business.dto.ElectronicContactDTO;
import com.intact.brokeroffice.business.dto.ImageContextEnum;
import com.intact.brokeroffice.business.dto.ImageDTO;
import com.intact.brokeroffice.business.dto.LineOfBusinessDTO;
import com.intact.brokeroffice.business.dto.PhoneDTO;
import com.intact.brokeroffice.business.dto.ServiceDTO;
import com.intact.brokeroffice.business.dto.SubBrokerDTO;
import com.intact.brokeroffice.business.subbrokers.ISubBrokersBusinessProcess;
import com.intact.brokeroffice.clientservice.dao.ClientServiceProfilMaster;
import com.intact.brokeroffice.controller.enums.SessionConstantsEnum;
import com.intact.brokeroffice.web.dto.JSFImageDTO;
import com.intact.brokeroffice.web.dto.PhoneSectionCodeEnum;
import com.intact.brokeroffice.web.dto.WebZoneConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * This class is used to transform a bean into a persistent object and
 * vice-versa
 */
@Service
public class SubBrokerAdapter {

	private WebZoneConfig config = null;

	@Autowired
	private ISubBrokersBusinessProcess subBrokersBusinessProcess;

	private Map<String, String[]> webAccessTypes = null;

	private Map<String, String> quoteSources = null;

	private String currentUser = null;

	public SubBrokerAdapter() {
		this.init();
		config = new WebZoneConfig();
	}

	protected void init() {
		this.webAccessTypes = new HashMap<String, String[]>();
		this.webAccessTypes.put(BrokerWebAccessTypeEnum.NONE.getValue(),
				new String[] { BrokerWebAccessTypeEnum.NONE.getValue() });
		this.webAccessTypes.put(BrokerWebAccessTypeEnum.QUOTE_BROKER.getValue(),
				new String[] { BrokerWebAccessTypeEnum.QUOTE_BROKER.getValue() });
		this.webAccessTypes.put(BrokerWebAccessTypeEnum.QUOTE_INTACT.getValue(),
				new String[] { BrokerWebAccessTypeEnum.QUOTE_INTACT.getValue() });
		this.webAccessTypes.put(BrokerWebAccessTypeEnum.QUOTE_INTACT_AND_BROKER.getValue(), new String[] {
				BrokerWebAccessTypeEnum.QUOTE_INTACT.getValue(), BrokerWebAccessTypeEnum.QUOTE_BROKER.getValue() });

		this.quoteSources = new HashMap<String, String>();
		this.quoteSources.put(BrokerWebAccessTypeEnum.NONE.getValue(), null);
		this.quoteSources.put(BrokerWebAccessTypeEnum.QUOTE_BROKER.getValue(), "WEBBK");
		this.quoteSources.put(BrokerWebAccessTypeEnum.QUOTE_INTACT.getValue(), "INT");
		this.quoteSources.put(BrokerWebAccessTypeEnum.QUOTE_INTACT_AND_BROKER.getValue(), null);
	}

	public WebZoneConfig getConfig() {
		return this.config;
	}

	public void setConfig(WebZoneConfig config) {
		this.config = config;
	}

	public String getCurrentUser() {
		return currentUser;
	}

	public void setCurrentUser(String currentUser) {
		this.currentUser = currentUser;
	}

	/**
	 * Takes the data entered by the user and transforms it into a broker CIF object
	 *
	 * BR5381 If blank, SLA reports will be sent to the regular email address
	 *
	 * BR5382 When checked, point of sale will not be copied on emails to clients
	 * where they are designated to receive a copy by default (i.e.: quote
	 * confirmations, requests to purchase and bind confirmations)
	 *
	 * @param broker
	 *            the broker
	 * @param subBrokerBean
	 *            the sub broker bean
	 * @throws Exception
	 */
	public void saveSubBroker(SubBrokers aSubBrokers, SubBrokerDTO subBrokerBean) throws Exception {
		// Set the last update user id in the subBroker, if necessary, before updating the fields
		this.checkIsSubBrokerDirty(aSubBrokers, subBrokerBean);

		aSubBrokers.setAttendingBrokerNameLine1(subBrokerBean.getOnlineName());

		aSubBrokers
				.setAddrDisplayIfBrokerOrgInd(Boolean.TRUE.equals(subBrokerBean.getDisplayBrokerAddress()) ? "Y" : "N");
		aSubBrokers
				.setAddrDisplayIfIntactOrgInd(Boolean.TRUE.equals(subBrokerBean.getDisplayBrokerAddress()) ? "Y" : "N");

		// TODO COMPLETE VERSION 2.5
		// aSubBrokers.setSlaReportElectronicAddress(subBrokerBean.getEmailSlaReport());


		this.saveBusinessHours(aSubBrokers, subBrokerBean);
		this.saveImages(aSubBrokers, subBrokerBean);
		this.saveAccessTypes(aSubBrokers, subBrokerBean);
		this.saveContacts(aSubBrokers, subBrokerBean);
		this.savePhoneNumbers(aSubBrokers, subBrokerBean);
		this.saveServices(aSubBrokers, subBrokerBean);
		aSubBrokers.setAllowClientSchCallbackInd(subBrokerBean.getAllowCallback());
	}

	/**
	 * Function to check if the infos in the subBroker were updated or not (non-contextual infos only)
	 * @param aSubBrokers
	 * @param subBrokerBean
	 */
	protected void checkIsSubBrokerDirty(SubBrokers aSubBrokers, SubBrokerDTO subBrokerBean) {
		boolean dirty = false;

		String displayBrokerInd = Boolean.TRUE.equals(subBrokerBean.getDisplayBrokerAddress()) ? "Y" : "N";

		if ((aSubBrokers.getAttendingBrokerNameLine1() != null && !aSubBrokers.getAttendingBrokerNameLine1().equals(subBrokerBean.getOnlineName()))
				|| (aSubBrokers.getAddrDisplayIfBrokerOrgInd() != null && !aSubBrokers.getAddrDisplayIfBrokerOrgInd().equals(displayBrokerInd))
				|| (aSubBrokers.getAddrDisplayIfIntactOrgInd() != null && !aSubBrokers.getAddrDisplayIfIntactOrgInd().equals(displayBrokerInd))) {
			dirty = true;
		}

		// If any of the info was updated, set the User ID that did the update in the subBroker
		if (dirty) {
			aSubBrokers.setLastUpdateUserID(this.getCurrentUser());
		}
	}

	protected String getPhone(String phoneNumber) {

		String newPhone = null;

		if (phoneNumber != null && phoneNumber.length() > 0) {
			newPhone = phoneNumber.replace("(","").replace(")", "").replace("-", "").replace(" ", "");
		}

		return newPhone;
	}

	protected void saveServices(SubBrokers aSubBrokers, SubBrokerDTO subBrokerBean) {

		for (ServiceDTO service : subBrokerBean.getServices().values()) {

			ApplicationIdEnum app = ApplicationIdEnum.valueOfCode(service.getApplication().getId());
			IContextualPhoneNumbers phone = aSubBrokers.getPhone(app, LineOfBusinessEnum.PERSONAL_LINE, null, null, PhoneNumberUsageEnum.BUSINESS_PHONE);
			this.fillPhone(phone, aSubBrokers, service.getApplication().getPhones().get(PhoneSectionCodeEnum.DEFAULT.getCode()),
					LineOfBusinessEnum.PERSONAL_LINE, app, null);

			aSubBrokers.setClientCentreWebAccessType(service.getApplication().getAccessType());
			aSubBrokers.setBrokerNameClientCentreDisplayInd(String.valueOf(service.getApplication().getAssignable()));
			aSubBrokers.setAllowClientNotificationInd(String.valueOf(service.getApplication().getNotifiable()));

			this.saveContact(aSubBrokers, service.getContact().getEmailAddress(), app, LineOfBusinessEnum.PERSONAL_LINE, ElectronicAddressTypeEnum.EMAIL, null);
			this.saveContact(aSubBrokers, service.getContact().getChatId(), app, LineOfBusinessEnum.PERSONAL_LINE, ElectronicAddressTypeEnum.URL, ElectronicAddressPurposeEnum.CHAT);
		}
	}

	protected void loadBusinessHours(ISubBrokers subBrokerDest, SubBrokerDTO subBrokerSrc) {

		for (int i = 1; i <= 7; i++) {
			BusinessHourDTO hour = new BusinessHourDTO();
			hour.setDayOfWeek(i);
			subBrokerSrc.getBusinessHours().put("" + i, hour);
		}

		for (ILocatorTimeRange time : subBrokerDest.getLocatorTimeRanges()) {
			BusinessHourDTO hour = subBrokerSrc.getBusinessHours().get("" + time.getWeekDayFromCode());
			if (hour != null) {
				hour.setOpenHour(time.getHoursFrom());
				hour.setCloseHour(time.getHoursTo());
			}
		}
	}

	protected void saveBusinessHours(ISubBrokers subBrokerDest, SubBrokerDTO subBrokerSrc) {
		for (BusinessHourDTO hour : subBrokerSrc.getBusinessHours().values()) {
			WeekDayCodeEnum currentDay = WeekDayCodeEnum.valueOfCode("" + hour.getDayOfWeek());
			LocatorTimeRanges time = subBrokerDest.getBusinessHours(currentDay, currentDay);

			if (time != null && (!time.getHoursFrom().equals(hour.getOpenHour())
							|| !time.getHoursTo().equals(hour.getCloseHour()))) {
				time.setEndDate(new Date());

				// If the business hour exists and the value is changing, new business hour needs to be created
				time = null;
			}

			if (time == null) {
				// Update subbroker to avoid constraint violation error
				this.subBrokersBusinessProcess.update(subBrokerDest);

				// Create new business hour
				time = new LocatorTimeRanges();
				time.setStartDate(new Date());
				time.setEndDate(null);
				time.setWeekDayFromCode(currentDay);
				time.setWeekDayToCode(currentDay);
				time.setHoursFrom(hour.getOpenHour());
				time.setHoursTo(hour.getCloseHour());
				time.setSubBroker(subBrokerDest);
				time.setCreatedByUserID(this.getCurrentUser());
				subBrokerDest.getLocatorTimeRanges().add(time);
			}
		}
	}

	/**
	 * takes the broker CIF object and creates a web bean (form) with it
	 *
	 * @param subBroker
	 *            the broker
	 *
	 * @return the sub broker bean
	 */
	public SubBrokerDTO loadForm(ISubBrokers subBroker, List<ClientServiceProfilMaster> clientServiceProfilMasters) {

		SubBrokerDTO subBrokerBean = new SubBrokerDTO();
		subBrokerBean.setId(subBroker.getSubBrokerId());
		subBrokerBean.setSubBrokerNo(subBroker.getSubBrokerNumber());
		subBrokerBean.setOnlineName(subBroker.getAttendingBrokerNameLine1());
		subBrokerBean.setDisplayBrokerAddress("Y".equals(subBroker.getAddrDisplayIfBrokerOrgInd()));
		subBrokerBean.setDisplayIntactAddress("Y".equals(subBroker.getAddrDisplayIfIntactOrgInd()));

		IContextualPhoneNumbers phone = subBroker.getBrokerFilePhone();
		if (phone != null && phone.getPhoneNbr() != null) {
			subBrokerBean.setPhoneNumber(phone.getAreaCode() + phone.getPhoneNbr());
		}

		this.loadBusinessHours(subBroker, subBrokerBean);
		this.loadImages(subBroker, subBrokerBean);
		subBrokerBean.setName(
				subBroker.getNameLine1() + (subBroker.getNameLine2() == null ? "" : " " + subBroker.getNameLine2()));

		String address1 = subBroker.getAddressLine1();
		String address2 = (subBroker.getCity() == null ? "" : subBroker.getCity());
		address2 += (subBroker.getProvince() == null ? "" : " (" + subBroker.getProvince()) + ")";
		address2 += (subBroker.getMailCode() == null ? "" : " " + subBroker.getMailCode());

		subBrokerBean.setAddress1(address1);
		subBrokerBean.setAddress2(address2);

		this.loadLines(subBrokerBean);

		this.loadContacts(subBroker, subBrokerBean);
		// TODO COMPLETE VERSION 2.5
		// subBrokerBean.setNoCopyEmailToClient("Y".equals(subBroker.getNoCopyEmailToClientInd())?Boolean.TRUE:
		// Boolean.FALSE);
		// subBrokerBean.setEmailSlaReport(subBroker.getAttendingElectronicAddress());

		subBrokerBean.setAllowCallback(subBroker.getAllowClientSchCallbackInd());
		/* Load SubBroker contextual info */
		this.loadWebAccessTypes(subBroker, subBrokerBean);

		this.loadPhoneNumbers(subBroker, subBrokerBean);
		this.loadServices(subBroker, subBrokerBean, clientServiceProfilMasters);
		return subBrokerBean;
	}

	protected void loadServices(ISubBrokers subBroker, SubBrokerDTO subBrokerBean,
			List<ClientServiceProfilMaster> clientServiceProfilMasters) {

		subBrokerBean.setServices(new HashMap<String, ServiceDTO>());

		for (ApplicationIdEnum app : this.getConfig().getServices()) {
			ServiceDTO service = new ServiceDTO();
			service.setContact(new ElectronicContactDTO());
			service.setApplication(new ApplicationDTO(app.getCode()));
			service.getApplication().setPhones(new HashMap<String, PhoneDTO>());
			PhoneDTO newPhone = new PhoneDTO();
			newPhone.setUsage(PhoneSectionCodeEnum.DEFAULT.getCode());
			service.getApplication().getPhones().put(newPhone.getUsage(), newPhone);
			subBrokerBean.getServices().put(service.getApplication().getId(), service);

			service.getApplication().setAccessType(subBroker.getClientCentreWebAccessType());
			service.getApplication().setAssignable("Y".equals(subBroker.getBrokerNameClientCentreDisplayInd()));
			service.getApplication().setNotifiable("Y".equals(subBroker.getAllowClientNotificationInd()));
			IContextualPhoneNumbers phone = subBroker.getPhone(app, LineOfBusinessEnum.PERSONAL_LINE, null, null, PhoneNumberUsageEnum.BUSINESS_PHONE);

			if (phone != null) {
				service.getApplication().getPhones().get(PhoneSectionCodeEnum.DEFAULT.getCode())
						.setIntactPhone(phone.getAreaCode() + phone.getPhoneNbr());
				service.getApplication().getPhones().get(PhoneSectionCodeEnum.DEFAULT.getCode())
						.setIntactTollFree(phone.isTollFree());
			}

			IContextualElectronicLocators contactEmail = subBroker.getContact(app, LineOfBusinessEnum.PERSONAL_LINE, null, ElectronicAddressTypeEnum.EMAIL, null);
			IContextualElectronicLocators contactUrl = subBroker.getContact(app, LineOfBusinessEnum.PERSONAL_LINE, null, ElectronicAddressTypeEnum.URL, ElectronicAddressPurposeEnum.CHAT);

			if (contactEmail != null) {
				service.getContact().setEmailAddress(contactEmail.getElectronicAddress());
			}

			if (contactUrl != null) {
				service.getContact().setChatId(contactUrl.getElectronicAddress());
			}

			String profilMaster = extractProfilMaster(clientServiceProfilMasters);
			if (profilMaster != null) {
				service.getApplication().setAccessType(String.valueOf(
						profilMaster.indexOf(SessionConstantsEnum.CC_WEB_ACCESS_TYPE.getSessionConstant()) != -1));
				service.getApplication().setAssignable(profilMaster
						.indexOf(SessionConstantsEnum.CC_BROKER_NAME_DISPLAY_IND.getSessionConstant()) != -1);
				service.getApplication().setNotifiable(profilMaster
						.indexOf(SessionConstantsEnum.CC_NOTIFICATION_IND.getSessionConstant()) != -1);
			} else {
				service.getApplication().setAccessType("false");
				service.getApplication().setAssignable(false);
			}
		}
	}

	protected void loadLines(SubBrokerDTO bean) {

		bean.setLinesOfBusiness(new HashMap<String, LineOfBusinessDTO>());
		bean.setServices(new HashMap<String, ServiceDTO>());

		for (LineOfBusinessEnum line : this.getConfig().getLines()) {
			LineOfBusinessDTO newLine = new LineOfBusinessDTO();
			newLine.setContact(new ElectronicContactDTO());
			newLine.setLineOfBusiness(line.getCode());
			newLine.setApplications(new TreeMap<String, ApplicationDTO>(new ApplicationComparator()));
			bean.getLinesOfBusiness().put(newLine.getLineOfBusiness(), newLine);

			for (ApplicationIdEnum app : this.getConfig().getApplications().get(line)) {

				ApplicationDTO newApp = new ApplicationDTO();
				newApp.setId(app.getCode());
				newApp.setAccessType(BrokerWebAccessTypeEnum.NONE.getValue());
				newApp.setAssignable(false);
				newApp.setPhones(new HashMap<String, PhoneDTO>());
				PhoneDTO newPhone = new PhoneDTO();
				newPhone.setUsage(PhoneSectionCodeEnum.DEFAULT.getCode());
				newApp.getPhones().put(newPhone.getUsage(), newPhone);
				newPhone = new PhoneDTO();
				newPhone.setUsage(PhoneSectionCodeEnum.OFFER.getCode());
				newApp.getPhones().put(newPhone.getUsage(), newPhone);
				newApp.setAccessType(BrokerWebAccessTypeEnum.NONE.getValue());
				newApp.setAssignable(false);
				bean.getLinesOfBusiness().get(line.getCode()).getApplications().put(newApp.getId(), newApp);
			}
		}
	}

	protected void loadImages(ISubBrokers subBroker, SubBrokerDTO subBrokerBean) {

		for (ImageContextEnum context : ImageContextEnum.values()) {
			JSFImageDTO newImage = new JSFImageDTO();
			newImage.setLanguage(context.getLanguage().getCode());
			newImage.setContext(context.getCode());
			newImage.setUsage(context.getUsage().getCode());
			newImage.setType(context.getType() != null ? context.getType().getCode() : "");

			subBrokerBean.getImages().put(context.getCode(), newImage);
		}

		ImageContextEnum context = null;
		for (IBrandVisual image : subBroker.getBrandVisuals()) {
			context = ImageContextEnum.fromContext(image.getNatureCode().getCode(), image.getLanguageCode().getCode(),
					image.getMimeTypeCode().getCode(), image.getUsageCode().getCode());
			JSFImageDTO newImage = (JSFImageDTO)subBrokerBean.getImages().get(context.getCode());

			if (newImage != null) {
				newImage.setPreviousImage(image.getGraphic());
			}
		}
	}

	protected String extractProfilMaster(List<ClientServiceProfilMaster> clientServiceProfilMasters) {
		if (clientServiceProfilMasters != null) {
			ClientServiceProfilMaster clientCentreProfilMaster = clientServiceProfilMasters.get(0);
			return clientCentreProfilMaster.getProfilesMaster();
		}
		return null;
	}

	/**
	 * Function used to load a CIF SubBroker's contextual general information into a
	 * SubBroker bean.
	 *
	 * @param aSubBrokers
	 *            The CIF SubBroker to load from
	 * @param subBrokerBean
	 *            The SubBroker bean to load to
	 */
	protected void loadWebAccessTypes(ISubBrokers aSubBrokers, SubBrokerDTO subBrokerBean) {

		if (aSubBrokers.getContextualSubBrokerGnInfos() != null) {

			for (IContextualSubBrokerGnInfos accessType : aSubBrokers.getContextualSubBrokerGnInfos()) {
				ApplicationDTO application = subBrokerBean.getLinesOfBusiness()
						.get(accessType.getLineOfBusinessCode().getCode()).getApplications()
						.get(accessType.getApplicationId().getCode());

				if (application != null) {
					application.setAccessType(accessType.getWebAccessType());
					application.setAssignable("Y".equals(accessType.getAssignableInd()));
				}
			}
		}
	}

	/**
	 * Function used to update a CIF SubBroker's contextual general information
	 * based on a given SubBroker bean.
	 *
	 * @param aSubBrokers
	 *            The CIF SubBroker to load to
	 * @param subBrokerBean
	 *            The SubBroker bean to load from
	 */
	protected void saveAccessTypes(ISubBrokers subBrokerDest, SubBrokerDTO subBrokerSrc) {

		for (LineOfBusinessDTO lob : subBrokerSrc.getLinesOfBusiness().values()) {
			for (ApplicationDTO app : lob.getApplications().values()) {

				LineOfBusinessEnum currentLob = LineOfBusinessEnum.valueOfCode(lob.getLineOfBusiness());
				ApplicationIdEnum currentApp = ApplicationIdEnum.valueOfCode(app.getId());
				String currentAssignableFlag = app.getAssignable() ? "Y" : "N";

				IContextualSubBrokerGnInfos accessType = subBrokerDest.getAccessType(currentApp, currentLob);

				if (accessType != null && (
						!accessType.getAssignableInd().equals(currentAssignableFlag) || !accessType.getWebAccessType().equals(app.getAccessType()))) {
					accessType.setEndDate(new Date());

					// If the general info exists and the value is changing, new general info needs to be created
					accessType = null;
				}

				if (accessType == null) {
					// Update subbroker to avoid constraint violation error
					this.subBrokersBusinessProcess.update(subBrokerDest);

					accessType = new ContextualSubBrokerGnInfos();
					accessType.setApplicationId(currentApp);
					accessType.setLineOfBusinessCode(currentLob);
					accessType.setAssignableInd(currentAssignableFlag);
					accessType.setWebAccessType(app.getAccessType());
					accessType.setSubBroker(subBrokerDest);
					accessType.setStartDate(new Date());
					accessType.setCreatedByUserID(this.getCurrentUser());
					subBrokerDest.getContextualSubBrokerGnInfos().add(accessType);
				}
			}
		}

	}

	/**
	 * Function used to load a CIF SubBroker's contextual electronic locators into a
	 * SubBroker bean.
	 *
	 * @param aSubBrokers
	 *            The CIF SubBroker to load from
	 * @param subBrokerBean
	 *            The SubBroker bean to load to
	 */

	protected void loadContacts(ISubBrokers aSubBrokers, SubBrokerDTO subBrokerBean) {

		LineOfBusinessDTO lob = null;

		for (IContextualElectronicLocators contact : aSubBrokers.getContextualElectronicLocators()) {
			 if (contact.isFromBrokerFile() != null && contact.isFromBrokerFile().equals("Y")) {
				 // If the fromBrokerFile indicator is true, it is general information
				if (ElectronicAddressTypeEnum.URL == contact.getElectronicAddressType() && contact.getPurposeCode() == null) {
					subBrokerBean.setUrl(contact.getElectronicAddress());
				}
			} else if (contact.getApplicationId() == null) { // Electronic locator info is generic for all applications of a line of business
				// Contact info for different lines of business
				if (contact.getLineOfBusinessCode() != null) {
					lob = subBrokerBean.getLinesOfBusiness().get(contact.getLineOfBusinessCode().getCode());

					if (ElectronicAddressTypeEnum.URL == contact.getElectronicAddressType() && ElectronicAddressPurposeEnum.CHAT == contact.getPurposeCode()) {
						lob.getContact().setChatId(contact.getElectronicAddress());
					} else if (ElectronicAddressTypeEnum.EMAIL == contact.getElectronicAddressType()) {
						lob.getContact().setEmailAddress(contact.getElectronicAddress());
					}
				}
			}
		}
	}

	/**
	 * Function used to update a CIF SubBroker's contextual electronic locators
	 * based on a given SubBroker bean.
	 *
	 * @param subBrokerDest
	 *            The CIF SubBroker to load to
	 * @param subBrokerSrc
	 *            The SubBroker bean to load from
	 */
	protected void saveContacts(ISubBrokers subBrokerDest, SubBrokerDTO subBrokerSrc) {

		LineOfBusinessEnum context = null;

		// Save Line of business related contacts
		for (LineOfBusinessDTO lob : subBrokerSrc.getLinesOfBusiness().values()) {

			context = LineOfBusinessEnum.valueOfCode(lob.getLineOfBusiness());

			this.saveContact(subBrokerDest, lob.getContact().getEmailAddress(), null,
					context, ElectronicAddressTypeEnum.EMAIL, null);
			this.saveContact(subBrokerDest, lob.getContact().getChatId(), null, context,
					ElectronicAddressTypeEnum.URL, ElectronicAddressPurposeEnum.CHAT);
		}
	}

	protected void saveContact(ISubBrokers subBrokerDest, String newValue, ApplicationIdEnum app,
			LineOfBusinessEnum lob, ElectronicAddressTypeEnum addressType, ElectronicAddressPurposeEnum purpose) {

		IContextualElectronicLocators contact = subBrokerDest.getContact(app, lob, null, addressType, purpose);

		if (newValue != null && newValue.length() > 0) {
			if (contact != null && !contact.getElectronicAddress().equals(newValue)) {
				contact.setEndDate(new Date());
				// If the contact exists and the value is changing, a new contact needs to be created
				contact = null;
			}

			if (contact == null) {
				// Update subbroker to avoid constraint violation error
				this.subBrokersBusinessProcess.update(subBrokerDest);

				// Create new contact
				contact = new ContextualElectronicLocators();
				contact.setApplicationId(app);
				contact.setLineOfBusinessCode(lob);
				contact.setElectronicAddressType(addressType);
				contact.setPurposeCode(purpose);
				contact.setSubBroker(subBrokerDest);
				contact.setStartDate(new Date());
				contact.setFromBrokerFile("N");
				contact.setCreatedByUserID(this.getCurrentUser());
				subBrokerDest.getContextualElectronicLocators().add(contact);
			}

			contact.setElectronicAddress(newValue);
		} else if (contact != null) {
			contact.setEndDate(new Date());
		}

	}

	/**
	 * Function used to load a CIF SubBroker's contextual phone numbers into a
	 * SubBroker bean.
	 *
	 * @param aSubBrokers
	 *            The CIF SubBroker to load from
	 * @param subBrokerBean
	 *            The SubBroker bean to load to
	 */
	protected void loadPhoneNumbers(ISubBrokers subBrokerDest, SubBrokerDTO subBrokerSrc) {

		for (IContextualPhoneNumbers phone : subBrokerDest.getContextualPhoneNumbers()) {

			if (phone.getLineOfBusinessCode() != null && phone.getApplicationId() != null) {

				ApplicationDTO app = subBrokerSrc.getLinesOfBusiness().get(phone.getLineOfBusinessCode().getCode())
						.getApplications().get(phone.getApplicationId().getCode());

				if (app != null) {
					PhoneDTO newPhone = app.getPhones()
							.get(phone.getApplicationSectionCode() != null ? phone.getApplicationSectionCode() : "");

					if (newPhone == null) {
						newPhone = new PhoneDTO();
						newPhone.setUsage(phone.getApplicationSectionCode());
						subBrokerSrc.getLinesOfBusiness().get(phone.getLineOfBusinessCode().getCode()).getApplications()
								.get(phone.getApplicationId().getCode()).getPhones()
								.put(phone.getApplicationSectionCode(), newPhone);
					}

					if (ApplicationOriginEnum.WINI == phone.getApplicationOriginCode()) {
						newPhone.setIntactPhone(phone.getAreaCode() + phone.getPhoneNbr());
						newPhone.setIntactTollFree(phone.isTollFree());
					} else if (ApplicationOriginEnum.CNT == phone.getApplicationOriginCode()) {
						newPhone.setBrokerPhone(phone.getAreaCode() + phone.getPhoneNbr());
						newPhone.setBrokerTollFree(phone.isTollFree());
					} else if (phone.getApplicationOriginCode() == null) {
            newPhone.setPhone(phone.getAreaCode() + phone.getPhoneNbr());
            newPhone.setTollFree(phone.isTollFree());
          }
				}
			}
		}
	}

	/**
	 * Function used to update a CIF SubBroker's contextual phone numbers based on a
	 * given SubBroker bean.
	 *
	 * @param aSubBrokers
	 *            The CIF SubBroker to load to
	 * @param subBrokerBean
	 *            The SubBroker bean to load from
	 */
	protected void savePhoneNumbers(ISubBrokers subBrokerDest, SubBrokerDTO subBrokerSrc) {

		for (LineOfBusinessDTO lob : subBrokerSrc.getLinesOfBusiness().values()) {

			for (ApplicationDTO app : lob.getApplications().values()) {

				ApplicationIdEnum appId = ApplicationIdEnum.valueOfCode(app.getId());
				LineOfBusinessEnum lobId = LineOfBusinessEnum.valueOfCode(lob.getLineOfBusiness());

				for (PhoneDTO phone : app.getPhones().values()) {
					IContextualPhoneNumbers intactPhone = subBrokerDest.getPhone(appId, lobId, ApplicationOriginEnum.WINI,
							"".equals(phone.getUsage()) ? null : phone.getUsage(), PhoneNumberUsageEnum.BUSINESS_PHONE);
					IContextualPhoneNumbers brokerPhone = subBrokerDest.getPhone(appId, lobId, ApplicationOriginEnum.CNT,
							"".equals(phone.getUsage()) ? null : phone.getUsage(), PhoneNumberUsageEnum.BUSINESS_PHONE);
          this.fillPhone(intactPhone, subBrokerDest, phone, lobId, appId, ApplicationOriginEnum.WINI);
					this.fillPhone(brokerPhone, subBrokerDest, phone, lobId, appId, ApplicationOriginEnum.CNT);

					// new bundle quote section
          if (appId == ApplicationIdEnum.WEB_QUOTE) {
            IContextualPhoneNumbers bundlePhone = subBrokerDest.getPhone(appId, lobId, null,
                "".equals(phone.getUsage()) ? null : phone.getUsage(), PhoneNumberUsageEnum.BUSINESS_PHONE);
            this.fillPhone(bundlePhone, subBrokerDest, phone, lobId, appId, null);
          }
				}
			}
		}
	}

	protected void fillPhone(IContextualPhoneNumbers phone, ISubBrokers subBrokerDest, PhoneDTO phoneSrc,
			LineOfBusinessEnum lob, ApplicationIdEnum app, ApplicationOriginEnum origin) {

		String newPhone = null;
		String phoneNumber = getPhoneNumber( phone,  subBrokerDest,  phoneSrc, lob,  app,  origin);
		Boolean tollFree = getTollFree( phone,  subBrokerDest,  phoneSrc, lob,  app,  origin);

		if (phoneNumber != null && phoneNumber.length() > 0) {
			newPhone = phoneNumber.replace("(", "").replace(")", "").replace("-", "").replace(" ", "");

			if (phone != null && (!newPhone.equals(phone.getAreaCode() + phone.getPhoneNbr())
							  || tollFree != phone.getTollFreePhoneInd().equals("Y"))) {
				phone.setEndDate(new Date());
				// If the phone number exists and the value is changing, a new phone number needs to be created
				phone = null;
			}

			if (phone == null) {
				// Update subbroker to avoid constraint violation error
				this.subBrokersBusinessProcess.update(subBrokerDest);

				// Create new phone number
				phone = new ContextualPhoneNumbers();
				phone.setLineOfBusinessCode(lob);
				phone.setApplicationId(app);
				phone.setApplicationOriginCode(origin);
				phone.setApplicationSectionCode(phoneSrc.getUsage());
				phone.setUsageCode(PhoneNumberUsageEnum.BUSINESS_PHONE);
				phone.setSubBroker(subBrokerDest);
				phone.setStartDate(new Date());
				phone.setFromBrokerFile("N");
				phone.setCreatedByUserID(this.getCurrentUser());
				subBrokerDest.getContextualPhoneNumbers().add(phone);
			}

			phone.setEndDate(null);
			phone.setTollFreePhoneInd(Boolean.TRUE.equals(tollFree) ? "Y" : "N");


			phone.setAreaCode(newPhone.substring(0,3));
			phone.setPhoneNbr(newPhone.substring(3));
		} else if (phone != null){
			phone.setEndDate(new Date());
		}

	}

	private String getPhoneNumber(IContextualPhoneNumbers phone, ISubBrokers subBrokerDest, PhoneDTO phoneSrc,
      LineOfBusinessEnum lob, ApplicationIdEnum app, ApplicationOriginEnum origin) {
	  if (app == ApplicationIdEnum.WEB_QUOTE) {
      return ApplicationOriginEnum.WINI == origin ? phoneSrc.getIntactPhone()
          : ApplicationOriginEnum.CNT == origin ? phoneSrc.getBrokerPhone() : phoneSrc.getPhone();
    }
	  return ApplicationOriginEnum.WINI == origin || origin == null ? phoneSrc.getIntactPhone()
        : phoneSrc.getBrokerPhone();
  }

  private Boolean getTollFree(IContextualPhoneNumbers phone, ISubBrokers subBrokerDest, PhoneDTO phoneSrc,
      LineOfBusinessEnum lob, ApplicationIdEnum app, ApplicationOriginEnum origin) {
    if (app == ApplicationIdEnum.WEB_QUOTE) {
      return  ApplicationOriginEnum.WINI == origin ? phoneSrc.getIntactTollFree()
          : ApplicationOriginEnum.CNT == origin ? phoneSrc.getBrokerTollFree()
              : phoneSrc.getTollFree();
    }
    return ApplicationOriginEnum.WINI == origin || origin == null ? phoneSrc.getIntactTollFree()
        : phoneSrc.getBrokerTollFree();
  }

	public List<SubBrokerDTO> getBrokers(CifCompanyEnum company) {

		List<SubBrokerDTO> brokers = new ArrayList<SubBrokerDTO>();

		List<ISubBrokers> subBrokersList = this.subBrokersBusinessProcess
				.getAllWebSubBrokers(company.getSubBrokerCompanyNumber(), company.getMasterBrokerCompanyNumber());

		for (ISubBrokers subBrokers : subBrokersList) {
			SubBrokerDTO aSubbrokerBean = new SubBrokerDTO(subBrokers.getSubBrokerNumber(),
					subBrokers.getSubBrokerId(),
					subBrokers.getNameLine1()
							+ (subBrokers.getNameLine2() == null ? "" : " " + subBrokers.getNameLine2()),
					subBrokers.getCity() != null ? subBrokers.getCity() : "", subBrokers.getSearchProvince());
			this.loadLines(aSubbrokerBean);
			this.loadWebAccessTypes(subBrokers, aSubbrokerBean);
			aSubbrokerBean.setMasterOwnerNo(subBrokers.getMasterOwnerNo());
			brokers.add(aSubbrokerBean);
		}

		return brokers;
	}

	protected void saveImages(ISubBrokers subBrokerDest, SubBrokerDTO subBrokerSrc) throws Exception {

		ImageContextEnum context = null;

		for (ImageDTO image : subBrokerSrc.getImages().values()) {

			context = ImageContextEnum.fromContext(((JSFImageDTO)image).getContext());
			BrandVisuals currentImage = null;

			if (ImageMimeTypeEnum.SVG == context.getType()) {
				currentImage = (BrandVisuals) subBrokerDest.getImage(context.getUsage(), context.getNature(), context.getLanguage(),
						ImageMimeTypeEnum.SVG);
			} else {
				currentImage = (BrandVisuals) subBrokerDest.getImage(context.getUsage(), context.getNature(),
						context.getLanguage());
			}

			if (((JSFImageDTO)image).getImage() != null) {
				if (currentImage != null ) {
					currentImage.setEndDate(new Date());

					// If the image exists (it is a new image), a new new image needs to be created
					currentImage = null;
				}

				if (currentImage == null) {
					// Update subbroker to avoid constraint violation error
					this.subBrokersBusinessProcess.update(subBrokerDest);

					// Create new image
					currentImage = new BrandVisuals();
					currentImage.setMimeTypeCode(this.updateInvalidMimeTypes(((JSFImageDTO)image).getImage().getContentType()));
					currentImage.setSubBroker(subBrokerDest);
					currentImage.setLanguageCode(context.getLanguage());
					currentImage.setNatureCode(context.getNature());
					currentImage.setUsageCode(context.getUsage());
					currentImage.setSubBroker(subBrokerDest);
					currentImage.setStartDate(new Date());
					currentImage.setCreatedByUserID(this.getCurrentUser());
					subBrokerDest.getBrandVisuals().add(currentImage);
				}

				currentImage.setGraphic(((JSFImageDTO)image).getImage().getContent());
			} else if (currentImage != null && ((JSFImageDTO)image).getPreviousImage() == null) {
				currentImage.setEndDate(new Date());
				this.subBrokersBusinessProcess.update(subBrokerDest);
			}
		}
	}

	/**
	 * Function to update invalid mimetypes, if necessary, so they pass the database constraint validation
	 *
	 * @param mimeType
	 * @return Updated mimetype
	 */
	protected ImageMimeTypeEnum updateInvalidMimeTypes (String mimeType) {
		ImageMimeTypeEnum cleanMimeType = null;

		if (mimeType!= null) {
			mimeType = mimeType.toUpperCase().replace("IMAGE/", "").replace("+XML", "").replace("X-", "");

			if (mimeType.equals("PJPG")) {cleanMimeType = ImageMimeTypeEnum.JPEG;}
			else if (mimeType.equals("PJPEG")) {cleanMimeType = ImageMimeTypeEnum.JPEG;}
			else {cleanMimeType = ImageMimeTypeEnum.valueOfCode(mimeType);}
		}

		return cleanMimeType;
	}
}
