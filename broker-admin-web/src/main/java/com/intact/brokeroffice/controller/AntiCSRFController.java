package com.intact.brokeroffice.controller;

import java.util.UUID;

import jakarta.faces.context.FacesContext;
import jakarta.servlet.http.HttpServletRequest;

public class AntiCSRFController {

	protected String tokenCSRF = null;
	protected static final String SECURITY_ANTI_CSRF_SESSION = "SECURITY_ANTI_CSRF_ATTRIBUTE";

	public String getTokenCSRF() {

		FacesContext context = FacesContext.getCurrentInstance();
		HttpServletRequest request = (HttpServletRequest) context.getExternalContext().getRequest();

		return this.buildToken(request, true);

	}

	public String buildToken(HttpServletRequest request, boolean keepCurrentIfExist) {
	
		String value = null;

		if (keepCurrentIfExist) {
			value = (String) (request.getSession(true).getAttribute(AntiCSRFController.SECURITY_ANTI_CSRF_SESSION));
		}

		if (value == null) {
			value = UUID.randomUUID().toString();
			request.getSession(true).setAttribute(SECURITY_ANTI_CSRF_SESSION, value);
		}

		return value;
	}

	public void setTokenCSRF(String token) {
		tokenCSRF = token;
	}

}
