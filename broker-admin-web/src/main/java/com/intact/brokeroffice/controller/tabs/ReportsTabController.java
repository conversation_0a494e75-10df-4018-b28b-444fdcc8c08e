/*
 * 
 */
package com.intact.brokeroffice.controller.tabs;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.intact.brokeroffice.controller.brokerchanges.BrokerChangesController;
import com.intact.brokeroffice.controller.performancemetrics.PerformanceMetricsController;

/**
 * The Class ReportsTabController manages the Reports tabs.
 */
@Component
@Scope("session")
public class ReportsTabController {

	/** The general tab controller. */
	@Autowired
	private GeneralTabController generalTabController;
	
	/**
	 * The Enum Page.
	 */
	private static enum Page { BROKERCHANGES, PERFORMANCEMETRICS }

	/** The current page. */
	private Page page = Page.BROKERCHANGES;
	
	@Autowired
	private PerformanceMetricsController performanceMetricsController;	
	
	@Autowired
	private BrokerChangesController brokerChangeController;
	
	
	/**
	 * Gets the current page.
	 * 
	 * @return the page
	 */
	public Page getPage() {
		return this.page;
	}

	/**
	 * Sets the current page.
	 * 
	 * @param aPage the new page
	 */
	public void setPage(Page aPage) {		
		this.page = aPage;
	}
	
	/**
	 * Broker changes.
	 */
	public void brokerChanges(){		
		this.page = Page.BROKERCHANGES;
		this.brokerChangeController.reset();
	}
	
	/**
	 * Performance metrics.
	 */
	public void performanceMetrics(){
		this.page = Page.PERFORMANCEMETRICS;
		this.performanceMetricsController.reset();
	}	
	
	/**
	 * Reports.
	 */
	public void reports()
	{		
		this.performanceMetricsController.reset();
		this.generalTabController.reports();
		brokerChanges();
	}
}
