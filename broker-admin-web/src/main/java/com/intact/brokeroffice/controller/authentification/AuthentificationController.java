package com.intact.brokeroffice.controller.authentification;

import java.util.ArrayList;
import java.util.List;
import java.util.StringTokenizer;

import jakarta.faces.context.FacesContext;
import jakarta.servlet.http.HttpSession;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.ing.canada.common.web.api.auth.TamTokensEnum;
import com.intact.brokeroffice.controller.enums.SessionConstantsEnum;
import com.intact.brokeroffice.helper.ResourceBundleHelper;

@Component
@Scope("session")
public class AuthentificationController {

	/** The ldap group broker. */
	@Autowired
	@Qualifier("ldap-group-broker")
	private String ldapGroupBroker;

	@Autowired
	@Qualifier("ldap-group-broker-reassign")
	private String ldapGroupBrokerReassign;

	@Autowired
	@Qualifier("logout-url-admin")
	private String exitUrlAdmin;

	@Autowired
	@Qualifier("logout-admin")
	private String logoutAdmin;

	private List<String> blockedSubbrokers;

	private static String LIST_BROKER_NOTALLOWED = "list-subbroker-notallowed";

	/**
	 * Logout
	 * 
	 * @return
	 */
	public String logout() {
		FacesContext context = FacesContext.getCurrentInstance();
		HttpSession session = (HttpSession) context.getExternalContext().getSession(false);

		if (session != null) {
			session.invalidate();
		}

		return "logout";
	}

	/**
	 * Gets the url to exit
	 * 
	 * @return the url to exit
	 */
	public String getUrlToExit() {
		return this.exitUrlAdmin;
	}

	/**
	 * Gets the current account uid.
	 * 
	 * @return the current account uid
	 */
	public String getCurrentAccountUId() {
		FacesContext context = FacesContext.getCurrentInstance();
		HttpSession session = (HttpSession) context.getExternalContext().getSession(true);
		return (String) session.getAttribute(TamTokensEnum.TAM_USER_NAME_CREDENTIAL.getTokenValue());
	}

	/**
	 * Gets the available masters.
	 * 
	 * @return the available masters
	 */
	@SuppressWarnings("unchecked")
	public List<String> getAvailableMasters() {
		FacesContext context = FacesContext.getCurrentInstance();
		HttpSession session = (HttpSession) context.getExternalContext().getSession(true);
		return (List<String>) session.getAttribute(SessionConstantsEnum.AVAILABLE_MASTERS.getSessionConstant());
	}

	/**
	 * Gets the current access level.
	 * 
	 * @return the current access level
	 */
	public String getCurrentAccessLevel() {
		FacesContext context = FacesContext.getCurrentInstance();
		HttpSession session = (HttpSession) context.getExternalContext().getSession(true);
		return (String) session.getAttribute(TamTokensEnum.TAM_SECURITY_ROLES_ATTRIB.getTokenValue());
	}

	/**
	 * Checks if is master role.
	 * 
	 * @return true, if is master role
	 */
	public boolean isMasterRole() {
		// return !this.ldapGroupBroker.equals(getCurrentAccessLevel());
		return getCurrentAccessLevel() != null && !getCurrentAccessLevel().startsWith(this.ldapGroupBroker)&& !getCurrentAccessLevel().startsWith(this.ldapGroupBrokerReassign);
	}
	
	public boolean isReassignBroker() {
		return getCurrentAccessLevel() != null && getCurrentAccessLevel().startsWith(this.ldapGroupBrokerReassign);
	}

	/**
	 * Checks if login user has subbrokers assigned to him
	 * 
	 * @return true if user has subbrokers assigned to him else false
	 */
	public boolean isUserWithoutSubbrokers() {
		List<String> theAvailableMasters = this.getAvailableMasters();
		return !this.isMasterRole() && (theAvailableMasters == null || theAvailableMasters.isEmpty());
	}

	/**
	 * Checks if exit button is displayed
	 * 
	 * @return true if exit button is displayed else false
	 */
	public boolean isExitBtnDisplayed() {

		/* if(this.ldapGroupBroker.equals(getCurrentAccessLevel())){ */
		if (getCurrentAccessLevel() != null && (getCurrentAccessLevel().startsWith(this.ldapGroupBroker)||getCurrentAccessLevel().startsWith(this.ldapGroupBrokerReassign))) {
			return true;
		} else if ("true".equals(this.logoutAdmin)) {
			return true;
		}
		return false;

	}

	/**
	 * Gets the list of subbroker(s) not allowed to see the quote view
	 * 
	 * BR5572 Accounts with Quote View forbidden
	 * 
	 * @return the list subbroker(s) not allowed to see the quote view
	 */
	public List<String> getBlockedSubbrokers() {

		if (this.blockedSubbrokers == null) {

			String subbrokers = ResourceBundleHelper.getMessage("brokerOffice-web", this, LIST_BROKER_NOTALLOWED);

			StringTokenizer st = new StringTokenizer(subbrokers, ";");

			this.blockedSubbrokers = new ArrayList<String>();
			while (st.hasMoreTokens()) {
				this.blockedSubbrokers.add((st.nextToken()).trim().toUpperCase());
			}
		}
		return this.blockedSubbrokers;
	}

	/**
	 * Check if user can view quote BR5572 Accounts with Quote View forbidden
	 * 
	 * @return true if user can view quote else return false
	 */
	public boolean isCanViewQuote() {

		if (this.getBlockedSubbrokers() != null && this.getCurrentAccountUId() != null
				&& this.getBlockedSubbrokers().contains(this.getCurrentAccountUId().toUpperCase())) {
			return false;
		}
		return true;

	}
}
