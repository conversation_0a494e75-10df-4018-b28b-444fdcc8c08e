package com.intact.brokeroffice.controller.fsa;

import java.util.Date;

import com.ing.canada.cif.domain.enums.FsaLoadStatusCodeEnum;
import com.ing.canada.cif.domain.enums.LineOfBusinessEnum;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.intact.brokeroffice.business.dto.BusinessContextEnum;
import com.intact.brokeroffice.controller.province.SubscriptionCompanyEnum;
import com.intact.brokeroffice.helper.ResourceBundleHelper;

public class FsaLoadBean {

	private Long loadId;
	
	private String fileName;	
	
	private ProvinceCodeEnum province;
	
	private SubscriptionCompanyEnum company;
	
	private BusinessContextEnum businessContext;
		
	private Date startDate;

	private Date endDate;
	
	private FsaLoadStatusCodeEnum status;
	
	private String owner;
	
	private String resultCode;
	
	private String resultId;	
	
	private Date processingDate;
	
	/** the {@link LineOfBusinessEnum} associated to this instance */
	private LineOfBusinessEnum lineOfBusiness;

	private final static String FSA_LOAD_STATUS_DESCR =  "fsa.load.status."; 
	
	/**
	 * Gets the loadId
	 * @return the loadId
	 */
	public Long getLoadId() {
		return this.loadId;
	}

	/**
	 * Sets the loadId
	 * @param loadId
	 */
	public void setLoadId(Long loadId) {
		this.loadId = loadId;
	}

	/**
	 * Gets the file name
	 * BR5536  Display table filename 
	 * @return the fileName
	 */
	public String getFileName() {
		return this.fileName;
	}

	/**
	 * Sets the file name
	 * @param aFileName the fileName to set
	 */
	public void setFileName(String aFileName) {
		this.fileName = aFileName;
	}

	/**
	 * Gets the owner
	 * @return the owner
	 */
	public String getOwner() {
		return this.owner;
	}

	/**
	 * Sets the owner
	 * @param aOwner the owner to set
	 */
	public void setOwner(String aOwner) {
		this.owner = aOwner;
	}

	/**
	 * Gets the startDate
	 * BR5539  Displays the Processing Date of the table 
	 * @return the startDate
	 */
	public Date getStartDate() {
		return this.startDate;
	}

	/**
	 * Sets the startDate
	 * 
	 * @param aStartDate the startDate to set
	 */
	public void setStartDate(Date aStartDate) {
		this.startDate = aStartDate;
	}

	/**
	 * Gets the endDate
	 * @return the endDate
	 */
	public Date getEndDate() {
		return this.endDate;
	}

	/**
	 * Sets the endDate
	 * @param aEndDate the endDate to set
	 */
	public void setEndDate(Date aEndDate) {
		this.endDate = aEndDate;
	}

	/**
	 * Gets the province
	 * BR5538  Displays the Province of the table selected during table creation 
	 * @return the province
	 */
	public ProvinceCodeEnum getProvince() {
		return this.province;
	}

	/**
	 * Sets the province
	 * @param aProvince the province to set
	 */
	public void setProvince(ProvinceCodeEnum aProvince) {
		this.province = aProvince;
	}

	public SubscriptionCompanyEnum getCompany() {
		return company;
	}

	public void setCompany(SubscriptionCompanyEnum company) {
		this.company = company;
	}
	
	public BusinessContextEnum getBusinessContext() {
		return businessContext;
	}

	public void setBusinessContext(BusinessContextEnum businessContext) {
		this.businessContext = businessContext;
	}

	/**
	 * Gets the result code
	 * @return the result code
	 */
	public String getResultCode() {
		return this.resultCode;
	}
	
	/**
	 * Uses to display the load status msg
	 * BR5575  Display Upload Result 
	 * @return
	 */
	public String getStatusKey() {
		if(this.status !=null)
		{
			return FSA_LOAD_STATUS_DESCR + this.status.getCode();
		}
		return "fsa.load.status.DEFAULT";
	}

	/**
	 * Sets the result code 
	 * @param aResultCode the result code to set
	 */
	public void setResultCode(String aResultCode) {
		this.resultCode = aResultCode;
	}

	/**
	 * Gets the result id
	 * @return the result id
	 */
	public String getResultId() {
		return this.resultId;
	}

	/**
	 * Sets the result id
	 * @param aResultId the result id to set
	 */
	public void setResultId(String aResultId) {
		this.resultId = aResultId;
	}

	/**
	 *  Gets the processing date
	 *  BR5539  Displays the Processing Date of the table 
	 * @return the processing date
	 */
	public Date getProcessingDate() {
		return this.processingDate;
	}

	/**
	 * Sets the processing date
	 * @param aProcessingDate the processing date to set
	 */
	public void setProcessingDate(Date aProcessingDate) {
		this.processingDate = aProcessingDate;
	}

	/**
	 * Gets the status
	 * @return the status
	 */
	public FsaLoadStatusCodeEnum getStatus() {
		return this.status;
	}

	/**
	 * Set the status
	 * @param aStatus the status to set
	 */
	public void setStatus(FsaLoadStatusCodeEnum aStatus) {
		this.status = aStatus;
	}
	
	/**
	 * Uses essentially for the order by status list
	 * BR5114  Clicking on the column header will sort the list 
	 * by the current column in ascending order. Clicking the column a second time will sort in descending order. Additional clicking will alternate the sort.: 
	 * @return
	 */
	public String getStatusStr() {
		return ResourceBundleHelper.getMessage("com.intact.brokeroffice.controller.fsa.fsa", this, getStatusKey());
	}

	/**
	 * @see #lineOfBusiness
	 */
	public LineOfBusinessEnum getLineOfBusiness() {
		return lineOfBusiness;
	}

	/**
	 * Set the {@link LineOfBusinessEnum}
	 * @param lineOfBusiness
	 */
	public void setLineOfBusiness(LineOfBusinessEnum lineOfBusiness) {
		this.lineOfBusiness = lineOfBusiness;
	}
}