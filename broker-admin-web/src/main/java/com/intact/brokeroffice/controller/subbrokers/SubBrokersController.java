/**
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance
 * Copyright (c) 2010, Intact Insurance, All rights reserved.<br>
 */
package com.intact.brokeroffice.controller.subbrokers;

import com.ing.canada.cif.domain.IContextualSubBrokerGnInfos;
import com.ing.canada.cif.domain.ISubBrokers;
import com.ing.canada.cif.domain.UpdateMode;
import com.ing.canada.cif.domain.enums.ApplicationIdEnum;
import com.ing.canada.cif.domain.enums.BrokerWebAccessTypeEnum;
import com.ing.canada.cif.domain.enums.CifCompanyEnum;
import com.ing.canada.cif.domain.enums.LineOfBusinessEnum;
import com.ing.canada.cif.domain.impl.SubBrokers;
import com.ing.canada.cif.service.ISubBrokersService;
import com.ing.canada.cif.service.broker.util.BrokerAdapter;
import com.intact.brokeroffice.business.clientservice.IClientServiceBusiness;
import com.intact.brokeroffice.business.dto.ApplicationDTO;
import com.intact.brokeroffice.business.dto.SubBrokerDTO;
import com.intact.brokeroffice.business.subbrokers.ISubBrokersBusinessProcess;
import com.intact.brokeroffice.clientservice.dao.ClientServiceProfilMaster;
import com.intact.brokeroffice.controller.AbstractScrollerController;
import com.intact.brokeroffice.controller.enums.SessionConstantsEnum;
import com.intact.brokeroffice.controller.province.ProvinceController;
import com.intact.brokeroffice.controller.tabs.GeneralTabController;
import com.intact.brokeroffice.controller.tabs.SubBrokersTabController;
import com.intact.brokeroffice.dao.SystemDAO;
import com.intact.brokeroffice.helper.FaceMessageHelper;
import com.intact.brokeroffice.helper.ResourceBundleHelper;
import com.intact.brokeroffice.web.dto.JSFImageDTO;
import com.intact.tools.comparison.domain.Change;
import com.intact.tools.comparison.domain.Diff;
import com.intact.tools.comparison.domain.DiffOptions;
import com.intact.tools.comparison.service.ComparisonApplicationService;
import org.apache.commons.lang.StringUtils;
import org.primefaces.util.LangUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import util.StringUtil;

import jakarta.faces.application.FacesMessage;
import jakarta.faces.context.FacesContext;
import jakarta.faces.model.SelectItem;
import javax.inject.Inject;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * The Class contains the action called by the subbroker section of the
 * application
 */
@Component
@Scope("session")
public class SubBrokersController extends AbstractScrollerController {
	/** The Constant log. */
	private static Logger logger = LoggerFactory.getLogger(SubBrokersController.class);

	@Autowired
	private SystemDAO systemDAO;

	@Autowired
	private ComparisonApplicationService comparisonService = null;

	@Autowired
	private ISubBrokersService subBrokersService;

	@Autowired
	private SubBrokersTabController subBrokersTabController;

	@Autowired
	private GeneralTabController generalTabController;

	@Autowired
	private SubBrokerAdapter subBrokerAdapter;

	@Autowired
	private ISubBrokersBusinessProcess subBrokersBusinessProcess;

	@Autowired
	private SubBrokerValidator subBrokerValidator;

	/** The province controller. */
	@Autowired
	private ProvinceController provinceController;

	private String changes = null;

	private List<SubBrokerDTO> subBrokerBeans = new ArrayList<SubBrokerDTO>();

	private SubBrokerDTO subBrokerBean = null;

	private ISubBrokers oldBroker = null;

	private SubBrokerDTO oldSubBroker = null;

	private long selectedId;

	private String ownerNoFilter;

	private String posNoFilter;

	private String posNameFilter;

	private String posCityProvinceFilter;

	private int index = 0;

	private List<BrokerWebAccessTypeEnum> webAccessTypeList;

	@Autowired
	private IClientServiceBusiness clientServiceBusiness;

	public boolean confirm = false;

	private String updateMode = UpdateMode.SINGLE.name();

	private Set<String> imageTypes = null;

	private CifCompanyEnum company = null;

	public SubBrokersController() {
		this.setImageTypes(new HashSet<String>());
		this.getImageTypes().add("Image/bmp");
		this.getImageTypes().add("Image/gif");
		this.getImageTypes().add("Image/jpeg");
		this.getImageTypes().add("Image/pjpeg");
		this.getImageTypes().add("Image/png");
		this.getImageTypes().add("Image/x-png");
		this.getImageTypes().add("image/svg+xml");
		this.listPage();
	}

	public void initIndex() {
		this.setIndex(0);
	}

	public void addIndex() {
		this.setIndex(this.getIndex() + 1);
	}

	public String showError(String inputId) {
		String error = "";

		Iterator<FacesMessage> messages = FacesContext.getCurrentInstance().getMessages("subBrokerForm:" + inputId);
		if (messages.hasNext()) {
			error = "<br/>" + messages.next().getDetail();
		}

		return error;
	}

	/**
	 * Gets the business hours in localized form
	 *
	 * English Content Time 12:00 AM<br>
	 * 12:15 AM<br>
	 * 12:30 AM<br>
	 * 12:45 AM<br>
	 * 01:00 AM<br>
	 * . . . <br>
	 * 10:45 PM<br>
	 * 11:00 PM<br>
	 * 11:15 PM<br>
	 * 11:30 PM<br>
	 * 11:45 PM<br>
	 * French Content<br>
	 * Time<br>
	 * 00h00<br>
	 * 00h15<br>
	 * 00h30<br>
	 * 00h45<br>
	 * 01h00<br>
	 * . . .<br>
	 * 22h45<br>
	 * 23h00<br>
	 * 23h15<br>
	 * 23h30<br>
	 * 23h45<br>
	 *
	 * @return the business hours
	 */
	public List<SelectItem> getBusinessHoursChoice() {
		List<SelectItem> businessHours = new ArrayList<SelectItem>();
		Locale locale = FacesContext.getCurrentInstance().getViewRoot().getLocale();

		SimpleDateFormat formatTime;

		if (locale.getLanguage().equals("fr")) {
			formatTime = new SimpleDateFormat("HH'h'mm");
		} else {
			formatTime = new SimpleDateFormat("hh:mm aa");
		}

		for (int hour = 0; hour < 24; hour++) {
			for (int minute = 0; minute < 60; minute += 15) {
				SimpleDateFormat formatValue = new SimpleDateFormat("HH:mm");
				Calendar cal = Calendar.getInstance();
				cal.set(Calendar.HOUR_OF_DAY, hour);
				cal.set(Calendar.MINUTE, minute);
				String time = formatTime.format(cal.getTime());
				String value = formatValue.format(cal.getTime());

				businessHours.add(new SelectItem(value, time));
			}
		}
		return businessHours;
	}

	/**
	 * Gets the broker web access type list.
	 *
	 * @return the broker web access type list
	 */
	public List<BrokerWebAccessTypeEnum> getWebAccessTypeList() {
		if (this.webAccessTypeList == null) {
			this.webAccessTypeList = new ArrayList<BrokerWebAccessTypeEnum>();
			for (BrokerWebAccessTypeEnum accessType : BrokerWebAccessTypeEnum.values()) {
				this.webAccessTypeList.add(accessType);
			}
		}
		return this.webAccessTypeList;
	}

	/**
	 * Gets the selected id (this is to know what was select from the list page when
	 * we select modify/delete).
	 *
	 * @return the selected id
	 */
	public long getSelectedId() {
		return this.selectedId;
	}

	/**
	 * Sets the selected id (this is done by the list UI for delete or modify
	 * action).
	 *
	 * @param aSelectedId
	 *            the new selected id
	 */
	public void setSelectedId(long aSelectedId) {
		this.selectedId = aSelectedId;
	}

	public int getIndex() {
		int newIndex = this.index;
		this.index++;
		return newIndex;
	}

	public void setIndex(int index) {
		this.index = index;
	}

	public ISubBrokers getOldBroker() {
		return oldBroker;
	}

	public void setOldBroker(ISubBrokers oldBroker) {
		this.oldBroker = oldBroker;
	}

	public SubBrokerDTO getOldSubBroker() {
		return oldSubBroker;
	}

	public void setOldSubBroker(SubBrokerDTO oldSubBroker) {
		this.oldSubBroker = oldSubBroker;
	}

	public String getChanges() {
		return this.changes;
	}

	public void setChanges(String changes) {
		this.changes = changes;
	}

	public ComparisonApplicationService getComparisonService() {
		return comparisonService;
	}

	public void setComparisonService(ComparisonApplicationService comparisonService) {
		this.comparisonService = comparisonService;
	}

	public void reset() {
		this.subBrokerBean = null;
	}

	/**
	 * Gets the list of subbrokers that are web enabled
	 *
	 * BR5259 The default sort order for the list is by POS No., in descending order
	 *
	 * @return the sub broker beans
	 */
	public List<SubBrokerDTO> getSubBrokerBeans() {
		if (this.generalTabController.isBrokers()) {
			if (this.subBrokerBeans == null
					|| !this.provinceController.getCompanyEnumCode().equals(this.getCompany())) {
				this.subBrokerBeans = this.subBrokerAdapter.getBrokers(this.provinceController.getCompanyEnumCode());
				this.setCompany(this.provinceController.getCompanyEnumCode());
			}
		}
		return this.subBrokerBeans;
	}

	protected void setCompany(CifCompanyEnum company) {
		this.company = company;
	}

	protected CifCompanyEnum getCompany() {
		return company;
	}

	/**
	 * Gets the subbrokerbean (this is the bean used to display the modify an
	 * subbroker page).
	 *
	 * @return the sub broker bean
	 */
	public SubBrokerDTO getSubBrokerBean() {
		return this.subBrokerBean;
	}

	public Set<String> getImageTypes() {
		return imageTypes;
	}

	public void setImageTypes(Set<String> imageTypes) {
		this.imageTypes = imageTypes;
	}

	public String computeChanges() throws Exception {

		String messageBoxList = "";
		if (UpdateMode.MULTI.name().equals(this.getUpdateMode())) {
			messageBoxList = this.buildChanges();
		}
		this.setChanges(messageBoxList);

		return this.getUpdateMode();
	}

	protected String buildChanges() throws Exception {

		StringBuilder builder = new StringBuilder("");
		String value = null;

		Diff diff = this.getComparisonService().compare("", this.getOldSubBroker(), this.getSubBrokerBean(),
				new DiffOptions());
		for (Change change : diff.getChanges()) {
			if (this.isChangeOK(change, builder)) {
				value = ResourceBundleHelper.getMessage("com.intact.brokeroffice.controller.subbrokers.subbrokers",
						this, change.getChangeId());
				if (value != null)
					builder.append("-" + value + "--");
			}
		}

		return builder.toString().trim();
	}

	protected boolean isChangeOK(Change change, StringBuilder builder) {
		return !(this.isEmpty(change.getLeft()) && this.isEmpty(change.getRight()))
				&& !(change.getChangeId().endsWith("Phone") && change
						.getRight().toString().replace("(", "").replace(")", "").replace(" ", "").replaceAll("-", "")
						.equals(change.getLeft())
						&& !(change.getChangeId().contains("businessHours")
								&& builder.toString().contains("businessHours")));
	}

	protected boolean isEmpty(Object value) {
		return value == null || (value instanceof String && value.toString().length() == 0);
	}

	public void modifySubBrokersInMultiUpdateMode() throws Exception {
		this.setUpdateMode(UpdateMode.MULTI.name());
		this.modifySubBrokers();
	}

	/**
	 * Modifies all sub-broker with the information submitted
	 *
	 * @throws Exception
	 */
	public void modifySubBrokers() throws Exception {

		if (SubBrokersTabController.Page.MODIFY_SUBBROKER
				.equals(this.subBrokersTabController.getPage())
				&& this.getSubBrokerBean().getSubBrokerNo().equals(this.getOldSubBroker().getSubBrokerNo())
				&& this.getSubBrokerBean().getId() == this.getOldSubBroker().getId()) {

				boolean valid = this.subBrokerValidator.validate(this.getSubBrokerBean());

				if (valid) {
					// get current sub broker
					ISubBrokers broker = this.getSubBroker();

					if (broker == null) {
						return;
					}

					broker.setUpdateMode(UpdateMode.MULTI);
					this.modifySubBroker(broker);
				}

			this.setUpdateMode(UpdateMode.SINGLE.name());
		}
	}


	/**
	 * Modifies only the current sub-broker with the information submitted
	 *
	 * @throws Exception
	 */
	public void modifySubBroker() throws Exception {

		if (SubBrokersTabController.Page.MODIFY_SUBBROKER
				.equals(this.subBrokersTabController.getPage())
				&& this.getSubBrokerBean().getSubBrokerNo().equals(this.getOldSubBroker().getSubBrokerNo())
				&& this.getSubBrokerBean().getId() == this.getOldSubBroker().getId()) {

			boolean valid = this.subBrokerValidator.validate(this.getSubBrokerBean());

			if (valid) {
				// get current sub broker
				ISubBrokers broker = this.getSubBroker();
				if (broker == null) {
					return;
				}
				this.modifySubBroker(broker);
			}
		}

	}

	protected void modifySubBroker(ISubBrokers broker) throws Exception {
		StringBuilder updateLogInfo = new StringBuilder(); // StringBuilder used to create update info to log

		this.subBrokersBusinessProcess.update(new BrokerAdapter(this.getOldBroker(), broker));
		/* Update option within the client Service Section */

		updateLogInfo.append("The user " + this.systemDAO.getSourceUser() + " did an update on the subBroker "
				+ broker.getSubBrokerNumber() + ".");

			modifyProfilMaster(broker);
			// This part is related to updating the CC access of all subbrokers in case of
			// 'update all POS '
			if (UpdateMode.MULTI.equals(broker.getUpdateMode())) {
				updateLogInfo.append(" Following subBrokers were impacted because of update all option : [");
				List<ISubBrokers> childrens = this.subBrokersService.getAllSubBrokers(broker);
				for (ISubBrokers children : childrens) {
					children.setClientCentreWebAccessType(broker.getClientCentreWebAccessType());
					children.setBrokerNameClientCentreDisplayInd(broker.getBrokerNameClientCentreDisplayInd());
					children.setAllowClientNotificationInd(broker.getAllowClientNotificationInd());
					modifyProfilMaster(children);
					updateLogInfo.append(children.getSubBrokerNumber() + ",");
				}
				updateLogInfo.deleteCharAt(updateLogInfo.length() - 1);
				updateLogInfo.append("].");
			}

			this.confirm = false;

		listPageWithModificationNotification();
		logger.info(updateLogInfo.toString());
	}

	private void modifyProfilMaster(ISubBrokers broker) {

		String profileMaster = this.constructMasterProfils(broker.getClientCentreWebAccessType(),
				broker.getBrokerNameClientCentreDisplayInd(),
				broker.getAccessType(ApplicationIdEnum.AUTO_REGULAR_QUOTE, LineOfBusinessEnum.PERSONAL_LINE),
				broker.getAccessType(ApplicationIdEnum.AUTO_QUICKQUOTE, LineOfBusinessEnum.PERSONAL_LINE),
				broker.getAccessType(ApplicationIdEnum.WEB_QUOTE, LineOfBusinessEnum.PERSONAL_LINE),
				broker.getAllowClientNotificationInd());

		this.clientServiceBusiness.updateProfilMaster(String.valueOf(broker.getSubBrokerId()),
				Calendar.getInstance().getTime(), profileMaster);
	}

	private String constructMasterProfils(String ccWebAccessType, String brokerNameDisplayInd,
			IContextualSubBrokerGnInfos autoRQAccessType, IContextualSubBrokerGnInfos autoQQAccessType,
			IContextualSubBrokerGnInfos homeQQAccessType, String brokerNotificationInd) {

		boolean ccWebAccessTypeB = Boolean.parseBoolean(ccWebAccessType);

		boolean brokerNameDisplayIndB = Boolean.parseBoolean(brokerNameDisplayInd);
		boolean brokerNotificationIndB = Boolean.parseBoolean(brokerNotificationInd);

		StringBuilder sb = new StringBuilder();
		if (ccWebAccessTypeB) {
			sb.append("[").append(ApplicationIdEnum.CLIENT_CENTRE.getCode()).append("|")
					.append(SessionConstantsEnum.CC_WEB_ACCESS_TYPE.getSessionConstant()).append("]");
		}
		if (brokerNameDisplayIndB) {
			sb.append("[").append(ApplicationIdEnum.CLIENT_CENTRE.getCode()).append("|")
					.append(SessionConstantsEnum.CC_BROKER_NAME_DISPLAY_IND.getSessionConstant()).append("]");
		}
		if (brokerNotificationIndB) {
			sb.append("[").append(ApplicationIdEnum.CLIENT_CENTRE.getCode()).append("|")
					.append(SessionConstantsEnum.CC_NOTIFICATION_IND.getSessionConstant()).append("]");
		}

		// AutoQuote

		String autoRQWebAccessTypeStringValue = getBrokerWebAccessCodeSessionConstantStringValue(
				autoRQAccessType != null ? autoRQAccessType.getWebAccessType() : null);

		if (autoRQWebAccessTypeStringValue != null) {
			sb.append("[").append(ApplicationIdEnum.AUTO_REGULAR_QUOTE.getCode()).append("|")
					.append(autoRQWebAccessTypeStringValue).append("]");
		}

		if (autoRQAccessType != null && StringUtils.isNotBlank(autoRQAccessType.getAssignableInd())
				&& autoRQAccessType.getAssignableInd().equals("Y")) {
			sb.append("[").append(ApplicationIdEnum.AUTO_REGULAR_QUOTE.getCode()).append("|")
					.append(SessionConstantsEnum.ASSIGNABLE.getSessionConstant()).append("]");
		}

		// QuickQuote

		String autoQQWebAccessTypeStringValue = getBrokerWebAccessCodeSessionConstantStringValue(
				autoQQAccessType != null ? autoQQAccessType.getWebAccessType() : null);

		if (autoQQWebAccessTypeStringValue != null) {
			sb.append("[").append(ApplicationIdEnum.AUTO_QUICKQUOTE.getCode()).append("|")
					.append(autoQQWebAccessTypeStringValue).append("]");
		}

		if (autoQQAccessType != null && StringUtils.isNotBlank(autoQQAccessType.getAssignableInd())
				&& autoQQAccessType.getAssignableInd().equals("Y")) {
			sb.append("[").append(ApplicationIdEnum.AUTO_QUICKQUOTE.getCode()).append("|")
					.append(SessionConstantsEnum.ASSIGNABLE.getSessionConstant()).append("]");
		}

		// Residential QuickQuote

		String homeQQWebAccessTypeStringValue = getBrokerWebAccessCodeSessionConstantStringValue(
				homeQQAccessType != null ? homeQQAccessType.getWebAccessType() : null);

		if (homeQQWebAccessTypeStringValue != null) {
			sb.append("[").append(ApplicationIdEnum.WEB_QUOTE.getCode()).append("|")
					.append(homeQQWebAccessTypeStringValue).append("]");
		}

		if (homeQQAccessType != null && StringUtils.isNotBlank(homeQQAccessType.getAssignableInd())
				&& homeQQAccessType.getAssignableInd().equals("Y")) {
			sb.append("[").append(ApplicationIdEnum.WEB_QUOTE.getCode()).append("|")
					.append(SessionConstantsEnum.ASSIGNABLE.getSessionConstant()).append("]");
		}

		return sb.toString();
	}

	private String getBrokerWebAccessCodeSessionConstantStringValue(String webAccessTypeCodeString) {

		BrokerWebAccessTypeEnum brokerWebAccessTypeEnum = BrokerWebAccessTypeEnum.valueOfCode(webAccessTypeCodeString);
		if (brokerWebAccessTypeEnum != null) {
			switch (brokerWebAccessTypeEnum) {
			case QUOTE_BROKER:
				// WEB_ACCESS_BROKER("WEBACC_QB"),
				return SessionConstantsEnum.WEB_ACCESS_BROKER.getSessionConstant();
			case QUOTE_INTACT:
				// WEB_ACCESS_INTACT("WEBACC_QI"),
				return SessionConstantsEnum.WEB_ACCESS_INTACT.getSessionConstant();
			case QUOTE_INTACT_AND_BROKER:
				// WEB_ACCESS_BROKER_AND_INTACT("WEBACC_QIB"),
				return SessionConstantsEnum.WEB_ACCESS_BROKER_AND_INTACT.getSessionConstant();
			case NONE:
				return null;
			}
		}
		return null;
	}

	/**
	 * Common method used for the modification of a single subBroker or all sub
	 * brokers of the same master client
	 *
	 * this action is called from the modify an account page when clicking on return
	 * to the list of account page.
	 *
	 * BR5154 When no selection is made, display the Select a Point of Sale error
	 * message
	 *
	 * BR5164 The exact number of digits must be entered in each field as follows:
	 * Area code = 3 Exchange = 3 Number = 4
	 *
	 * BR5248 All fields related to the Online Telephone Number will be displayed on
	 * the same line separated by an en dash
	 *
	 * BR5249 If any value is entered it must be numeric and exactly three (3)
	 * digits, otherwise, display the Telephone Number error message
	 *
	 * BR5251 If any value is entered, the preceding and subsequent fields must be
	 * completed, otherwise, display the Telephone Number error message
	 *
	 * BR5252 If any value is entered it must be numeric and exactly four (4)
	 * digits, otherwise, display the Telephone Number error message.
	 *
	 * BR5253 If any value is entered, the preceding two fields must be completed,
	 * otherwise, display the Telephone Number error message.
	 *
	 * BR5379 When checked, suppresses the display of the POS Address for the
	 * selected website(s) in the information panel of the autoquote.
	 *
	 * BR5519 Online Web Site url not mandatory
	 *
	 * BR5261 Each listed value will be centered over a List of Values for the
	 * Business Hours fields
	 *
	 * BR5254 Display list under each day of the week
	 *
	 * BR5254 Display list under each day of the week
	 *
	 * @throws Exception
	 *             the exception
	 */
	protected ISubBrokers getSubBroker() throws Exception {

		ISubBrokers subBrokers = this.subBrokersBusinessProcess.getSubBrokerByID(this.selectedId);
		subBrokers.setUpdateMode(this.getUpdateMode().equals("multi") ? UpdateMode.MULTI : UpdateMode.SINGLE);

		/**
		 * Validate subBroker assignations when trying to deactivate webAccessType.
		 * Return null if it is assigned.
		 **/
		if (!this.verifyUnassignedToQuoteOrFSA(subBrokers, ApplicationIdEnum.AUTO_REGULAR_QUOTE,
				LineOfBusinessEnum.PERSONAL_LINE) // Auto Regular Quote
				|| !this.verifyUnassignedToQuoteOrFSA(subBrokers, ApplicationIdEnum.AUTO_QUICKQUOTE,
						LineOfBusinessEnum.PERSONAL_LINE) // Auto Quick Quote
				|| !this.verifyUnassignedToQuoteOrFSA(subBrokers, ApplicationIdEnum.AUTO_QUICKQUOTE,
						LineOfBusinessEnum.COMMERCIAL_LINE) // IRCA Quick Quote
				|| !this.verifyUnassignedToQuoteOrFSA(subBrokers, ApplicationIdEnum.WEB_QUOTE,
						LineOfBusinessEnum.PERSONAL_LINE) // Home Quote
				|| !this.verifyUnassignedToQuoteOrFSA(subBrokers, ApplicationIdEnum.COMMERCIAL_CLIENT_QUICKQUOTE,
						LineOfBusinessEnum.COMMERCIAL_LINE)) // Commercial Property QuickQuote
		{
			return null;
		}
		copySubBroker(subBrokers);
		return subBrokers;
	}

	/**
	 * Method used to verify if the subBroker is unassigned to a FSA or a Quote for
	 * a given context (ApplicationId + Line of Business).
	 *
	 * @param subBrokers
	 *            The subBroker to verify
	 * @param applicationId
	 *            The current context's application Id
	 * @param lineOfBusiness
	 *            The current context's line of business
	 * @Return True if the subBroker is unassigned to a quote or FSA
	 */
	protected boolean verifyUnassignedToQuoteOrFSA(ISubBrokers subBrokers, ApplicationIdEnum applicationId,
			LineOfBusinessEnum lineOfBusiness) {
		IContextualSubBrokerGnInfos accessType = null;
		boolean unassignedToFsa = true;
		boolean unassignedToQuote = true;

		accessType = subBrokers.getAccessType(applicationId, lineOfBusiness);

		if (!this.confirm && accessType != null
				&& !BrokerWebAccessTypeEnum.NONE.getValue().equals(accessType.getWebAccessType())
				&& BrokerWebAccessTypeEnum.NONE.getValue()
						.equals(subBrokerBean.getLinesOfBusiness().get(lineOfBusiness.getCode()).getApplications()
								.get(applicationId.getCode()).getAccessType())) {

			// BR5796 POS Must Not Have Assigned FSA's
			// The POS to be deactivate to the web program cannot have any
			// assigned FSA's otherwise an error message will be generated.
			unassignedToFsa = this.subBrokersBusinessProcess.verifyPosUnassignedToFsa(this.subBrokerBean.getId(),
					applicationId, lineOfBusiness);
			if (!unassignedToFsa) {
				FaceMessageHelper.addErrorMessage(
						"subBrokerForm:accessType" + applicationId.getCode() + lineOfBusiness.getCode(),
						"error.fsa.assigned.to.POS.msg", this);
				this.subBrokersTabController.modify();
				return false;
			}

			// BR5750 Advise if POS Has Assigned Quotes :
			// When deactivate a POS of the web program, display a message
			// to the user if the POS has active assigned quotes. The
			// message should provide the user with the option to continue
			// or cancel the deactivate operation
			unassignedToQuote = this.subBrokersBusinessProcess.verifyPosUnassignedToQuote(this.subBrokerBean.getId(),
					applicationId, lineOfBusiness);
			if (!unassignedToQuote) {
				FaceMessageHelper.addErrorMessage(
						"subBrokerForm:accessType" + applicationId.getCode() + lineOfBusiness.getCode(),
						"error.active.quotes.assigned.POS.msg", this);
				this.confirm = true;
				this.subBrokersTabController.modify();
				return false;
			}
		}
		return true;
	}

	/**
	 * Merge submitted bean content to the model object
	 *
	 * @param source
	 * @throws Exception
	 */
	private void copySubBroker(ISubBrokers source) throws Exception {
		this.subBrokerAdapter
				.setCurrentUser(this.systemDAO.getSourceUser() != null ? this.systemDAO.getSourceUser() : "UNKNOWN");
		this.subBrokerAdapter.saveSubBroker((SubBrokers) source, this.subBrokerBean);
	}

	public LineOfBusinessEnum[] getLineOfBusinesses() {
		return LineOfBusinessEnum.values();
	}

	/**
	 * This is called following a modification of a subbroker, the subBrokerBean is
	 * not cleared so we can display a message to the user about what as been
	 * modified. BR5260 Redirects the user to the Point of Sale List
	 */
	public void listPageWithModificationNotification() {
		this.subBrokerBeans = null;
		this.subBrokersTabController.list();
	}

	/**
	 * this action is called from the modify a subbroker page when clicking on
	 * return to the list of subbroker page.
	 *
	 * BR5102 Default value is 10 BR5260 Redirects the user to the Point of Sale
	 * List:
	 */

	public void listPage() {
		this.setScrollerPerPage(Integer.valueOf(10));
		this.setScrollerPage(Integer.valueOf(1));
		this.subBrokerBean = null;
		this.subBrokerBeans = null;
		this.ownerNoFilter = null;
		this.posNoFilter = null;
		this.posNameFilter = null;
		this.posCityProvinceFilter = null;

		if (subBrokersTabController != null) {
			this.subBrokersTabController.list();
		}
	}

	/**
	 * This action is called when the application wants to modify a subbroker.
	 *
	 *
	 * BR5243 If user clicks the Modify hyperlink, they are redirected to the
	 * Modify Point of Sale page
	 *
	 */
	@SuppressWarnings("boxing")
	public void modifyPage() {
		this.setOldBroker(this.subBrokersBusinessProcess.getSubBrokerByID(this.selectedId));
		this.setChanges("");
		List<ClientServiceProfilMaster> profiles = this.clientServiceBusiness
				.getProfilMasterInfo(String.valueOf(this.getOldBroker().getSubBrokerId()));
		this.subBrokerBean = this.subBrokerAdapter.loadForm(this.getOldBroker(), profiles);
		this.setOldSubBroker(this.subBrokerAdapter.loadForm(this.getOldBroker(), profiles));
		this.setUpdateMode(UpdateMode.SINGLE.name());
		this.subBrokersTabController.modify();
		this.confirm = false;
	}

	/**
	 * BR6115 Web Zone POS List search Gets the point of sale No filter
	 *
	 * @return
	 */
	public String getPosNoFilter() {
		return this.posNoFilter;
	}

	/**
	 * Sets the point of sale No filter
	 *
	 * @param aCurrentPosNoFilterValue
	 */
	public void setPosNoFilter(String aCurrentPosNoFilterValue) {
		this.posNoFilter = aCurrentPosNoFilterValue;
	}

	/**
	 * BR6115 Web Zone POS List search Gets the point of sale name filter
	 *
	 * @return
	 */
	public String getPosNameFilter() {
		return this.posNameFilter;
	}

	/**
	 * Sets the point of sale name filter
	 *
	 * @param posNameFilter
	 */
	public void setPosNameFilter(String posNameFilter) {
		this.posNameFilter = posNameFilter;
	}

	/**
	 * BR6115 Web Zone POS List search Gets the point of sale city province filter
	 *
	 * @return
	 */
	public String getPosCityProvinceFilter() {
		return this.posCityProvinceFilter;
	}

	/**
	 * Sets the point of sale city province filter
	 *
	 * @param posCityProvinceFilter
	 */
	public void setPosCityProvinceFilter(String posCityProvinceFilter) {
		this.posCityProvinceFilter = posCityProvinceFilter;
	}

	/**
	 * BR6115 Web Zone POS List search Gets the owner no filter
	 *
	 * @return
	 */
	public String getOwnerNoFilter() {
		return this.ownerNoFilter;
	}

	/**
	 * Sets the owner no filter
	 *
	 * @param ownerNoFilter
	 */
	public void setOwnerNoFilter(String ownerNoFilter) {
		this.ownerNoFilter = ownerNoFilter;
	}

	/**
	 * checks if user confirm the deactivation of broker when a quote is still
	 * assigned to POS
	 *
	 * @return
	 */
	public boolean isConfirm() {
		return this.confirm;
	}

	/**
	 * Sets the user confirmation of broker deactivation
	 *
	 * @param confirm
	 */
	public void setConfirm(boolean confirm) {
		this.confirm = confirm;
	}

	public boolean filterName(String aSubBrokerName, String filterText, Locale locale) {
		return StringUtil.isStringValueContainsLowerCaseText(aSubBrokerName, filterText);
	}

	public boolean filterCityProvince(String current, String filterText, Locale locale) {
		if (current == null) {
			return false;
		}
		String[] filterByValues = current.split(";", -1);

		String city = filterByValues[0] != null ? filterByValues[0] : "";
		String province = filterByValues.length > 1 ? filterByValues[1] : "";
		String cityProvStr = city + ", " + province;
		return StringUtil.isStringValueContainsLowerCaseText(cityProvStr, filterText);
	}

	public boolean filterOwnerNo(String aSubBrokerOwnerNo, String filterText, Locale locale) {
		return StringUtil.isStringValueContainsLowerCaseText(aSubBrokerOwnerNo, filterText);
	}

	public boolean filterPosNo(String aSubBrokerPosNo, String filterText, Locale locale) {
		return StringUtil.isStringValueContainsLowerCaseText(aSubBrokerPosNo, filterText);
	}

	public String getLanguage() {
		return FacesContext.getCurrentInstance().getViewRoot().getLocale().getLanguage();
	}

	public void setLanguage(String language) {

	}

	public String getUpdateMode() {
		return this.updateMode;
	}

	public void setUpdateMode(String updateMode) {
		this.updateMode = updateMode;
	}

	public String getAssignableStatus(SubBrokerDTO subBroker) {

		ApplicationDTO reg = subBroker.getLinesOfBusiness().get(LineOfBusinessEnum.PERSONAL_LINE.getCode())
				.getApplications().get(ApplicationIdEnum.AUTO_REGULAR_QUOTE.getCode());
		ApplicationDTO quick = subBroker.getLinesOfBusiness().get(LineOfBusinessEnum.PERSONAL_LINE.getCode())
				.getApplications().get(ApplicationIdEnum.AUTO_QUICKQUOTE.getCode());

		String status = (Boolean.TRUE.equals(reg != null && reg.getAssignable()) ? "A" : "")
				+ (Boolean.TRUE.equals(quick != null && quick.getAssignable()) ? "Q" : "");
		return status.length() > 0 ? status : "*";
	}

	public String getAccessStatus(ApplicationDTO product) {
		String status = "";

		if (product != null) {
			status = (!BrokerWebAccessTypeEnum.NONE.getValue().equals(product.getAccessType())
					? product.getAccessType().replace("Q", "")
					: "");
		}

		return status.length() > 0 ? status : "X";
	}

	public String getStatus(SubBrokerDTO subBroker) {
		ApplicationDTO reg = subBroker.getLinesOfBusiness().get(LineOfBusinessEnum.PERSONAL_LINE.getCode())
				.getApplications().get(ApplicationIdEnum.AUTO_REGULAR_QUOTE.getCode());
		ApplicationDTO quick = subBroker.getLinesOfBusiness().get(LineOfBusinessEnum.PERSONAL_LINE.getCode())
				.getApplications().get(ApplicationIdEnum.AUTO_QUICKQUOTE.getCode());

		return this.getAccessStatus(reg) + "/" + this.getAccessStatus(quick) + "/"
				+ this.getAssignableStatus(subBroker);
	}

	public boolean isDisplayable(JSFImageDTO image) {
		return image != null && (image.getPreviousImage() != null
				|| (image.getImage() != null && this.getImageTypes().contains(image.getImage().getContentType())));
	}

	public Object[] imagesList() {
		return this.getSubBrokerBean().getImages().values().toArray();
	}

	public void clear(String imageName) {
		JSFImageDTO image = (JSFImageDTO) this.subBrokerBean.getImages().get(imageName);
		if (image != null) {
			image.clear();
		}
	}
}
