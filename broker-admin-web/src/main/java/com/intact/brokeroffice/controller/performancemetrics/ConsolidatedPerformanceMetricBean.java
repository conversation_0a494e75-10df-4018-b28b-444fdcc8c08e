package com.intact.brokeroffice.controller.performancemetrics;

import java.util.ArrayList;
import java.util.List;

public class ConsolidatedPerformanceMetricBean extends PerformanceMetricBean {
	
	private List<PerformanceMetricBean> performanceMetricBeans;
	private boolean show = false;
	/**
	 * Constructor I
	 */
	public ConsolidatedPerformanceMetricBean(){
		super();		
		this.performanceMetricBeans = new ArrayList<PerformanceMetricBean>();		
	}
	
	/**
	 * Constructor II
	 * @param aBrokerName
	 */
	public ConsolidatedPerformanceMetricBean(String aBrokerName, String aBrokerNbr){
		super();
		this.performanceMetricBeans = new ArrayList<PerformanceMetricBean>();
		this.setBrokerName(aBrokerName);
		this.setBrokerNbr(aBrokerNbr);
	}	
	
	/**
	 * Consolidation based on PerformanceMetricBean object values
	 * @param aBean : the performance metric bean
	 * @param addElementToList: boolean to indicate if bean should be add to list elements 
	 * 
	 * BR5291  Displays the column sub total for all entries of a given Owner in field Nos. 7 19. 
	 * BR5292  Displays the column total for all entries in field Nos. 7 19.
	 * 
	 */
	public void addPerformanceMetric(PerformanceMetricBean aBean, boolean addElementToList)	{	
			
			this.setNbrOfQuotesIncomplete(this.getNbrOfQuotesIncomplete().add(aBean.getNbrOfQuotesIncomplete()));
			this.setNbrOfQuotesComplete(this.getNbrOfQuotesComplete().add(aBean.getNbrOfQuotesComplete()));
		
			this.setNbrOfPurchaseRequestIncomplete(this.getNbrOfPurchaseRequestIncomplete().add(aBean.getNbrOfPurchaseRequestIncomplete()));
			this.setNbrOfPurchaseRequestComplete(this.getNbrOfPurchaseRequestComplete().add(aBean.getNbrOfPurchaseRequestComplete()));
				
			this.setNbrOfUploadedQuotesAccepted(this.getNbrOfUploadedQuotesAccepted().add(aBean.getNbrOfUploadedQuotesAccepted()));
			this.setNbrOfUploadedQuotesRefused(this.getNbrOfUploadedQuotesRefused().add(aBean.getNbrOfUploadedQuotesRefused()));
			this.setNbrOfUploadedQuotesNotProceed(this.getNbrOfUploadedQuotesNotProceed().add(aBean.getNbrOfUploadedQuotesNotProceed()));
			this.setNbrOfQuotesExpired(this.getNbrOfQuotesExpired().add(aBean.getNbrOfQuotesExpired()));
		
			this.setNbrOfQuotesTotal(this.getNbrOfQuotesTotal().add(aBean.getNbrOfQuotesTotal()));
		
			this.setNbrOfFollowUpNeverContacted(this.getNbrOfFollowUpNeverContacted().add(aBean.getNbrOfFollowUpNeverContacted()));
			this.setNbrOfFollowUpContactNeeded(this.getNbrOfFollowUpContactNeeded().add(aBean.getNbrOfFollowUpContactNeeded()));
			this.setNbrOfFollowUpNoContactNeeded(this.getNbrOfFollowUpNoContactNeeded().add(aBean.getNbrOfFollowUpNoContactNeeded()));
			this.setNbrOfFollowUpNoneRequired(this.getNbrOfFollowUpNoneRequired().add(aBean.getNbrOfFollowUpNoneRequired()));
			
				
	
			this.setNbrOfFollowUpTotal(this.getNbrOfFollowUpTotal().add(aBean.getNbrOfFollowUpTotal()));			
			
			if(addElementToList)
			{				
				this.performanceMetricBeans.add(aBean);
			}		
	}

	/**
	 * Gets the list of performance metric beans
	 * @return (List) of performance metric beans
	 */
	public List<PerformanceMetricBean> getPerformanceMetricBeans() {
		return this.performanceMetricBeans;
	}

	/**
	 * Sets the performance metric beans
	 * @param aPerformanceMetricBeans the performance metric beans to set
	 */
	public void setPerformanceMetricBeans(
			List<PerformanceMetricBean> aPerformanceMetricBeans) {
		this.performanceMetricBeans = aPerformanceMetricBeans;
	}
	
	/**
	 * Gets the show indicator for collapse/ellapse subitems
	 * @return Boolean.True if items are display else Boolean.False if hidden 
	 */
	public Boolean getShow() {
		return this.show;
	}

	/**
	 * Sets the show indicator for collapse/ellapse subitems
	 * @param aShow
	 */
	public void setShow(boolean aShow) {
		this.show = aShow;
	}
	
	/**
	 * alternate the show indicator for collapse/ellapse of subitems
	 *  
	 */
	public void change(){
		if(this.show){
			this.show = false;
		}
		else{
			this.show = true;
		}
	}	
}
