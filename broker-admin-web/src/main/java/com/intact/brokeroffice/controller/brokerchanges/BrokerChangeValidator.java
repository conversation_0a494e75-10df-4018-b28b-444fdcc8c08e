/**
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance
 * Copyright (c) 2009, Intact Insurance, All rights reserved.<br>
 */
package com.intact.brokeroffice.controller.brokerchanges;

import java.util.Date;

import org.springframework.stereotype.Component;

import com.intact.brokeroffice.helper.FaceMessageHelper;

/**
 * <AUTHOR>
 *
 */
@Component
public class BrokerChangeValidator {

		private boolean showErrorMessage = true;
		private Date dateFrom;
		private Date dateTo;

		/**
		* method to validate the form performance metric
		* @param aDateFrom
		* @param aDateTo
		* @return true, if successful
		*/

		public boolean validate(Date aDateFrom, Date aDateTo)
		{
			boolean valid = true;
			this.setDateTo(aDateTo);
			this.setDateFrom(aDateFrom);

			// dates from and to validation
			valid = validateDates(valid);

			return valid;
		}

		/**
		 * @param aDateFrom
		 * @param aDateTo
		 * @param valid
		 * @return true, if successful
		 */
		public boolean validateDates(boolean valid){

			boolean validDates = true;

			// BR5169  The selected date cannot be in the future otherwise
			// display future date error message.
			if(this.getDateFrom()!=null && this.getDateFrom().compareTo(new Date()) > 0)
			{

				if(this.showErrorMessage){
					//MSG500  Error: Date in the future
					FaceMessageHelper.addErrorMessage("brokerChangesForm:calendarFrom", "error.msg.broker.change.future.date", this);
				}
				validDates = false;
			}

			// To date validation - A period To date must be selected
			// BR5169  The selected date cannot be in the future otherwise
			// display future date error message.
			if(this.getDateTo()!=null && this.getDateTo().compareTo(new Date()) > 0){
				//MSG500
				if(this.showErrorMessage){
					FaceMessageHelper.addErrorMessage("brokerChangesForm:calendarTo", "error.msg.broker.change.future.date", this);
				}
				validDates = false;
			}

			// BR5275  The selected Period From date must be less than or equal to the Period To date
			// otherwise display date range error message.:
			// The selected Period From date must be less than or equal to the Period To date
			// otherwise display date range error message.

			if(valid && this.getDateFrom()!=null && this.getDateTo() !=null && this.getDateFrom().compareTo(this.getDateTo()) > 0)
			{

				if(this.showErrorMessage){
					// MSG499  Date range error
					FaceMessageHelper.addErrorMessage("brokerChangesForm:calendarFrom", "error.msg.broker.change.period.start.must.be.prior.or.equal.to.end.date", this);
					FaceMessageHelper.addErrorMessage("brokerChangesForm:calendarTo", "error.msg.broker.change.period.end.must.be.after.or.equal.to.start.date", this);
				}
				validDates = false;
			}

			return valid && validDates;
		}

		/**
		 * Checks if message error must be shown
		 * @return boolean if true
		 */
		public boolean isShowErrorMessage() {
			return this.showErrorMessage;
		}

		/**Set the indicator to show error message
		 * @param aShowErrorMessage
		 */
		public void setShowErrorMessage(boolean aShowErrorMessage) {
			this.showErrorMessage = aShowErrorMessage;
		}

		/**
		 * Gets the date from
		 * @return the date from
		 */
		public Date getDateFrom() {
			return this.dateFrom;
		}

		/**
		 * Sets the date from
		 * @param aDateFrom the date from to set
		 */
		public void setDateFrom(Date aDateFrom) {
			this.dateFrom = aDateFrom;
		}

		/**
		 * Gets the date to
		 * @return the date to
		 */
		public Date getDateTo() {
			return this.dateTo;
		}

		/**
		 * Set the date to
		 * @param aDateTo the date to to set
		 */
		public void setDateTo(Date aDateTo) {
			this.dateTo = aDateTo;
		}
}

