package com.intact.brokeroffice.controller.brokerchanges;

import java.util.*;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.ing.canada.plp.report.SubBrokerAssignmentInfo;
import com.ing.canada.plp.service.ISubBrokerAssignmentInfoService;
import com.intact.brokeroffice.controller.AbstractScrollerController;
import com.intact.brokeroffice.controller.province.ProvinceController;
import com.intact.brokeroffice.controller.tabs.GeneralTabController;

import jakarta.faces.application.FacesMessage;
import jakarta.faces.context.FacesContext;
import jakarta.faces.validator.ValidatorException;

@Component
@Scope("session")
public class BrokerChangesController extends AbstractScrollerController{

	@Autowired
	private GeneralTabController generalTabController;

	private List<BrokerChangeBean> brokerChangeBeans = new ArrayList<>();

	private Date dateTo = new Date();
	private Date dateFrom = initializeDateFrom();

	/** The performance metric validator. */
	@Autowired
	private BrokerChangeValidator brokerChangeValidator;

	@Autowired
	private ISubBrokerAssignmentInfoService subBrokerAssignmentInfoService;

	/** The province controller. */
	@Autowired
	private ProvinceController provinceController;
	/**
	 * Gets the BrokerChangeBean objects List (brokerChangeBeans)
	 *
	 * BR5274  The report will display results from all quotes that have a Creation Date on or after the Period From date.
	 *
	 * BR5278  The report will display results from all quotes that have a Creation Date on or before the Period To date.
	 *
	 * @return List<BrokerChangeBean>
	 */

	public List<BrokerChangeBean> getBrokerChangeBeans() {
		if(this.generalTabController.isReports()){
			if(CollectionUtils.isEmpty(this.brokerChangeBeans)){
				setBrokerChangeBeans(new ArrayList<>());
				List<SubBrokerAssignmentInfo> list = this.subBrokerAssignmentInfoService.getAllSubBrokerAssignmentsSP(
						this.dateFrom, this.dateTo, this.provinceController.getProvinceCode(), this.provinceController
								.getCompanyEnumCode().getSubBrokerCompanyNumber(), this.provinceController
								.getManufacturerCompanyCode());
				for (SubBrokerAssignmentInfo objectInfo : list){
					this.brokerChangeBeans.add(new BrokerChangeBean(objectInfo.getEffectiveDate(), objectInfo.getClientFullName(),  objectInfo.getOldBrokerName() , objectInfo.getNewBrokerName() , objectInfo.getOldBrokerCity(),  objectInfo.getNewBrokerCity(), objectInfo.getAssignmentReason().getCode(), objectInfo.getAssignorUserId()));
				}
			}
		}
		return this.brokerChangeBeans;
	}

	/**
	 * Sets the BrokerChangeBean objects List (brokerChangeBeans)
	 * @param someBrokerChangeBeans
	 */
	public void setBrokerChangeBeans(List<BrokerChangeBean> someBrokerChangeBeans) {
		this.brokerChangeBeans = someBrokerChangeBeans;
	}

	/**
	 * Gets the date from
	 * BR5273 - The Period From date indicates the starting date from which
	 * the report results will be displayed.
	 * @return (Date) the date from
	 */
	public Date getDateFrom() {
		return this.dateFrom;
	}

	/**
	 * Sets the date from (uses to filter information by dates)
	 * @param aDateFrom the dateFrom to set
	 */
	public void setDateFrom(Date aDateFrom) {
		this.dateFrom = aDateFrom;
	}

	/**
	 * Gets the date to
	 *
	 * BR5277  - The Period To date indicates the ending date up
	 * to and including which the report results will be displayed.
	 *
	 * @return Date
	 */
	public Date getDateTo() {
		return this.dateTo;
	}

	/**
	 * Sets the date to (uses to filter information by dates)
	 * @param aDateTo the dateTo to set
	 */
	public void setDateTo(Date aDateTo) {
		this.dateTo = aDateTo;
	}

	/**
	 * Initalize dateFrom with date one month before the current date
	 * @return Date
	 */
	public Date initializeDateFrom()
	{
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(getDateTo());
		calendar.add(Calendar.MONTH, -1);
		return calendar.getTime();
	}

	public boolean validateDates() {
		return this.brokerChangeValidator.validate(this.dateFrom, this.dateTo);
	}

	/**
	 *  Method call on click to find the broker change list
	 */
	public void findChanges(){
		this.setScrollerPage(1);
		this.brokerChangeBeans.clear();
		manageRemovedDate();

		boolean valid = validateDates();

		if(valid){
			List<SubBrokerAssignmentInfo> list = this.subBrokerAssignmentInfoService.getAllSubBrokerAssignmentsSP(
					this.dateFrom, this.dateTo, this.provinceController.getProvinceCode(), this.provinceController
							.getCompanyEnumCode().getSubBrokerCompanyNumber(), this.provinceController
							.getManufacturerCompanyCode());
			for (SubBrokerAssignmentInfo objectInfo : list){
				this.brokerChangeBeans.add(new BrokerChangeBean(objectInfo.getEffectiveDate(), objectInfo.getClientFullName(),  objectInfo.getOldBrokerName() , objectInfo.getNewBrokerName() , objectInfo.getOldBrokerCity(),  objectInfo.getNewBrokerCity(), objectInfo.getAssignmentReason().getCode(), objectInfo.getAssignorUserId()));
			}
		}
	}

	/**
	 * method reset
	 */
	public void reset(){
		this.brokerChangeBeans = new ArrayList<>();
		initDatesValue();
	}

	/**
	 * initialize the date from default value
	 * @return Date (the date from default value)
	 */
	private void initDatesValue()
	{
		setDateTo(new Date());

		Calendar calendar = Calendar.getInstance();
		calendar.setTime(getDateTo());
		calendar.add(Calendar.MONTH, -1);
		setDateFrom(calendar.getTime());

		//BR5102  Default value is 10.
		this.setScrollerPerPage(10);
		this.setScrollerPage(1);
	}

	/**
	 * Checks if empty changes exist
	 * @return true else false
	 */
	public boolean getEmptyChanges(){
		return (this.brokerChangeBeans == null || this.brokerChangeBeans.isEmpty());
	}

	/**
	 * method to init and manage empty date scenario
	 *
	 * BR5276, BR5272
	 */
	public void manageRemovedDate()
	{
		// BR5276  : If the Period To date is removed it will be defaulted to Todays date
		if(this.dateTo == null){
			this.dateTo = new Date();
		}
		// BR5272  : If the Period From date is removed it will be defaulted to the Period To date -30 days.
		if(this.dateFrom == null){
			Calendar fromCalendar = Calendar.getInstance();
			fromCalendar.setTime(this.dateTo);
			fromCalendar.add(Calendar.DATE, -30);
			this.dateFrom = fromCalendar.getTime();
		}
	}
}
