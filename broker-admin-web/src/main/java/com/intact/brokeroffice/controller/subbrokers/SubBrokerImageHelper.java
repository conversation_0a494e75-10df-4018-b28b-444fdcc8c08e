package com.intact.brokeroffice.controller.subbrokers;

import org.apache.batik.anim.dom.SAXSVGDocumentFactory;
import org.apache.batik.bridge.*;
import org.apache.batik.gvt.GraphicsNode;
import org.apache.batik.transcoder.TranscoderInput;
import org.apache.batik.transcoder.TranscoderOutput;
import org.apache.batik.transcoder.image.PNGTranscoder;
import org.apache.batik.util.XMLResourceDescriptor;
import org.apache.commons.lang3.StringUtils;
import org.primefaces.model.file.UploadedFile;
import org.w3c.dom.svg.SVGDocument;

import java.awt.geom.Point2D;
import java.awt.geom.Rectangle2D;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

public class SubBrokerImageHelper {

	private static final float ONE_INCH_IN_CENTIMETERS = 25.4f;

	private static final double IMAGE_HEIGHT_IN_INCHES = 0.80;

	private static final int IMAGE_WIDTH_IN_INCHES = 2;

	private static float dpi = 300;

	private static Set<String> MEDIA_TYPES = new HashSet<String>(Arrays.asList(new String[] {"bmp", "gif", "jpeg", "pjpeg", "png", "x-png"}));

	private static Set<String> INTERNET_MEDIA_TYPES = new HashSet<String>(Arrays.asList(new String[] {"image/bmp", "image/gif", "image/jpeg", "image/pjpeg", "image/png", "image/x-png"}));

	private static Set<String> INTERNET_SVG_MEDIA_TYPES = new HashSet<String>(Arrays.asList(new String[] {"image/svg+xml"}));

	public static boolean isValidMediaType(String type) {
		return MEDIA_TYPES.contains(StringUtils.lowerCase(type));
	}

	/**
	 * Checks our file type is an image. BR5259 If the file is not a recognized
	 * image file, display the Not a Recognized Image error message
	 *
	 * @param aFile
	 *            the a file
	 *
	 * @return true, if is image
	 */
	public static boolean isImage(UploadedFile aFile) {
		return aFile != null && INTERNET_MEDIA_TYPES.contains(StringUtils.lowerCase(aFile.getContentType()));
	}

	/**
	 * Checks our file type is an image. BR5259 If the file is not a recognized
	 * image file, display the Not a Recognized Image error message
	 *
	 * @param aFile
	 *            the a file
	 *
	 * @return true, if is image
	 */
	public static boolean isSVGImage(UploadedFile aFile) {
		return aFile != null && INTERNET_SVG_MEDIA_TYPES.contains(StringUtils.lowerCase(aFile.getContentType()));
	}

	/**
	 * This method transcode an SVG image to JPEG image in order
	 * to display it correctly when displaying  in IE8 and below.
	 *
	 * @param svgImage
	 * @return
	 */
	public static byte[] transcodeSvgToJpeg(byte[] svgImage) {

		ByteArrayOutputStream ostream = new ByteArrayOutputStream();
		ByteArrayInputStream inputStream = new ByteArrayInputStream(svgImage);

        try {
            SVGDocument doc = createSVGDocument(svgImage);
            UserAgent userAgent = new UserAgentAdapter();
            DocumentLoader loader = new DocumentLoader(userAgent);
            BridgeContext ctx = new BridgeContext(userAgent, loader);
            ctx.setDynamicState(BridgeContext.DYNAMIC);
            GVTBuilder builder = new GVTBuilder();
            GraphicsNode rootGN = builder.build(ctx, doc);
            Rectangle2D bounds = rootGN.getBounds();

            double imgageWidth = IMAGE_WIDTH_IN_INCHES * dpi;
            double imageHeight = IMAGE_HEIGHT_IN_INCHES * dpi;
            Point2D.Double scaledSize = getScaledSize(bounds.getWidth(), bounds.getHeight(), imgageWidth, imageHeight);

        	PNGTranscoder imageTranscoder = new PNGTranscoder();
        	imageTranscoder.addTranscodingHint(PNGTranscoder.KEY_WIDTH, (float) scaledSize.getX());
        	imageTranscoder.addTranscodingHint(PNGTranscoder.KEY_HEIGHT, (float)scaledSize.getY());
        	imageTranscoder.addTranscodingHint(PNGTranscoder.KEY_PIXEL_UNIT_TO_MILLIMETER, ONE_INCH_IN_CENTIMETERS / dpi);

        	TranscoderInput input = new TranscoderInput(inputStream);
        	TranscoderOutput output = new TranscoderOutput(ostream);
        	imageTranscoder.transcode(input, output);
        	return ostream.toByteArray();
        }
        catch(Exception e) {
        	// nothing we can do here..
        	// we'll just fallback to returning null
        }
        finally {

        	try {
        		// Flush and close the stream.
        		ostream.flush();
        		ostream.close();
        	}
        	catch(IOException io) {
        		// nothing we can do here..
        		// we'll just fallback to returning null
        	}
        }
		return null;
	}

	private static Point2D.Double getScaledSize(double currentWidth, double currentHeight, double maxWidth, double maxHeight) {

        double ratioX =  maxWidth / currentWidth;
        double ratioY = maxHeight / currentHeight;
        double newWidth;
        double newHeight;

        if (ratioX > ratioY) {
            newWidth = currentWidth * ratioY;
            newHeight = currentHeight * ratioY;
        } else {
        	newWidth = currentWidth * ratioX;
            newHeight = currentHeight * ratioX;
        }

        return new Point2D.Double(newWidth, newHeight);
    }

	/**
	 * Use the SAXSVGDocumentFactory to parse the given URI into a DOM.
	 *
	 * @param uri The path to the SVG file to read.
	 * @return A Document instance that represents the SVG file.
	 * @throws Exception The file could not be read.
	 */
	private static SVGDocument createSVGDocument(byte[] data) throws IOException {

		ByteArrayInputStream inputStream = new ByteArrayInputStream(data);
	    String parser = XMLResourceDescriptor.getXMLParserClassName();
	    SAXSVGDocumentFactory factory = new SAXSVGDocumentFactory( parser );
	    return (SVGDocument) factory.createDocument(null, inputStream);
	}
}
