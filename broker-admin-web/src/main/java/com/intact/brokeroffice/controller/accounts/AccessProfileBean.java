package com.intact.brokeroffice.controller.accounts;

import com.intact.canada.brm.domain.profile.AccessProfileEnum;

public class AccessProfileBean {
	
	private AccessProfileEnum profile;
	private Boolean selected = false;
	
	public AccessProfileEnum getProfile() {
		return profile;
	}

	public void setProfile(AccessProfileEnum profile) {
		this.profile = profile;
	}

	public Boolean getSelected() {
		return selected;
	}

	public void setSelected(Boolean isSelected) {
		this.selected = isSelected;
	}

	

}
