package com.intact.brokeroffice.controller.tabs;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;


/**
 * The Class BrokersTabController manages the Brokers tabs.
 */
@Component
@Scope("session")
public class BrokersTabController {
	
	/** The general tab controller. */
	@Autowired
	private GeneralTabController generalTabController;
	
	/**
	 * The Enum Page.
	 */
	private static enum Page { SUBBROKERS, ACCOUNTS }
	
	/** The current page. */
	private Page page = Page.SUBBROKERS; 

		
	/**
	 * Gets the current page.
	 * 
	 * @return the page
	 */
	public Page getPage() {
		return this.page;
	}

	/**
	 * Sets the current page.
	 * 
	 * @param aPage the new page
	 */
	public void setPage(Page aPage) {
		this.page = aPage;
	}

	/**
	 * Sub brokers.
	 */
	public void subBrokers(){
		this.generalTabController.brokers();
		this.page = Page.SUBBROKERS;
	}
	
	/**
	 * Accounts.
	 */
	public void accounts(){
		this.page = Page.ACCOUNTS;
	}
	
	/**
	 * Checks if is sub brokers page.
	 * 
	 * @return true, if is sub brokers page
	 */
	public boolean isSubBrokersPage(){
		return this.page == Page.SUBBROKERS;
	}
	
	/**
	 * Checks if is accounts page.
	 * 
	 * @return true, if is accounts page
	 */
	public boolean isAccountsPage(){
		return this.page == Page.ACCOUNTS;
	}
}
