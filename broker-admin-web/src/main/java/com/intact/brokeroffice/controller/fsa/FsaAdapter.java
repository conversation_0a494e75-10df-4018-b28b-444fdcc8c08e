package com.intact.brokeroffice.controller.fsa;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.TimeZone;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import com.ing.canada.cif.domain.IFsaLoad;
import com.ing.canada.cif.domain.IFsaMessage;
import com.ing.canada.cif.domain.enums.FsaLoadStatusCodeEnum;
import com.ing.canada.cif.domain.enums.LineOfBusinessEnum;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.intact.brokeroffice.business.dto.BusinessContextEnum;
import com.intact.brokeroffice.controller.province.SubscriptionCompanyEnum;

@Component
public class FsaAdapter {

	/**
	 * LOAD DB MODEL FROM FSA BEAN
	 * @param anFsaLoad
	 * @param anFsaLoadTableBean
	 */
	public void loadFsaTableModel(IFsaLoad anFsaLoad, FsaLoadBean anFsaLoadTableBean) {
		anFsaLoad.setLoadId(anFsaLoadTableBean.getLoadId());
		anFsaLoad.setFileName(anFsaLoadTableBean.getFileName());
		anFsaLoad.setProvinceCd(anFsaLoadTableBean.getProvince().getCode());
		anFsaLoad.setStartDate(anFsaLoadTableBean.getStartDate());
		anFsaLoad.setStatusCd(anFsaLoadTableBean.getStatus().getCode());
		anFsaLoad.setCompanyCode(anFsaLoadTableBean.getCompany().getCifCompany().getSubBrokerCompanyNumber());
	}

	/**
	 * LOAD FSA load bean from FSA load object DB 
	 * @param source
	 * @param destination
	 */
	private void loadFsaTableBean(IFsaLoad source, FsaLoadBean destination) {
		destination.setLoadId(source.getLoadId());
		destination.setFileName(source.getFileName());
		destination.setLineOfBusiness(LineOfBusinessEnum.valueOfCode(source.getLineOfBusiness()));
		destination.setStartDate(source.getStartDate());
		destination.setStatus(FsaLoadStatusCodeEnum.valueOfCode(source.getStatusCd()));
		
		if(source.getProvinceCd() != null){
			destination.setProvince(ProvinceCodeEnum.valueOfCode(source.getProvinceCd()));
		}
		
		if (source.getCompanyCode() != null) {
			destination.setCompany(SubscriptionCompanyEnum.fromCode(source.getCompanyCode()));
		}
							
		destination.setBusinessContext(BusinessContextEnum.valuefromString(source.getLineOfBusiness(), source.getApplicationId()));
		
	}

	/**
	 * LOAD FSA table beans
	 * @param inputFsaList
	 * @param someBeans
	 */
	public List<FsaLoadBean> loadFsaTableBeans(List<IFsaLoad> inputFsaList) {
		List<FsaLoadBean> result;
		
		if(CollectionUtils.isEmpty(inputFsaList)) {
			result = Collections.<FsaLoadBean>emptyList();
		} else {
			result = new ArrayList<FsaLoadBean>(inputFsaList.size());

			for(IFsaLoad anFsaLoad : inputFsaList){
				FsaLoadBean aBean = new FsaLoadBean();
				loadFsaTableBean(anFsaLoad, aBean);
				result.add(aBean);
			}
		}

		return result;
	}

	/**
	 * LOAD DB FSA message object MODEL FROM FSA message bean
	 * @param anFsaMessage
	 * @param anFsaMessageBean
	 */
	public void loadFsaMessageModel(IFsaMessage anFsaMessage, FsaMessageBean anFsaMessageBean) {

		anFsaMessage.setMessageSequence(anFsaMessageBean.getMessageSequence());

		anFsaMessage.setMessage(anFsaMessageBean.getMessage());

		anFsaMessage.setProvinceCd(anFsaMessageBean.getProvinceCd());

		anFsaMessage.setMessageTypeCd(anFsaMessageBean.getMessageTypeCd());

		anFsaMessage.setStatusCd(anFsaMessageBean.getStatusCd());

		anFsaMessage.setRejectFsa(anFsaMessageBean.getRejectFsa());

		anFsaMessage.setRejectPostalCode(anFsaMessageBean.getRejectPostalCode());

		anFsaMessage.setRejectSubBrokerNumber(anFsaMessageBean.getRejectSubBrokerNumber());

		anFsaMessage.setRejectCompanyNumber(anFsaMessageBean.getRejectCompanyNumber());

		anFsaMessage.setRejectExternalSystemOriginCd(anFsaMessageBean.getRejectSubBrokerNumber());

		if(anFsaMessageBean.getLanguage()!=null){
			anFsaMessage.setLanguageCd(anFsaMessageBean.getLanguage());
		}
	}

	/**
	 * LOAD FSA BEAN from FSA DATA BASE INFO 
	 * @param anFsaMessage
	 * @param anFsaMessageBean
	 */
	public void loadFsaMessageBean(IFsaMessage anFsaMessage, FsaMessageBean anFsaMessageBean) {

		anFsaMessageBean.setMessageSequence(anFsaMessage.getMessageSequence());

		anFsaMessageBean.setMessage(anFsaMessage.getMessage());

		anFsaMessageBean.setProvinceCd(anFsaMessage.getProvinceCd());

		anFsaMessageBean.setCreationDate(anFsaMessage.getCreationDate());

		anFsaMessageBean.setMessageTypeCd(anFsaMessage.getMessageTypeCd());

		anFsaMessageBean.setStatusCd(anFsaMessage.getStatusCd());

		anFsaMessageBean.setRejectFsa(anFsaMessage.getRejectFsa());

		anFsaMessageBean.setRejectPostalCode(anFsaMessage.getRejectPostalCode());

		anFsaMessageBean.setRejectSubBrokerNumber(anFsaMessage.getRejectSubBrokerNumber());

		anFsaMessageBean.setRejectCompanyNumber(anFsaMessage.getRejectCompanyNumber());

		anFsaMessageBean.setRejectExternalSystemOriginCd(anFsaMessage.getRejectExternalSystemOriginCd());

		anFsaMessageBean.setRejectStartDate(anFsaMessage.getRejectStartDate());

		anFsaMessageBean.setRejectEndDate(anFsaMessage.getRejectEndDate());

		anFsaMessageBean.setLanguage(anFsaMessage.getLanguageCd());
	}

	/**
	 * LOAD NUMBER OF RECORDS CHANGED and PROCEED TIME 
	 * 	
	 * @param anFsaMessage
	 * @param anFsaMessageBean
	 */
	public void loadSuccesMessageBean(IFsaMessage anFsaMessage,  FsaMessageBean anFsaMessageBean) {
		loadFsaMessageBean(anFsaMessage, anFsaMessageBean);
		buildSuccesInfo(anFsaMessageBean);
	}

	/**
	 * LOAD NUMBER OF RECORDS CHANGED and PROCEED TIME 
	 * 
	 * @param anFsaMessageBean
	 */
	private void buildSuccesInfo(FsaMessageBean anFsaMessageBean) {
		String aMessage = anFsaMessageBean.getMessage();

		List<String> list = new ArrayList<String>(Arrays.asList(aMessage.split(";")));
		if (list.size() == 2) {
			anFsaMessageBean.setFsaProceedNbr(list.get(0).trim());
			if (list.get(1) != null) {
				String proceedTs = list.get(1).trim();
				long milliseconds = Long.valueOf(proceedTs).longValue() * 1000;
				anFsaMessageBean.setProceedTime(getDate(milliseconds, "HH:mm:ss"));
			}
		}
	}

	/**
	 * FORMAT milliseconds into HH:mm:ss 
	 * BR5586  Display the total upload time 
	 * @param milliseconds
	 * @param format
	 * @return
	 */
	public static String getDate(long milliseconds, String format){
		SimpleDateFormat sdf = new SimpleDateFormat(format);
		sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
		return sdf.format(new Date(milliseconds));
	}

	/**
	 * build the FSA messages beans 
	 * @param someFsaMessages
	 * @param someBeans
	 */
	public void loadFsaMessageBeans(List<IFsaMessage> someFsaMessages, List<FsaMessageBean> someBeans) {
		FsaMessageBean aBean = null;
		for(IFsaMessage anFsaMessage : someFsaMessages){
			aBean = new FsaMessageBean();
			loadFsaMessageBean(anFsaMessage, aBean);
			someBeans.add(aBean);
		}
	}

	/**
	 * BR5818  Convert the filename to be uploaded in a standard name
	 * creates the file name with number extension
	 * @param sizeOflist
	 * @param fileNameWithoutNbr
	 * @return
	 */
	public String buildNextFileExtension(int sizeOflist, String fileNameWithoutNbr){
		return fileNameWithoutNbr + String.valueOf(sizeOflist + 1);
	}
}
