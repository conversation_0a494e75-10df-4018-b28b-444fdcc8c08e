package com.intact.brokeroffice.controller.fsa;

import java.util.Date;

public class FsaMessageBean {

	private Long errorLine;
	
	private Long messageSequence;
	
	private String message;
	
	private String provinceCd;

	private Date creationDate;
	
	private String messageTypeCd;

	private String statusCd;
	
	private String rejectPostalCode;

	private String rejectFsa;

	private String rejectSubBrokerNumber;
	
	private String rejectCompanyNumber;

	private Date rejectStartDate;

	private Date rejectEndDate;
	
	private String rejectExternalSystemOriginCd;
	
	private String language;
	
	private String fsaProceedNbr;
	
	private String proceedTime;
	
	private final static String MESSAGE_TYPE_CD_DESCR = "fsa.report.message.MSG.";
	/**
	 * Gets the message sequence
	 * @return
	 */
	public Long getMessageSequence() {
		return this.messageSequence;
	}

	/**
	 * Sets the message sequence
	 * @param aMessageSequence
	 */
	public void setMessageSequence(Long aMessageSequence) {
		this.messageSequence = aMessageSequence;
	}

	/**
	 * Gets the message 
	 * @return the message
	 */
	public String getMessage() {
		return this.message;
	}

	/**
	 * Sets the message 
	 * @param aMessage the message to set
	 */
	public void setMessage(String aMessage) {
		this.message = aMessage;
	}

	/**
	 * Gets the province enum code
	 * @return
	 */
	public String getProvinceCd() {
		return this.provinceCd;
	}

	/**
	 * Sets the province enum code
	 * @param aProvinceCd
	 */
	public void setProvinceCd(String aProvinceCd) {
		this.provinceCd = aProvinceCd;
	}

	/**
	 * Gets the creation date
	 * @return the creation date
	 */
	public Date getCreationDate() {
		return this.creationDate;
	}

	/**
	 * Sets the creation date
	 * @param aCreationDate the creation date to set
	 */
	public void setCreationDate(Date aCreationDate) {
		this.creationDate = aCreationDate;
	}

	/**
	 * Gets the message type cd
	 * @return
	 */
	public String getMessageTypeCd() {
		return this.messageTypeCd;
	}

	/**
	 * Sets the message type cd
	 * @param aMessageTypeCd
	 */
	public void setMessageTypeCd(String aMessageTypeCd) {
		this.messageTypeCd = aMessageTypeCd;
	}

	/**
	 * Gets the status code
	 * @return the status code
	 */
	public String getStatusCd() {
		return this.statusCd;
	}

	/**
	 * Sets the status code
	 * @param aStatusCd the status code to set
	 */
	public void setStatusCd(String aStatusCd) {
		this.statusCd = aStatusCd;
	}

	/**
	 * Gets the reject postal code
	 * @return
	 */
	public String getRejectPostalCode() {
		return this.rejectPostalCode;
	}

	/**
	 * Sets the reject postal code
	 * @param aRejectPostalCode
	 */
	public void setRejectPostalCode(String aRejectPostalCode) {
		this.rejectPostalCode = aRejectPostalCode;
	}

	/**
	 * Gets reject fsa
	 * @return the reject fsa
	 * 
	 */
	public String getRejectFsa() {
		return this.rejectFsa;
	}

	/**
	 * Sets the reject fsa
	 * @param aRejectFsa
	 */
	public void setRejectFsa(String aRejectFsa) {
		this.rejectFsa = aRejectFsa;
	}

	/**
	 * Gets the reject subbroker number
	 * @return
	 */
	public String getRejectSubBrokerNumber() {
		return this.rejectSubBrokerNumber;
	}

	/**
	 * Sets the reject subbroker number
	 * @param aRejectSubbrokerNumber
	 */
	public void setRejectSubBrokerNumber(String aRejectSubBrokerNumber) {
		this.rejectSubBrokerNumber = aRejectSubBrokerNumber;
	}

	/**
	 * Gets the reject company number
	 * @return
	 */
	public String getRejectCompanyNumber() {
		return this.rejectCompanyNumber;
	}

	/**
	 * Sets the reject company number
	 * @param aRejectCompanyNumber
	 */
	public void setRejectCompanyNumber(String aRejectCompanyNumber) {
		this.rejectCompanyNumber = aRejectCompanyNumber;
	}

	/**
	 * Gets the reject start date
	 * @return
	 */
	public Date getRejectStartDate() {
		return this.rejectStartDate;
	}

	/**
	 * Sets the reject start date
	 * @param aRejectStartDate
	 */
	public void setRejectStartDate(Date aRejectStartDate) {
		this.rejectStartDate = aRejectStartDate;
	}

	/**
	 * Gets the reject end date
	 * @return
	 */
	public Date getRejectEndDate() {
		return this.rejectEndDate;
	}

	/**
	 * Sets the reject end date
	 * @param aRejectEndDate
	 */
	public void setRejectEndDate(Date aRejectEndDate) {
		this.rejectEndDate = aRejectEndDate;
	}

	/**
	 *  Gets the reject system origin cd
	 * @return
	 */
	public String getRejectExternalSystemOriginCd() {
		return this.rejectExternalSystemOriginCd;
	}

	/**
	 * Sets the reject system origin cd
	 * @param rejectExternalSystemOriginCd
	 */
	public void setRejectExternalSystemOriginCd(String aRejectExternalSystemOriginCd) {
		this.rejectExternalSystemOriginCd = aRejectExternalSystemOriginCd;
	}

	/**
	 * Gets the language code 
	 * @return
	 */
	public String getLanguage() {
		return this.language;
	}

	/**
	 * Sets the language code
	 * @param aLanguageCode
	 */
	public void setLanguage(String aLanguage) {
		this.language = aLanguage;
	}

	/**
	 * Gets the error line
	 * @return
	 */
	public Long getErrorLine() {
		return this.errorLine;
	}

	/**
	 * Sets the error line
	 * @param errorLine
	 */
	public void setErrorLine(Long errorLine) {
		this.errorLine = errorLine;
	}
	
	/**
	 * Gets the message type code key for description 
	 * @return
	 */
	public String getMessageTypeCdKey(){
		
		if(this.messageTypeCd!=null){
			return MESSAGE_TYPE_CD_DESCR + this.messageTypeCd;
		}
		return MESSAGE_TYPE_CD_DESCR + "DEFAULT";
	}

	/**
	 * Gets fsa proceed number
	 * BR5583  Display number of records processed from the uploaded file 
	 * @return
	 */
	public String getFsaProceedNbr() {
		return this.fsaProceedNbr;
	}

	/**
	 * Sets the fsa proceed number
	 * @param fsaProceedNbr
	 */
	public void setFsaProceedNbr(String fsaProceedNbr) {
		this.fsaProceedNbr = fsaProceedNbr;
	}

	/**
	 * Gets the proceed time
	 * BR5586  Display the total upload time 
	 * @return
	 */
	public String getProceedTime() {
		return this.proceedTime;
	}

	/**
	 * Sets proceed time
	 * @param proceedTime
	 */
	public void setProceedTime(String proceedTime) {
		this.proceedTime = proceedTime;
	}	
	
	/**
	 * check if message type is E, F, G and M
	 * @return
	 */
	public boolean isDisplayed(){
		if(this.messageTypeCd != null && ( 
				("E").equals(this.messageTypeCd) || 
				("F").equals(this.messageTypeCd) || 
				("G").equals(this.messageTypeCd) || 
				("M").equals(this.messageTypeCd))){
			
			return true;
			
		}		
		return false;
	}
	
	/**
	 * number of record per line displayed
	 * BR5580  Display message when validation errors occur 
	 * Add the number of errors in () for the following messages:
	 * @return
	 */
	public int getNbRecord(){
		if(this.messageTypeCd != null && ( 
				("E").equals(this.messageTypeCd) || 
				("F").equals(this.messageTypeCd) || 
				("G").equals(this.messageTypeCd) || 
				("M").equals(this.messageTypeCd))){
			
			return Integer.valueOf(this.message).intValue();
			
		}		
		return 1;
	}
	
}