package com.intact.brokeroffice.controller.performancemetrics;

//import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.ing.canada.plp.report.performancemetric.PerformanceMetricInfo;
import com.ing.canada.plp.report.service.IPerformanceMetricInfoService;
import com.intact.brokeroffice.controller.AbstractScrollerController;
import com.intact.brokeroffice.controller.province.ProvinceController;

@Component
@Scope("session")
public class PerformanceMetricsController extends AbstractScrollerController {

	/** The performance metric info dao. */
	@Autowired
	private IPerformanceMetricInfoService performanceMetricInfoService;

	/** The performance metric validator. */
	@Autowired
	private PerformanceMetricsValidator performanceMetricsValidator;

	private List<ConsolidatedPerformanceMetricBean> consolidatedPerformanceMetricBeans = new ArrayList<ConsolidatedPerformanceMetricBean>();

	private ConsolidatedPerformanceMetricBean totalPerformanceMetricBean;

	private Date dateTo = new Date();

	private Date dateFrom = initializeDateFrom();

	private List<String> quoteSources = new ArrayList<String>();

	private String selectedQuoteSource;

	private static final String QUOTE_SOURCE_FROM_INTACT_WEBSITE = "INT";

	private static final String QUOTE_SOURCE_FROM_WEBBROKER_WEBSITE = "WEBBK";

	private static final String QUOTE_SOURCE_FROM_BOTH = "BOTH";

	/** The language controller. */
	@Autowired
	private ProvinceController provinceController;

	/**
	 * PerformanceMetricsController CONSTRUCTOR
	 */
	public PerformanceMetricsController() {
		initDatesValue();
		initQuoteSources();
	}

	/**
	 * init the quote source list values INT: WEB INTACT WEBBK : WEB BROKER BOTH : BOTH WEB INTACT AND BROKER
	 */
	private void initQuoteSources() {
		this.quoteSources.add(QUOTE_SOURCE_FROM_INTACT_WEBSITE);
		this.quoteSources.add(QUOTE_SOURCE_FROM_WEBBROKER_WEBSITE);
		this.quoteSources.add(QUOTE_SOURCE_FROM_BOTH);
	}

	/**
	 * builds the consolidated performance metric bean objects
	 */
	public void retrieveConsolidatedPerformanceMetricBeans() {

		this.consolidatedPerformanceMetricBeans = new ArrayList<ConsolidatedPerformanceMetricBean>();
		this.totalPerformanceMetricBean = new ConsolidatedPerformanceMetricBean();

		List<PerformanceMetricInfo> items = this.performanceMetricInfoService.getPerformanceMetricListSP(this.dateFrom,
				this.dateTo, this.selectedQuoteSource, this.provinceController.getProvinceCode(),
				this.provinceController.getCompanyEnumCode().getSubBrokerCompanyNumber(), this.provinceController
						.getManufacturerCompanyCode());

		if (items != null && !items.isEmpty()) {
			Map<String, ConsolidatedPerformanceMetricBean> map = new HashMap<String, ConsolidatedPerformanceMetricBean>();
			for (PerformanceMetricInfo perfMetricInfo : items) {
				boolean subTotalInd = isSubTotalIndicator(perfMetricInfo.getSubBrokerName());
				boolean totalInd = isTotalIndicator(perfMetricInfo.getMasterBrokerName());

				if (!totalInd && !subTotalInd) {

					PerformanceMetricBean anItemBean = new PerformanceMetricBean(perfMetricInfo);
					String aKey = anItemBean.getBrokerNbr();
					if (map.containsKey(aKey)) {
						ConsolidatedPerformanceMetricBean consolidatedBean = map.get(aKey);
						consolidatedBean.addPerformanceMetric(anItemBean, true);
					} else {
						ConsolidatedPerformanceMetricBean consolidatedBean = new ConsolidatedPerformanceMetricBean(
								perfMetricInfo.getMasterBrokerName(), perfMetricInfo.getMasterBrokerNbr());
						map.put(aKey, consolidatedBean);
						consolidatedBean.addPerformanceMetric(anItemBean, true);
						this.consolidatedPerformanceMetricBeans.add(consolidatedBean);
					}
					this.getTotalPerformanceMetricBean().addPerformanceMetric(anItemBean, false);
				}
			}
		}

		// BR5288 Displays the Owner Name and No. in ascending order of the Owner No. above all the associated points of
		// sale listed for that Owner.
		// BR5289 Displays the name and POS No. of the points of sale in ascending order of their POS No. in ascending
		// order of their associated Owner No.
		// BR5290 Displays the Owner Name and No. in ascending order of the Owner No. after all associated points of
		// sale for that Owner have been listed.:
		Collections.sort(this.consolidatedPerformanceMetricBeans, new Comparator<ConsolidatedPerformanceMetricBean>() {
			public int compare(ConsolidatedPerformanceMetricBean c1, ConsolidatedPerformanceMetricBean c2) {
				return c1.getBrokerNbr().compareTo(c2.getBrokerNbr());
			}
		});
	}

	/**
	 * Gets the date from
	 *
	 * BR5273 The Period From date indicates the starting date from which the report results will be displayed.:
	 *
	 * @return Date
	 */
	public Date getDateFrom() {
		return this.dateFrom;
	}

	/**
	 * Sets the date from
	 *
	 * @param aDateFrom the dateFrom to set
	 */
	public void setDateFrom(Date aDateFrom) {
		this.dateFrom = aDateFrom;
	}

	/**
	 * Gets the date to
	 *
	 * BR5277 The Period To date indicates the ending date up to and including which the report results will be
	 * displayed.
	 *
	 * @return Date
	 */
	public Date getDateTo() {
		return this.dateTo;
	}

	/**
	 * Sets the date to
	 *
	 * @param aDateTo the dateTo to set
	 */
	public void setDateTo(Date aDateTo) {
		this.dateTo = aDateTo;
	}

	/**
	 * initialize the date from default value
	 *
	 * @return Date (the date from default value)
	 */
	public Date initializeDateFrom() {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(getDateTo());

		// BR5272 If the Period From date is removed it will be defaulted to the Period To date -30 days.
		calendar.add(Calendar.MONTH, -1);

		this.setScrollerPerPage(10);
		this.setScrollerPage(1);

		return calendar.getTime();
	}

	/**
	 * method to display metric according to date from and to filters
	 */

	public boolean validateDates() {
		return this.performanceMetricsValidator.validate(this.dateFrom, this.dateTo);
	}

	public void processMetric() {

		resetResult();
		manageRemovedDate();
		boolean valid = validateDates();

		if (this.dateFrom == null) {
			this.setDateFrom(this.performanceMetricsValidator.getDateFrom());
		}
		if (this.dateTo == null) {
			this.setDateTo(this.performanceMetricsValidator.getDateTo());
		}

		if (!valid) {
			return;
		}
		// retrieveStaticConsolidatedPerformanceMetricBeans();
		retrieveConsolidatedPerformanceMetricBeans();
		// this.reset();
	}

	/**
	 * Gets the consolidated performance metric beans
	 *
	 * @return List<ConsolidatedPerformanceMetricBean>
	 */
	public List<ConsolidatedPerformanceMetricBean> getConsolidatedPerformanceMetricBeans() {
		if (this.consolidatedPerformanceMetricBeans == null) {
			retrieveConsolidatedPerformanceMetricBeans();
		}
		return this.consolidatedPerformanceMetricBeans;
	}

	/**
	 * Gets the total performance metric
	 *
	 * @return ConsolidatedPerformanceMetricBean
	 */
	public ConsolidatedPerformanceMetricBean getTotalPerformanceMetricBean() {
		return this.totalPerformanceMetricBean;
	}

	/**
	 * Sets the total performance metric
	 *
	 * @param aGrandTotalPerformanceMetricBean the totalPerformanceMetricBean to set
	 */
	public void setTotalPerformanceMetricBean(ConsolidatedPerformanceMetricBean aGrandTotalPerformanceMetricBean) {
		this.totalPerformanceMetricBean = aGrandTotalPerformanceMetricBean;
	}

	/**
	 * allows to control collapse/ellapse of subtable elements BR5330 In the Performance Indicator Report, when clicked,
	 * displays any hidden content under the Owner header. BR5335 In the Performance Indicator Report, when clicked, the
	 * Owner S.Total line remains displayed.:
	 *
	 * @param aBean
	 */
	public void change(ConsolidatedPerformanceMetricBean aBean) {
		if (aBean.getShow()) {
			aBean.setShow(false);
		} else {
			aBean.setShow(true);
		}
	}

	/**
	 * Sets the consolidated performance metric Bean
	 *
	 * @param aConsolidatedPerformanceMetricBeans the consolidatedPerformanceMetricBeans to set
	 */
	public void setConsolidatedPerformanceMetricBeans(
			List<ConsolidatedPerformanceMetricBean> aConsolidatedPerformanceMetricBeans) {
		this.consolidatedPerformanceMetricBeans = aConsolidatedPerformanceMetricBeans;
	}

	/**
	 * method reset
	 */
	public void reset() {
		this.consolidatedPerformanceMetricBeans = null;
		this.totalPerformanceMetricBean = null;
		initDatesValue();
	}

	/**
	 * reset list of consolidatedPerformanceMetricBean and totalPerformanceMetricBean
	 */
	public void resetResult() {
		if (this.consolidatedPerformanceMetricBeans != null) {
			this.consolidatedPerformanceMetricBeans.clear();
		}
		this.totalPerformanceMetricBean = null;
	}

	/**
	 * Display the day before the date to
	 *
	 * @return Date
	 */
	public Date getDateBefore() {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(this.getDateTo());
		calendar.add(Calendar.DATE, -1);
		return calendar.getTime();
	}

	/**
	 * initialize the date from default value
	 *
	 * @return Date (the date from default value)
	 */
	private void initDatesValue() {
		setDateTo(new Date());

		Calendar calendar = Calendar.getInstance();
		calendar.setTime(getDateTo());
		calendar.add(Calendar.MONTH, -1);

		setDateFrom(calendar.getTime());
	}

	/**
	 * methods call by ajax to hide all the sub section information
	 */
	public void hide() {
		for (ConsolidatedPerformanceMetricBean itemBean : this.consolidatedPerformanceMetricBeans) {
			itemBean.setShow(false);
		}
	}

	/**
	 * methods call by ajax to show all the sub section information
	 */
	public void display() {
		for (ConsolidatedPerformanceMetricBean itemBean : this.consolidatedPerformanceMetricBeans) {
			itemBean.setShow(true);
		}
	}

	/**
	 * Checks if displaying a sub total
	 *
	 * @param aSubBrokerName
	 * @return true if sub total else false
	 */
	private boolean isSubTotalIndicator(String aSubBrokerName) {
		return "S.Total for master".equals(aSubBrokerName);
	}

	/**
	 * Checks if displaying a total
	 *
	 * @param aBrokerName
	 * @return true if sub total else false
	 */
	private boolean isTotalIndicator(String aBrokerName) {
		return "Total global".equals(aBrokerName);
	}

	/**
	 * manage empty date on performance report KPI
	 */
	public void manageRemovedDate() {
		// BR5276 If the Period To date is removed it will be defaulted to Todays date:
		if (this.dateTo == null) {
			this.dateTo = new Date();
		}
		// BR5272 If the Period From date is removed it will be defaulted to the Period To date -30 days.:
		if (this.dateFrom == null) {
			Calendar fromCalendar = Calendar.getInstance();
			fromCalendar.setTime(this.dateTo);
			fromCalendar.add(Calendar.DATE, -30);
			this.dateFrom = fromCalendar.getTime();
		}
	}

	/**
	 * Gets the list of quotes sources (Intact and Broker web site)
	 *
	 * @return the list of quote sources
	 */
	public List<String> getQuoteSources() {
		return this.quoteSources;
	}

	/**
	 * Sets the list of quote source (Intact and Broker web site)
	 *
	 * @param aQuoteSources the list of quote sources to set
	 */
	public void setQuoteSources(List<String> aQuoteSources) {
		this.quoteSources = aQuoteSources;
	}

	/**
	 * Gets the selected quote source
	 *
	 * BR5176 Multiple selections cannot be made.:
	 *
	 * @return the selected quote source
	 */
	public String getSelectedQuoteSource() {
		return this.selectedQuoteSource;
	}

	/**
	 * Sets the selected quote source
	 *
	 * @param aSelectedQuoteSource the selected quote source to set
	 */
	public void setSelectedQuoteSource(String aSelectedQuoteSource) {
		this.selectedQuoteSource = aSelectedQuoteSource;
	}

}
