package com.intact.brokeroffice.controller.permission;

import static com.ing.canada.cif.domain.enums.LineOfBusinessEnum.COMMERCIAL_LINE;
import static com.ing.canada.cif.domain.enums.LineOfBusinessEnum.PERSONAL_LINE;

import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import jakarta.faces.context.FacesContext;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.ing.canada.cif.domain.IBrokersInfos;
import com.ing.canada.cif.domain.enums.ApplicationIdEnum;
import com.ing.canada.cif.domain.enums.LineOfBusinessEnum;
import com.ing.canada.cif.service.IBrokerService;
import com.ing.canada.plp.domain.enums.ApplicationModeEnum;
import com.ing.canada.plp.domain.enums.LineOfBusinessCodeEnum;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.intact.brokeroffice.business.accounts.IAccountsBusinessProcess;
import com.intact.brokeroffice.business.common.ICommandBusinessProcess;
import com.intact.brokeroffice.business.exception.BrokerServiceException;
import com.intact.brokeroffice.controller.authentification.AuthentificationController;
import com.intact.brokeroffice.controller.province.ProvinceController;
import com.intact.canada.brm.domain.broker.BrokerWebOfficeAccess;
import com.intact.canada.brm.domain.profile.AccessProfileEnum;
import com.intact.canada.brm.domain.profile.UserAccessProfile;
import com.intact.canada.brm.domain.user.UserAccount;
/**
 * The Class PermissionController.
 */
@Component
@Scope("session")
public class PermissionController {
	/** log */
	private static Logger logger = LoggerFactory.getLogger(PermissionController.class);

	/** The authentification controller. */
	@Autowired
	private AuthentificationController authentificationController;

	/** The accounts business process. */
	@Autowired
	private IAccountsBusinessProcess accountsBusinessProcess;

	/** The broker service cif. */
	@Autowired
	@Qualifier("cifBrokersService")
	private IBrokerService brokerService;

	/** The command business process. */
	@Autowired
	private ICommandBusinessProcess commandBusinessProcess;

	/** The ldap group admins. */
	@Autowired
	@Qualifier("ldap-group-admins")
	private String ldapGroupAdmins;

	/** The ldap group program admins. */
	@Autowired
	@Qualifier("ldap-group-program-admins")
	private String ldapGroupProgramAdmins;

	@Autowired
	private ProvinceController provinceController;

	public PermissionController() {

	}

	/**
	 * Returns a string with the abbreviation of the users permission (this is
	 * mainly used to add details when adding notes/activities to a quote)
	 *
	 * @return the abbreviated group
	 */
	public String getAbbreviatedGroup() {
		String currentAccess = this.authentificationController.getCurrentAccessLevel();

		if (this.ldapGroupAdmins.equals(currentAccess) || this.ldapGroupProgramAdmins.equals(currentAccess)) {
			return " (INTACT)";
		}

		return "";
	}

	/**
	 * Check add followup notes. BR5390 Web Zone user access: BR5203 Do not
	 * display for Program Admin users.
	 *
	 * @return the check add followup notes
	 */
	public Boolean getCheckAddFollowupNotes() {
		String currentAccess = this.authentificationController.getCurrentAccessLevel();

		if (currentAccess != null
				&& (currentAccess.startsWith(this.ldapGroupAdmins))) {

			return Boolean.TRUE;
		}
		return Boolean.FALSE;
	}

	/**
	 * Check upload. BR5203 Do not display for Program Admin users. BR5390 Web
	 * Zone user access:
	 *
	 * @return the check upload
	 */
	public Boolean getCheckUpload() {
		String currentAccess = this.authentificationController.getCurrentAccessLevel();

		if (currentAccess != null
				&& (currentAccess.startsWith(this.ldapGroupAdmins))) {

			return Boolean.TRUE;
		}
		return Boolean.FALSE;
	}

	/**
	 * Check account management. BR5390 Web Zone user access:
	 *
	 * @return the check account management
	 */
	public Boolean getCheckAccountManagement() {
		String currentAccess = this.authentificationController.getCurrentAccessLevel();

		if (currentAccess != null && (currentAccess.startsWith(this.ldapGroupProgramAdmins) || currentAccess.startsWith(this.ldapGroupAdmins))) {
			return Boolean.TRUE;
		}
		return Boolean.FALSE;
	}

	/**
	 * Current access
	 *
	 * @return
	 */
	public String getAccess() {
		String currentAccess = this.authentificationController.getCurrentAccessLevel();
		return currentAccess;
	}

	/**
	 * Check manage account access. BR5390 Web Zone user access:
	 *
	 * @return the check manage account access
	 */
	public Boolean getCheckManageAccountAccess() {
		return (this.authentificationController.getCurrentAccessLevel() != null && this.authentificationController.getCurrentAccessLevel().startsWith(
				this.ldapGroupAdmins));
	}

	/**
	 * Check manage sub broker profile. BR5390 Web Zone user access:
	 *
	 * @return the check manage sub broker profile
	 */
	public Boolean getCheckManageSubBrokerProfile() {
		String currentAccess = this.authentificationController.getCurrentAccessLevel();

		if (currentAccess != null && (currentAccess.startsWith(this.ldapGroupProgramAdmins) || currentAccess.startsWith(this.ldapGroupAdmins))) {
			return Boolean.TRUE;
		}
		return Boolean.FALSE;
	}

	/**
	 * Check manage fsa. BR5390 Web Zone user access:
	 *
	 * @return the check manage fsa
	 */
	public Boolean getCheckManageFsa() {
		if (this.authentificationController.getCurrentAccessLevel() != null
				&& this.authentificationController.getCurrentAccessLevel().startsWith(this.ldapGroupAdmins)) {
			return Boolean.TRUE;
		}
		return Boolean.FALSE;
	}

	/**
	 * Check reports. BR5390 Web Zone user access:
	 *
	 * @return the check reports
	 */
	public Boolean getCheckReports() {
		String currentAccess = this.authentificationController.getCurrentAccessLevel();
		if (currentAccess != null && (currentAccess.startsWith(this.ldapGroupProgramAdmins) || currentAccess.startsWith(this.ldapGroupAdmins))) {
			return Boolean.TRUE;
		}
		return Boolean.FALSE;
	}

	/**
	 * Check view change report. BR5390 Web Zone user access:
	 *
	 * @return the check view change report
	 */
	public Boolean getCheckViewChangeReport() {
		String currentAccess = this.authentificationController.getCurrentAccessLevel();

		if (currentAccess != null && (currentAccess.startsWith(this.ldapGroupProgramAdmins) || currentAccess.startsWith(this.ldapGroupAdmins))) {
			return Boolean.TRUE;
		}
		return Boolean.FALSE;
	}

	/**
	 * Check view kpi report. BR5390 Web Zone user access:
	 *
	 * @return the check view kpi report
	 */
	public Boolean getCheckViewKpiReport() {
		String currentAccess = this.authentificationController.getCurrentAccessLevel();

		if (currentAccess != null && (currentAccess.startsWith(this.ldapGroupProgramAdmins) || currentAccess.startsWith(this.ldapGroupAdmins))) {
			return Boolean.TRUE;
		}
		return Boolean.FALSE;
	}

	/**
	 * @return true if the user has access to personal line of business
	 */
	public boolean isPersonalLinesAccessProfile() {
		return getAvailableLinesOfBusiness().contains(PERSONAL_LINE);
	}

	/**
	 * @return true if the user has access to commercial line of business
	 */
	public boolean isIRCACommercialQQAccessProfile() {
		return getAvailableLinesOfBusiness().contains(COMMERCIAL_LINE);
	}

	/**
	 * @return all lines of business available to the current account in session
	 */
	public Set<LineOfBusinessEnum> getAvailableLinesOfBusiness() {
		try {
			return getAllAccessibleLinesOfBusiness();
		} catch (BrokerServiceException e) {
			logger.error(e.getMessage());
		}

		return Collections.<LineOfBusinessEnum>emptySet();
	}

	/**
	 * Obtain all accessible lines of business based on the current account
	 * @return Set containing all accessible lines of business based on the current account
	 * <ul>
	 * 		<li>{@link LineOfBusinessCodeEnum#PERSONAL_LINES} and {@link LineOfBusinessCodeEnum#COMMERCIAL_LINES} if the user has a master role</li>
	 * 		<li>{@link LineOfBusinessCodeEnum#PERSONAL_LINES} and/or {@link LineOfBusinessCodeEnum#COMMERCIAL_LINES} depending on the user's access profiles</li>
	 * 		<li>empty set if the user has no defined accesses</li>
	 * </ul>
	 * @throws BrokerServiceException
	 */
	private Set<LineOfBusinessEnum> getAllAccessibleLinesOfBusiness() throws BrokerServiceException {
		Set<LineOfBusinessEnum> result = null;

		if(this.authentificationController.isMasterRole()) {
			result = new HashSet<LineOfBusinessEnum>(LineOfBusinessEnum.values().length);
			result.add(PERSONAL_LINE);
			result.add(COMMERCIAL_LINE);
		} else {
			String userName = this.authentificationController.getCurrentAccountUId();

			if (userName != null) {
				UserAccount userAccount = this.accountsBusinessProcess.findByUId(userName);

				if (userAccount != null && userAccount.getUserAccessProfiles() != null) {

					result = new HashSet<LineOfBusinessEnum>(LineOfBusinessEnum.values().length);
					for (UserAccessProfile userAccessProfile : userAccount.getUserAccessProfiles()) {
						if (userAccessProfile.getExpiryDate() == null) {
							if(AccessProfileEnum.PL == userAccessProfile.getAccessProfile().getName()) {
								result.add(PERSONAL_LINE);
							} else if(AccessProfileEnum.IRCA == userAccessProfile.getAccessProfile().getName()) {
								result.add(COMMERCIAL_LINE);
							}
						}
					}
				}
			}
		}

		return result == null ? Collections.<LineOfBusinessEnum>emptySet() : result;
	}


	/**
	 * Gets the check quote access.
	 *
	 * BR5113 If the current user is a participating point of sale and the
	 * selected quote has been reassigned to another point of sale since the
	 * List of Quotes/Search Results were first displayed, a message indicating
	 * the selected quote is no longer available w BR5390 Web Zone user access:
	 *
	 *
	 * @return the check quote access
	 * @throws BrokerServiceException
	 */

	public Boolean getCheckQuoteAccess() throws BrokerServiceException {
		Boolean checkQuoteAccess = false;

		ApplicationIdEnum app = null;
		LineOfBusinessEnum lob = null;
		String company = null;
		this.getCurrentContext(company, app, lob);

		if (this.authentificationController.isMasterRole()) {
			checkQuoteAccess = true;
		} else {
			checkQuoteAccess = false;
			String userName = this.authentificationController.getCurrentAccountUId();
			if (userName != null) {
				UserAccount userAccount = this.accountsBusinessProcess.findByUId(userName);

				if (userAccount != null) {
					String masterCode = getMasterCode();
					if (masterCode != null) {
						for (BrokerWebOfficeAccess brokerWebOfficeAccess : userAccount.getBrokerWebOfficeAccesses()) {
							if (brokerWebOfficeAccess.getMasterOwnerCode().equals(masterCode)) {
								checkQuoteAccess = Boolean.TRUE;
							}
						}
					}
				}
			}
		}

		if (!checkQuoteAccess && this.isAllowed(company, app, lob)) {
				checkQuoteAccess = true;
		}

		return checkQuoteAccess;
	}

	protected boolean isAllowed(String company, ApplicationIdEnum app, LineOfBusinessEnum lob) {
		List<String> brokers = this.authentificationController.getAvailableMasters();
		return this.brokerService.checkAccess(brokers, company, app, lob);
	}

	protected void getCurrentContext(String company, ApplicationIdEnum app, LineOfBusinessEnum lob) {
		ApplicationModeEnum mode = null;

		// get Reference number
		String aReferenceNo = (String) FacesContext.getCurrentInstance().getExternalContext().getRequestParameterMap()
				.get("referenceNo");
		if (aReferenceNo == null) {
			// Requests comming from the QuoteView
			aReferenceNo = (String) FacesContext.getCurrentInstance().getViewRoot().getAttributes().get("referenceNo");
		}

		PolicyVersion policy = this.commandBusinessProcess.findLatestPolicyVersion(aReferenceNo);

		if (policy != null) {
			mode = policy.getInsurancePolicy().getApplicationMode();
			if (mode != null) {
				if (mode == ApplicationModeEnum.REGULAR_QUOTE) {app = ApplicationIdEnum.AUTO_REGULAR_QUOTE;}
				else if (mode == ApplicationModeEnum.QUICK_QUOTE) {app = ApplicationIdEnum.AUTO_QUICKQUOTE;}
			}
			lob = LineOfBusinessEnum.valueOfCode(policy.getInsurancePolicy().getLineOfBusiness().getCode());
			company = policy.getInsurancePolicy().getManufacturerCompany().getCode();
		} else {
			app = ApplicationIdEnum.AUTO_QUICKQUOTE;
			lob = LineOfBusinessEnum.PERSONAL_LINE;
		}
	}

	/**
	 * Gets the master code.
	 *
	 * @return the master code
	 */
	private String getMasterCode() {
		// QuoteView requested with parameters
		String referenceNo = (String) FacesContext.getCurrentInstance().getExternalContext().getRequestParameterMap().get("referenceNo");

		if (referenceNo == null) {
			// Requests comming from the QuoteView
			referenceNo = (String) FacesContext.getCurrentInstance().getViewRoot().getAttributes().get("referenceNo");
		}
		PolicyVersion policyVersion = this.commandBusinessProcess.findLatestPolicyVersion(referenceNo);

		if (policyVersion == null) {
			return null;
		}

		IBrokersInfos brokersInfos = this.brokerService.getBrokersInfosBySubBrokerId(policyVersion.getInsurancePolicy().getLatestSubBrokerAssignment()
				.getCifSubBrokerId(), this.provinceController.getCompanyEnumCode().getMasterBrokerCompanyNumber());

		return brokersInfos != null ? brokersInfos.getMasterOwnerNo() : null;
	}

}
