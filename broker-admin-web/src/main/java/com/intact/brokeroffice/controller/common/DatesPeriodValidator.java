/**
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance 
 * Copyright (c) 2009, Intact Insurance, All rights reserved.<br>
 */
package com.intact.brokeroffice.controller.common;

import java.util.Date;

import org.springframework.stereotype.Component;

import com.intact.brokeroffice.helper.FaceMessageHelper;

/**
 * <AUTHOR>
 *
 */

@Component
public class DatesPeriodValidator {
	
private boolean showErrorMessage = true;
	
	/**
	* method to validate the form performance metric
	* @param aDateFrom
	* @param aDateTo
	* @return true, if successful
	*/
	public boolean validate(Date aDateFrom, Date aDateTo, String formName)
	{				
		boolean valid = true;		
		
		// no info validation
		if (aDateFrom==null &&	aDateTo ==null) 
		{			
			if(this.showErrorMessage){
				FaceMessageHelper.addErrorMessage(formName + ":calendarFrom", "error.msg.period.start.date", this);
				FaceMessageHelper.addErrorMessage(formName +":calendarTo", "error.msg.period.end.date", this);
			}
			return false;
		}
		
		// dates from and to validation
		valid = validateDates(aDateFrom, aDateTo, valid, formName);
				
		return valid;
	}
	
	public boolean validateDates(Date aDateFrom, Date aDateTo, boolean valid, String formName){
		
		// From date validation - A period From date must be selected
		if(aDateFrom==null)
		{
			if(this.showErrorMessage){
				FaceMessageHelper.addErrorMessage(formName + ":calendarFrom", "error.msg.period.start.date", this);
			}
			return false;
		}		
		
		// To date validation - A period To date must be selected
		if(	aDateTo ==null){
			if(this.showErrorMessage){
				FaceMessageHelper.addErrorMessage(formName +":calendarTo", "error.msg.period.end.date", this);
			}
			return false;
		}
		
		// The selected period From date cannot be in the future otherwise 
		// display future date error
		if(aDateFrom.compareTo(new Date()) > 0)
		{
			//MSG500
			if(this.showErrorMessage){
				FaceMessageHelper.addErrorMessage(formName +":calendarFrom", "error.msg.future.date", this);
			}
			return false;
		}		
		
		// To date validation - A period To date must be selected
		if(aDateTo.compareTo(new Date()) > 0){
			//MSG500
			if(this.showErrorMessage){
				FaceMessageHelper.addErrorMessage(formName +":calendarTo", "error.msg.future.date", this);
			}
			return false;
		}		
		
		// The selected period From date must be prior or equal to period To date 
		if(valid && aDateFrom.compareTo(aDateTo) > 0)
		{
			//MSG499
			if(this.showErrorMessage){
				FaceMessageHelper.addErrorMessage(formName +":calendarFrom", "error.msg.period.start.must.be.prior.or.equal.to.end.date", this);
			}
			return false;
		}	
		
		return valid;
	}

	/**
	 * Gets error message display indicator
	 * @return true if error message should be display
	 */
	public boolean isShowErrorMessage() {
		return this.showErrorMessage;
	}

	/**
	 * Sets the show error message indicator 
	 * @param aShowErrorMessage the show error message indicator to set
	 */
	public void setShowErrorMessage(boolean aShowErrorMessage) {
		this.showErrorMessage = aShowErrorMessage;
	}
}
