/**
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance
 * Copyright (c) 2010, Intact Insurance, All rights reserved.<br>
 */
package com.intact.brokeroffice.controller.spoe;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;

import jakarta.faces.context.FacesContext;
import jakarta.faces.model.SelectItem;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpSession;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.ing.canada.cif.domain.enums.ApplicationIdEnum;
import com.ing.canada.cif.domain.enums.LineOfBusinessEnum;
import com.ing.canada.common.web.api.auth.TamTokensEnum;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.intact.brokeroffice.business.accounts.IAccountsBusinessProcess;
import com.intact.brokeroffice.business.exception.BrokerServiceException;
import com.intact.brokeroffice.controller.accounts.MasterBrokerBean;
import com.intact.brokeroffice.controller.enums.SessionConstantsEnum;
import com.intact.brokeroffice.controller.helper.ProvinceHelper;
import com.intact.brokeroffice.controller.province.ProvinceClusterEnum;
import com.intact.brokeroffice.controller.province.ProvinceController;
import com.intact.brokeroffice.controller.province.SubscriptionCompanyEnum;

import util.ProvinceCompanyConverter;

@Component
@Scope("session")
public class SpoeController {

	private final Logger LOGGER = Logger.getLogger(this.getClass().getName());

	private List<SelectItem> accessLevel = new ArrayList<SelectItem>();

	@Autowired
	@Qualifier("ldap-group-program-admins")
	private String ldapGroupProgramAdmins;

	@Autowired
	@Qualifier("ldap-group-admins")
	private String ldapGroupAdmins;

	@Autowired
	@Qualifier("spoe-mode")
	private String spoeMode;

	@Autowired
	private IAccountsBusinessProcess accountsBusinessProcess;

	private List<MasterBrokerBean> masterBrokers;

	private SpoeBean spoeBean = new SpoeBean();

	private List<ProvinceCodeEnum> provinces;

	/** The province controller. */
	@Autowired
	private ProvinceController provinceController;

	/**
	 * Gets the spoe bean.
	 *
	 * @return the spoeBean
	 */
	public SpoeBean getSpoeBean() {
		return this.spoeBean;
	}

	/**
	 * Gets the list of access level available for the application (to be displayed
	 * in a select list)
	 *
	 * @return List<SelectItem>
	 */
	public List<SelectItem> getAccessLevel() {
		this.initAccessLevel();
		return this.accessLevel;
	}

	//TODO: investigate the need to invalidate the current session
	public void initialize() {
//		HttpSession session = (HttpSession) FacesContext.getCurrentInstance().getExternalContext().getSession(false);
//		if (session != null) {
//			session.invalidate();
//		}
	}

	/**
	 * init the access level available for the application
	 */
	private void initAccessLevel() {
		if (this.accessLevel.isEmpty()) {

			// WebZone-Program-Admins-Intact
			for (ProvinceCodeEnum province : ProvinceHelper.activeProvinces) {
				this.accessLevel.add(ProvinceHelper.AddItemAccesLevel(this.ldapGroupProgramAdmins, province));
			}
			// WebZone-Intact-Admins-activeProvinces
			for (ProvinceCodeEnum province : ProvinceHelper.activeProvinces) {
				this.accessLevel.add(ProvinceHelper.AddItemAccesLevel(this.ldapGroupAdmins, province));
			}

			// WebZone-Intact-Admins-ON + WebZone-Intact-Admins-QC
			this.accessLevel.add(ProvinceHelper.AddItemAccesLevel(this.ldapGroupAdmins, ProvinceCodeEnum.ONTARIO,
					ProvinceCodeEnum.QUEBEC));

			// WebZone-Intact-Admins-ON + WebZone-Intact-Admins-QC +
			// WebZone-Intact-Admins-AB
			this.accessLevel.add(ProvinceHelper.AddItemAccesLevel(this.ldapGroupAdmins, ProvinceCodeEnum.ONTARIO,
					ProvinceCodeEnum.QUEBEC, ProvinceCodeEnum.ALBERTA));

			// WebZone-Program-Admins-Intact ON-QC-AB
			this.accessLevel.add(ProvinceHelper.AddItemAccesLevel(this.ldapGroupProgramAdmins, ProvinceCodeEnum.ONTARIO,
					ProvinceCodeEnum.QUEBEC, ProvinceCodeEnum.ALBERTA));

			// TODO adjust aligning with desired access levels
			// WebZone-Program-Admins-Intact ROC
			this.accessLevel
					.add(ProvinceHelper.AddItemAccesLevel(this.ldapGroupProgramAdmins, ProvinceClusterEnum.ALL));

		}
	}

	/**
	 * This is the SPOE version of the login
	 *
	 * @return the string
	 * @throws BrokerServiceException
	 */
	public String login() throws Exception {
		FacesContext context = FacesContext.getCurrentInstance();
		HttpSession session = (HttpSession) context.getExternalContext().getSession(true);
		session.setAttribute(TamTokensEnum.TAM_SECURITY_ROLES_ATTRIB.getTokenValue(),
				this.spoeBean.getSelectedAccessLevel());

		LOGGER.info("GROUP : " + this.spoeBean.getSelectedAccessLevel());
		// CLASSIC USER ID in session
		session.setAttribute(TamTokensEnum.TAM_USER_CLASSIC_USER_ID.getTokenValue(), "DA7143");

		String defaultProvince = this.accountsBusinessProcess.getDefaultProvince(
				!StringUtils.isBlank(this.spoeBean.getUserId()) ? this.spoeBean.getUserId() : "spoeUId");

		session.setAttribute(SessionConstantsEnum.COMPANY.getSessionConstant(),
				defaultProvince != null ? ProvinceCompanyConverter.convertProvinceToSubBrokerCompany(defaultProvince) : this.spoeBean.getSelectedCompany());

		List<String> selectedMasters = new ArrayList<String>();

		this.provinceController.setProvinces(null);
		this.provinceController.setCompanies(null);

		session.setAttribute(TamTokensEnum.TAM_USER_NAME_CREDENTIAL.getTokenValue(),
				!StringUtils.isBlank(this.spoeBean.getUserId()) ? this.spoeBean.getUserId() : "spoeUId");

		session.setAttribute(SessionConstantsEnum.AVAILABLE_MASTERS.getSessionConstant(), selectedMasters);
		manageAccessLevel(session);

		return "index";
	}

	/**
	 * manage province list
	 *
	 * @param aSession
	 * @param provinceList
	 */
	private void manageAccessLevel(HttpSession aSession) throws Exception {
		String userRole = this.spoeBean.getSelectedAccessLevel();

		if (userRole.contains(this.ldapGroupAdmins)) {
			aSession.setAttribute(TamTokensEnum.TAM_SECURITY_ROLES_ATTRIB.getTokenValue(), this.ldapGroupAdmins);
			manageCompanies(aSession, userRole, this.ldapGroupAdmins);

		} else if (userRole.contains(this.ldapGroupProgramAdmins)) {
			aSession.setAttribute(TamTokensEnum.TAM_SECURITY_ROLES_ATTRIB.getTokenValue(), this.ldapGroupProgramAdmins);
			manageCompanies(aSession, userRole, this.ldapGroupProgramAdmins);
		}
	}

	/**
	 * Method to manage the company based on the ldap security settings applicable
	 * for only ldapGroupProgramAdmins
	 *
	 * @param httpServletRequest
	 * @param userRole
	 * @param ldapSecurityGroup
	 */
	public void manageCompanies(HttpSession aSession, String userRole, String ldapSecurityGroup) throws Exception {

		int nbCompanies = ProvinceHelper.manageCompanies(aSession, userRole, ldapSecurityGroup, provinceController);
		if (nbCompanies > 1) {
			retrieveDefaultCompany();
		}
	}

	/**
	 * retrieves the cookie for the default company
	 */
	private void retrieveDefaultCompany() {
		FacesContext context = FacesContext.getCurrentInstance();
		HttpServletRequest httpServletRequest = (HttpServletRequest) context.getExternalContext().getRequest();
		HttpSession aSession = (HttpSession) context.getExternalContext().getSession(true);
		String defaultCompany = null;
		if (ProvinceController.searchForBrokerDefaultCompany(httpServletRequest) != null) {
			Cookie aCookie = ProvinceController.searchForBrokerDefaultCompany(httpServletRequest);
			defaultCompany = aCookie.getValue();
		}

		// use ST parameter to set the province parameter
		else {
			defaultCompany = ProvinceCompanyConverter.convertProvinceToSubBrokerCompany(this.accountsBusinessProcess.getDefaultProvince(this.spoeBean.getUserId()));
			// manage scenario WHERE ST is NULL
			if (defaultCompany == null) {
				List<String> provinceAvailables = (List<String>) aSession
						.getAttribute(SessionConstantsEnum.AVAILABLE_PROVINCES.getSessionConstant());
				defaultCompany = provinceAvailables.get(0);
			}
		}

		this.provinceController.setSelectedCompany(SubscriptionCompanyEnum.fromCode(defaultCompany));
		aSession.setAttribute(SessionConstantsEnum.COMPANY.getSessionConstant(), defaultCompany);
		this.provinceController.reset();
	}

	/**
	 * Gets the list of masters (use for masters checkboxes to select).
	 *
	 * @return the master brokers
	 */
	public List<MasterBrokerBean> getMasterBrokers() {
		// by default the dropdown points to ldap broker, in any scenario where
		// we have ldap broker, we want to display
		// the list of masters

		if (this.spoeBean.getSelectedAccessLevel() == null) {

			Map<String, String> masterBrokerMap = this.accountsBusinessProcess.getAssignedMasterBrokers(
					this.provinceController.getCompanyEnumCode(), this.provinceController.getProvinceCode().getCode(),
					ApplicationIdEnum.asList(), LineOfBusinessEnum.asList());
			List<MasterBrokerBean> masterBrokerBeans = new ArrayList<MasterBrokerBean>();

			for (String key : masterBrokerMap.keySet()) {
				String value = masterBrokerMap.get(key);
				MasterBrokerBean masterBrokerBean = new MasterBrokerBean(key, value);
				masterBrokerBeans.add(masterBrokerBean);
			}
			this.masterBrokers = masterBrokerBeans;
		} else {
			this.masterBrokers = new ArrayList<MasterBrokerBean>();
		}

		return this.masterBrokers;
	}

	/**
	 * Gets the checks if is spoe mode.
	 *
	 * @return the checks if is spoe mode
	 */
	public String getIsSpoeMode() {
		return this.spoeMode;
	}

	/**
	 * Gets the province list
	 *
	 * @return the province list
	 */
	public List<ProvinceCodeEnum> getProvinces() {
		if (this.provinces == null) {
			this.provinces = new ArrayList<ProvinceCodeEnum>();
			for (ProvinceCodeEnum provinceCodeEnum : ProvinceHelper.activeProvinces) {
				this.provinces.add(provinceCodeEnum);
			}
		}
		return this.provinces;
	}

	/**
	 * initialize the province value
	 */
	public void onProvinceChanged() {
		FacesContext context = FacesContext.getCurrentInstance();
		HttpSession session = (HttpSession) context.getExternalContext().getSession(true);
		session.setAttribute(SessionConstantsEnum.PROVINCE.getSessionConstant(), this.spoeBean.getSelectedProvince());
		this.provinceController.reset();
	}

	public void onAccessLevelChanged() {
		this.spoeBean.setUserId("");
		String selectedAccessLevel = this.spoeBean.getSelectedAccessLevel();

		if (ProvinceHelper.containAccessLevelAndProvinces(selectedAccessLevel, this.ldapGroupAdmins,
				ProvinceCodeEnum.ONTARIO)) {
			this.spoeBean.setUserId("m_webadmins");
		} else if (ProvinceHelper.containAccessLevelAndProvinces(selectedAccessLevel, this.ldapGroupAdmins,
				ProvinceCodeEnum.ALBERTA)) {
			this.spoeBean.setUserId("m_webadmins3");
		} else if (ProvinceHelper.containAccessLevelAndProvinces(selectedAccessLevel, this.ldapGroupProgramAdmins,
				ProvinceCodeEnum.ONTARIO)) {
			this.spoeBean.setUserId("m_webpadmon");
		} else if (ProvinceHelper.containAccessLevelAndProvinces(selectedAccessLevel, this.ldapGroupProgramAdmins,
				ProvinceCodeEnum.QUEBEC)) {
			this.spoeBean.setUserId("m_webpadmqc");
		} else if (ProvinceHelper.containAccessLevelAndProvinces(selectedAccessLevel, this.ldapGroupProgramAdmins,
				ProvinceCodeEnum.ALBERTA)) {
			this.spoeBean.setUserId("m_webpadmab");
		} else if (ProvinceHelper.containAccessLevelAndProvinces(selectedAccessLevel, this.ldapGroupAdmins,
				ProvinceCodeEnum.ONTARIO, ProvinceCodeEnum.QUEBEC)) {
			this.spoeBean.setUserId("m_webadmins2");
		} else if (ProvinceHelper.containAccessLevelAndProvinces(selectedAccessLevel, this.ldapGroupProgramAdmins,
				ProvinceCodeEnum.ONTARIO, ProvinceCodeEnum.QUEBEC)) {
			this.spoeBean.setUserId("m_webpadm2");
		} else if (ProvinceHelper.containAccessLevelAndProvinces(selectedAccessLevel, this.ldapGroupAdmins,
				ProvinceCodeEnum.ONTARIO, ProvinceCodeEnum.QUEBEC, ProvinceCodeEnum.ALBERTA)) {
			this.spoeBean.setUserId("m_webadmins4");
		} else if (ProvinceHelper.containAccessLevelAndProvinces(selectedAccessLevel, this.ldapGroupProgramAdmins,
				ProvinceCodeEnum.ONTARIO, ProvinceCodeEnum.QUEBEC, ProvinceCodeEnum.ALBERTA)) {
			this.spoeBean.setUserId("m_webpadm3");
		}
	}

}
