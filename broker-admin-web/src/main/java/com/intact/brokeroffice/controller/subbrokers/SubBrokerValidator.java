/**
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance
 * Copyright (c) 2010, Intact Insurance, All rights reserved.<br>
 */
package com.intact.brokeroffice.controller.subbrokers;

import com.ing.canada.cif.domain.enums.ApplicationIdEnum;
import com.ing.canada.cif.domain.enums.BrokerWebAccessTypeEnum;
import com.ing.canada.cif.domain.enums.LineOfBusinessEnum;
import com.intact.brokeroffice.business.dto.*;
import com.intact.brokeroffice.helper.FaceMessageHelper;
import com.intact.brokeroffice.web.dto.JSFImageDTO;
import com.intact.brokeroffice.web.dto.PhoneSectionCodeEnum;
import com.intact.tools.string.service.StringApplicationService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.Predicate;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.validator.EmailValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * This class contains the validation required by the sub broker section of the
 * application
 */
@Service
public class SubBrokerValidator {

	private final int PHONE_NUMBER_LENGTH = 10;
	private final int PHONE_NUMBER_AND_TOLL_FREE_LENGTH = 11;

	private final int MAX_EMAIL_ADDRESS_CARACTERS = 73;

	private final boolean MANDATORY = true;

	private final long MAX_FILE_SIZE = 2097152;
	private final String ERROR_INVALID_FILE_TYPE = "error.file.not.a.recognized.image.msg";
	private final String ERROR_INVALID_SVG_FILE_TYPE = "error.file.not.a.recognized.image.svg.msg";
	private final String ERROR_FILE_SIZE_TOO_LARGE = "error.file.size.too.large.msg";

	private final String ERR_MISSING_INFO = "error.missingInfo";
	private final String ERR_ONLINE_NAME_MANDATORY_NO_LOGO = "error.online.name.mandatory.no.logo";

	@Autowired
	private StringApplicationService stringService = null;

	public SubBrokerValidator() {

	}

	public StringApplicationService getStringService() {
		return stringService;
	}

	public void setStringService(StringApplicationService stringService) {
		this.stringService = stringService;
	}

	/**
	 * Validates the data from the create/modify a broker page.
	 *
	 * BR5256 User must select a valid image file (BMP, GIF, JPG or PNG) of less
	 * than 2Mb. If the file size is equal to or exceeds 2Mb, display the File Size
	 * Too Large error message
	 *
	 * BR5259 If the file is not a recognized image file, display the Not a
	 * Recognized Image error message
	 *
	 * @param subBrokerBean
	 *            the sub broker bean
	 *
	 * @return true, if successful
	 *
	 * @throws Exception
	 *             the exception
	 */

	public boolean validate(SubBrokerDTO subBroker) throws Exception {

		boolean valid = true;

		valid = this.validatePhoneNumbers(subBroker);
		valid = valid & this.validateBundlePhoneNumbers(subBroker);
		valid = valid & this.validateEmailAddresses(subBroker);
		valid = valid & this.validateBusinessHours(subBroker);
		valid = valid & this.validateVisuals(subBroker);
		valid = valid & this.validateOnlineName(subBroker);

		return valid;
	}

	/**
	 * Online name is mandatory if the sub-broker has no logos
	 *
	 * @param subBroker
	 * @return true in the following cases otherwise false:
	 * <ul>
	 *  <li>the online name is non-empty</li>
	 *  <li>the online name is empty but there exists at least one image associated to the sub-broker </li>
	 * </ul>
	 */
	private boolean validateOnlineName(SubBrokerDTO subBroker) {
		if(StringUtils.isBlank(subBroker.getOnlineName())) {

			// Verify if there exists at least one image
			boolean containsAtLeastOneImage = CollectionUtils.exists(subBroker.getImages().values(), new Predicate() {
				@Override
				public boolean evaluate(Object object) {
					return !((JSFImageDTO) object).isEmpty();
				}
			});

			if(!containsAtLeastOneImage) {
				FaceMessageHelper.addErrorMessage("subBrokerForm:onlineName", ERR_ONLINE_NAME_MANDATORY_NO_LOGO, this);
				return false;
			}
		}

		return true;
	}

	protected boolean validateVisuals(SubBrokerDTO subBroker) {

		boolean valid = true;

		for (ImageDTO image : subBroker.getImages().values()) {
			valid = valid & this.validate(image, "image_" + image.getContext());
		}

		return valid;
	}

	private boolean validateBusinessHours(SubBrokerDTO subBroker) throws Exception {

		boolean valid = true;

		for (BusinessHourDTO hour : subBroker.getBusinessHours().values()) {
			valid = valid & this.validateMissingInfo("hour-open-" + hour.getDayOfWeek(), hour.getOpenHour());
			valid = valid & this.validateMissingInfo("hour-close-" + hour.getDayOfWeek(), hour.getCloseHour());
		}

		return valid;
	}

	protected boolean validateEmailAddresses(SubBrokerDTO subBroker) throws Exception {

		boolean valid = true;

		for (LineOfBusinessDTO lob : subBroker.getLinesOfBusiness().values()) {

			// 152045 can be empty if not active
			if (lob.getContact().getEmailAddress()==null
					|| "".equals(lob.getContact().getEmailAddress())){
				if (!lob.getLineOfBusiness().equals(LineOfBusinessEnum.COMMERCIAL_LINE.getCode())
						&& isSubBrokerActive(lob))	{
					valid =  false;
					this.addError("email" + lob.getLineOfBusiness(), ERR_MISSING_INFO);
				}
			}

			else {
				valid = valid
						& validateMissingInfo("email" + lob.getLineOfBusiness(), lob.getContact().getEmailAddress());

				if (lob.getContact().getEmailAddress().length() > 0
						&& !EmailValidator.getInstance().isValid(lob.getContact().getEmailAddress())
						|| lob.getContact().getEmailAddress().length() > MAX_EMAIL_ADDRESS_CARACTERS) {
					this.addError("email" + lob.getLineOfBusiness(), "error.email");
					valid = false;
				}
			}

		}

		for (ServiceDTO service : subBroker.getServices().values()) {
			if (service.getContact().getEmailAddress().length() > 0
					&& !EmailValidator.getInstance().isValid(service.getContact().getEmailAddress())
					|| service.getContact().getEmailAddress().length() > MAX_EMAIL_ADDRESS_CARACTERS) {
				this.addError("service.email." + service.getApplication().getId(), "error.email");
				valid = false;
			}
		}

		return valid;

	}

	// will return true is subbroker is assignable for an application
	private boolean isSubBrokerActive(LineOfBusinessDTO lob) {

		for (ApplicationDTO app : lob.getApplications().values()) {
			if (!"NONE".equals(app.getAccessType())) return true;
		}

		return false;

	}

	/**
	 * Validate the {@link SubBrokerImage} and clears its content if not valid
	 *
	 * @param the
	 *            current
	 * @param image
	 *            The {@link SubBrokerImage} instance to validate
	 * @param imageId
	 *            The id of the error message
	 * @return true if no error is found otherwise false
	 */
	protected Boolean validate(ImageDTO image, String imageId) {
		return validate(image, imageId,
				image.getType().equals("SVG") ? ERROR_INVALID_SVG_FILE_TYPE : ERROR_INVALID_FILE_TYPE,
				ERROR_FILE_SIZE_TOO_LARGE);
	}

	/**
	 * Validate the {@link SubBrokerImage} and clears its content if not valid
	 *
	 * @param image
	 *            The {@link SubBrokerImage} instance to validate
	 * @param imageId
	 *            The id of the error message
	 * @param invalidErrorKey
	 *            error message key for invalid file format
	 * @param invalidSizeError
	 *            error message key for file size too big
	 * @return true if no error is found otherwise false
	 */
	protected boolean validate(ImageDTO image, String imageId, String invalidErrorKey, String invalidSizeError) {

		JSFImageDTO jsfImage = 	(JSFImageDTO) image;

		if (jsfImage.getImage() != null) {
			if ((!SubBrokerImageHelper.isImage(jsfImage.getImage()) && !image.getType().equals("SVG"))
					|| (!SubBrokerImageHelper.isSVGImage(jsfImage.getImage()) && image.getType().equals("SVG"))) {
				FaceMessageHelper.addErrorMessage("subBrokerForm:" + imageId, invalidErrorKey, this);
				jsfImage.clearImage();
				return false;
			} else if (jsfImage.getImage().getSize() > MAX_FILE_SIZE) {
				FaceMessageHelper.addErrorMessage("subBrokerForm:" + imageId, invalidSizeError, this);
				jsfImage.clearImage();
				return false;
			}
		}

		return true;
	}

	protected boolean validatePhoneNumbers(SubBrokerDTO subBroker) throws Exception {

		boolean valid = true;

		for (LineOfBusinessDTO lob : subBroker.getLinesOfBusiness().values()) {
			for (ApplicationDTO app : lob.getApplications().values())
				for (PhoneDTO phone : app.getPhones().values()) {

					valid = valid & this.validatePhoneField(
							app.getAccessType().equals(BrokerWebAccessTypeEnum.QUOTE_INTACT.getValue()) || app.getAccessType().equals(BrokerWebAccessTypeEnum.QUOTE_INTACT_AND_BROKER.getValue()) ? MANDATORY : !MANDATORY,
							phone.getIntactPhone(), phone.getTollFree(),
							"subBrokerForm:phone_" + lob.getLineOfBusiness() + "_" + app.getId() + "_" + phone.getUsage() + "_I",
							BrokerWebAccessTypeEnum.QUOTE_INTACT);

					valid = valid & this.validatePhoneField(
							app.getAccessType().equals(BrokerWebAccessTypeEnum.QUOTE_BROKER.getValue()) || app.getAccessType().equals(BrokerWebAccessTypeEnum.QUOTE_INTACT_AND_BROKER.getValue()) ? MANDATORY : !MANDATORY,
							phone.getBrokerPhone(), phone.getTollFree(),
							"subBrokerForm:phone_" + lob.getLineOfBusiness() + "_" + app.getId() + "_" + phone.getUsage() + "_B",
							BrokerWebAccessTypeEnum.QUOTE_BROKER);
				}
		}

		for (ServiceDTO service : subBroker.getServices().values()) {

			for (PhoneDTO phone : service.getApplication().getPhones().values()) {

				valid = valid & this.validatePhoneField(!MANDATORY, phone.getIntactPhone(), phone.getTollFree(),"subBrokerForm:service.phone." + service.getApplication().getId());
			}

		}

		return valid;
	}

	/**
	 * @see #validatePhoneField(boolean, String, String, BrokerWebAccessTypeEnum)
	 * @return true, if successful, otherwise false
	 */
	private boolean validatePhoneField(boolean mandatory, String value, boolean isTollFree, String prefix) throws Exception {
		return validatePhoneField(mandatory, value, isTollFree, prefix, null);
	}

	/**
	 * Validate a phone number field BR5249 If any value is entered it must be
	 * numeric and exactly three (3) digits, otherwise, display the Telephone Number
	 * error message.: BR5163 If this field is entered, the complete phone number
	 * must be entered BR5164 The exact number of digits must be entered in each
	 * field as follows: Area code = 3 Exchange = 3 Number = 4: BR5165 If less than
	 * the required number of digits are entered in any of the three input areas,
	 * display the Phone Number Validation error message.:
	 *
	 * @param mandatory whether the the field is mandatory
	 * @param value the value of the field
	 * @param id the prefix
	 * @param webAccessType the {@link BrokerWebAccessTypeEnum} to be used in this validation process
	 * @return true if the field is valid otherwise, false.
	 * @throws Exception in case of errors
	 */
	private boolean validatePhoneField(boolean mandatory, String value, boolean isTollFree, String id, BrokerWebAccessTypeEnum webAccessType) throws Exception {
		boolean valid = true;

		String phoneNumber = stripPhoneNumber(value);

		if (!mandatory && this.getStringService().isEmpty("", phoneNumber)) {
			return valid;
		}
		if (this.getStringService().isEmpty("", phoneNumber)
				|| !this.getStringService().isNumeric("", phoneNumber)) {
			addPhoneNumberErrorMessage(id, webAccessType);
			return false;
		}

		if (isTollFree) {
			if (phoneNumber.length() > PHONE_NUMBER_AND_TOLL_FREE_LENGTH) {
				addPhoneNumberErrorMessage(id, webAccessType);
				return false;
			}
		} else if (phoneNumber.length() > PHONE_NUMBER_LENGTH) {
			addPhoneNumberErrorMessage(id, webAccessType);
			return false;
		}

		return valid;
	}

	/**
	 * Add a phone number error message in the context based on the {@link BrokerWebAccessTypeEnum}
	 * @param id id of the message to be added to the context
	 * @param webAccessType The {@link BrokerWebAccessTypeEnum}
	 */
	private void addPhoneNumberErrorMessage(String id, BrokerWebAccessTypeEnum webAccessType) {
		if(webAccessType == null) {
			FaceMessageHelper.addErrorMessage(id, "error.phone.number.msg", this);
		} else {
			FaceMessageHelper.addErrorMessage(id, "error.phone.number.msg." + webAccessType.getValue(), this);
		}
	}

	protected String stripPhoneNumber(String phoneNumber) {
		if (phoneNumber == null)
			return phoneNumber;
		String strippedField = phoneNumber.replace("_", "");
		strippedField = strippedField.replace("-", "");
		strippedField = strippedField.replace(" ", "");
		strippedField = strippedField.replace("(", "");
		strippedField = strippedField.replace(")", "");
		return strippedField;
	}

  /**
   * This method verifies that if one of the bundle phone is there,
   * the other must be there to
   *
   * @param subBroker
   *            the sub broker dto
   *
   * @return true, if successful
   *
   * @throws Exception
   *             the exception
   */
  protected boolean validateBundlePhoneNumbers(SubBrokerDTO subBroker) throws Exception {

    boolean foundDefault = false;
    boolean foundOffpg = false;

    for (LineOfBusinessDTO lob : subBroker.getLinesOfBusiness().values()) {
      for (ApplicationDTO app : lob.getApplications().values())
        for (PhoneDTO phone : app.getPhones().values()) {
          if (lob.getLineOfBusiness().equals(LineOfBusinessEnum.PERSONAL_LINE.getCode())
              && app.getId().equals(ApplicationIdEnum.WEB_QUOTE.getCode())
              && StringUtils.isNotEmpty(phone.getPhone())) {
            if (phone.getUsage().equals(PhoneSectionCodeEnum.OFFER.getCode())) {
              foundOffpg = true;
            } else {
              foundDefault = true;
            }
          }
        }
    }
    boolean valid = foundDefault == foundOffpg;

    if (!valid) {
      String id = "subBrokerForm:phone_P_WQ_" + (foundDefault ? "OFFPG" : "");
      FaceMessageHelper.addErrorMessage(id, "error.phone.number.msg.bundle." + (foundDefault ? "offpg" : "default"), this);
    }

    return valid;
  }


	/**
	 * This method verifies that a field is not empty by introspection.
	 *
	 * @param field
	 *            the field
	 * @param valid
	 *            the valid
	 * @param subBrokerBean
	 *            the sub broker bean
	 *
	 * @return true, if successful
	 *
	 * @throws Exception
	 *             the exception
	 */
	protected boolean validateMissingInfo(String errorId, String field) throws Exception {

		if (this.getStringService().isEmpty("", field)) {
			FaceMessageHelper.addErrorMessage("subBrokerForm:" + errorId, ERR_MISSING_INFO, this);
			return false;
		}

		return true;
	}

	protected void addError(String errorId, String messageId) throws Exception {

		FaceMessageHelper.addErrorMessage("subBrokerForm:" + errorId, messageId, this);
	}
}
