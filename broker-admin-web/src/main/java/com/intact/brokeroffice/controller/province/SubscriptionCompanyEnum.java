package com.intact.brokeroffice.controller.province;

import com.ing.canada.cif.domain.enums.CifCompanyEnum;
import com.ing.canada.plp.domain.enums.ManufacturerCompanyCodeEnum;
 
public enum SubscriptionCompanyEnum {
	
	GC(CifCompanyEnum.INTACT_QC, "A - INTACT Quebec"),
	HAL(CifCompanyEnum.INTACT_HALIFAX, "6 - HALIFAX (daily halcion)"),
	WU(CifCompanyEnum.INTACT_AB, "3 - Western Union (daily halcion)");
	
	private CifCompanyEnum companyEnum;
	private ManufacturerCompanyCodeEnum manufacturerCode;
	private String label;
	private String code;
		
	private SubscriptionCompanyEnum (CifCompanyEnum companyEnum, String label) {
		this.companyEnum = companyEnum;
		this.manufacturerCode = ManufacturerCompanyCodeEnum.valueOfCode(companyEnum.getSubBrokerCompanyNumber());
		this.label = label;
		this.code = companyEnum.getSubBrokerCompanyNumber();
	}

	public CifCompanyEnum getCifCompany() {
		return this.companyEnum;
	}

	public ManufacturerCompanyCodeEnum getManufacturerCode() {
		return manufacturerCode;
	}

	public String getLabel() {
		return label;
	}

	public void setLabel(String label) {
		this.label = label;
	}
	
	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public static SubscriptionCompanyEnum fromCode(String value) {
		
		for (SubscriptionCompanyEnum company : SubscriptionCompanyEnum.values()) {
			if (company.getCifCompany() != null && company.getCifCompany().getSubBrokerCompanyNumber() != null
				&& company.getCifCompany().getSubBrokerCompanyNumber().equals(value)) {
				return company;
			}
		}
		return null;
	}
	
}
