package com.intact.brokeroffice.controller.fsa;

import java.util.List;

import com.intact.brokeroffice.business.dto.BusinessContextEnum;
import com.intact.brokeroffice.controller.province.SubscriptionCompanyEnum;
import org.primefaces.model.file.UploadedFile;

/**
 * Class representing a newly uploaded fsa file instance
 */
public class FsaUploadBean {
	/** the newly uploaded file */
	private UploadedFile file;

	/** the province to which the newly uploaded fsa file should be affected */
	private String province;

	/** the underwriting company to which the newly uploaded fsa file should be affected */
	private String company;

	/** the application id to which the newly uploaded fsa file should be affected */
	private String businessContext;

	/** the {@link CompanyCodeEnum available companys} to which the fsa file can be affected to  */
	private List<SubscriptionCompanyEnum> companyItems;

	/** the {@link BusinessContextEnum available business contexts} to which the fsa file can be affected to  */
	private List<BusinessContextEnum> businessContextItems;

	/** Constructor */
	public FsaUploadBean() {
	}

	/**
	 * BR6144 Browse function BR5596 Opens the File Upload Pop-up in the users default Windows folder
	 * @see {@link #file}
	 * @return the file
	 */
	public UploadedFile getFile() {
		return file;
	}

	/**
	 * UPLOADED FSA FILE CONTENT
	 *
	 * @param aFile the file to set
	 * @see {@link #file}
	 */
	public void setFile(UploadedFile file) {
		this.file = file;
	}

	/** @see {@link #province} */
	public String getProvince() {
		return province;
	}

	/** @see {@link #province} */
	public void setProvince(String province) {
		this.province = province;
	}

	/** @see {@link #company} */
	public String getCompany() {
		return company;
	}

	/** @see {@link #company} */
	public void setCompany(String company) {
		this.company = company;
	}

	public String getLineOfBusiness() {
		if (this.businessContext != null) {
			return this.businessContext.substring(0, 1);
		}
		return null;
	}

	/** @see {@link #businessContext} */
	public String getBusinessContext() {
		return businessContext;
	}

	/** @see {@link #businessContext} */
	public void setBusinessContext(String businessContext) {
		this.businessContext = businessContext;
	}

	/** @see {@link #companyItems} */
	public List<SubscriptionCompanyEnum> getCompanyItems() {
		return companyItems;
	}

	/** @see {@link #companyItems} */
	public void setCompanyItems(List<SubscriptionCompanyEnum> companyItems) {
		this.companyItems = companyItems;
	}


	/** @see {@link #businessContextItems} */
	public List<BusinessContextEnum> getBusinessContextItems() {
		return businessContextItems;
	}

	/** @see {@link #businessContextItems} */
	public void setBusinessContextItems(List<BusinessContextEnum> businessContextItems) {
		this.businessContextItems = businessContextItems;
	}
}
