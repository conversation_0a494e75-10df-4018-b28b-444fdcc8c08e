package com.intact.brokeroffice.controller.owner.viewusers;

import java.util.ArrayList;
import java.util.List;

import jakarta.faces.context.FacesContext;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.intact.brokeroffice.business.accounts.IAccountsBusinessProcess;
import com.intact.brokeroffice.business.exception.BrokerServiceException;
import com.intact.brokeroffice.controller.AbstractScrollerController;
import com.intact.brokeroffice.controller.accounts.AccountBean;
import com.intact.brokeroffice.controller.accounts.AccountsAdapter;
import com.intact.brokeroffice.controller.accounts.MasterBrokerBean;

/**
 * The Class ViewUsersController. This is a Request scope controller to view multiple users by owner in separate web page.
 */
@Component
@Scope("view")
public class ViewUsersController extends AbstractScrollerController {

	/** The selected owner id. */
	private String selectedOwnerNo;
	
	@Autowired
	private IAccountsBusinessProcess accountsBusinessProcess;
	
	private List<AccountBean> masterUsers = new ArrayList<AccountBean>(); 
	
	private MasterBrokerBean master = new MasterBrokerBean();
	
	@Autowired
	private AccountsAdapter accountsAdapter;
	
	/**
	 * Gets the selected owner no. Since we are in a Request scope, we must get the the selectedOwnerNoin the request parameters or
	 * in the viewRoot.
	 * 
	 * @return the selected owner no
	 * @throws BrokerServiceException 
	 */
	public String getSelectedOwnerNo() throws BrokerServiceException {
		if (this.selectedOwnerNo == null) {			
			// QuoteView requested with parameters
			this.selectedOwnerNo = (String) FacesContext.getCurrentInstance().getExternalContext()
			.getRequestParameterMap().get("selectedOwnerNo");
			if(this.selectedOwnerNo !=null){
				FacesContext.getCurrentInstance().getViewRoot().getAttributes().put("selectedOwnerNo", this.selectedOwnerNo);							
				viewUsers();		
			}
		}
		return this.selectedOwnerNo;
	}	
	
	/**
	 * View users of a selected master owner.
	 * @throws BrokerServiceException 
	 */
	private void viewUsers() throws BrokerServiceException {

		this.master.setName(this.accountsBusinessProcess.findMasterOwnerName(this.selectedOwnerNo));
		
		this.master.setNumber(this.selectedOwnerNo);
		
		this.accountsAdapter.LoadUsers(
				this.accountsBusinessProcess.findByOwner(this.selectedOwnerNo), 
				this.masterUsers);
		
	}

	/**
	 * Gets the master bean list
	 * @return the master bean list
	 */
	public List<AccountBean> getMasterUsers() {
		return this.masterUsers;
	}

	/**
	 * Sets the master bean list
	 * @param aMasterUsers the master bean list to set
	 */
	public void setMasterUsers(List<AccountBean> aMasterUsers) {
		this.masterUsers = aMasterUsers;
	}

	/**
	 * Sets the selected owner number
	 * @param aSelectedOwnerNo the selected owner number to set
	 */
	public void setSelectedOwnerNo(String aSelectedOwnerNo) {
		this.selectedOwnerNo = aSelectedOwnerNo;
	}

	/**
	 * Gets the master
	 * @return the master
	 */
	public MasterBrokerBean getMaster() {
		return this.master;
	}

	/**
	 * Sets the master
	 * @param aMaster the master to set
	 */
	public void setMaster(MasterBrokerBean aMaster) {
		this.master = aMaster;
	}	
	
}
