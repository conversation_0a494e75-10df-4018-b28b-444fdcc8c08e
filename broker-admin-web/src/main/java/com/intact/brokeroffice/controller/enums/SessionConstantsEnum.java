package com.intact.brokeroffice.controller.enums;

public enum SessionConstantsEnum {

	AVAILABLE_MASTERS("availableMasters"), //
	AVAILABLE_PROVINCES("availableProvinces"), //
	AVAILABLE_COMPANIES("availableCompanies"), //
	COMPANY("company"),
	BROKER_LANGUAGE("brokerLanguage"), PROVINCE("province"), //
	EMAIL_REFERENCE_NUMBER("emailReferenceNo"),
	APPLICATION_CODE_CC("CLCT"),
	APPLICATION_CODE_QQ("AQQK"),
	APPLICATION_CODE_AQ("AQRG"),
	LOGO_FILE_TYPE("LOGO"),
	LOGO_USAGE_PRINTING("PR"),
	LOGO_USAGE_UI("UI"),
	SVG_FILE_TYPE("SVG"),
	JPEG_FILE_TYPE("JPEG"),
	<PERSON>MP_FILE_TYPE("BMP"),
	G<PERSON>_FILE_TYPE("GIF"),
	PNG_FILE_TYPE("PNG"),
	OFFER_SECTION_CODE("OFFPG"),
	PHONE_NUMBER_BUSINESS_USAGE("BP"),
	APPLICATION_ORIGINE_CODE_BROKER("CNT"),
	APPLICATION_ORIGINE_CODE_INTACT("WINI"),
	ASSIGNABLE("ASSIGN"),
	
	WEB_ACCESS_BROKER("WEBACC_QB"),
	WEB_ACCESS_INTACT("WEBACC_QI"),
	WEB_ACCESS_BROKER_AND_INTACT("WEBACC_QIB"),
	
	CC_URL("URL"),
	CC_EMAIL("EMAIL"),
	CC_WEB_ACCESS_TYPE("CLCTASSN"),
	CC_BROKER_NAME_DISPLAY_IND("DISPNAME"),
	CC_NOTIFICATION_IND("CLCTNOTI"),
	
	CHAT_ID_VALUE("7");
	

	private final String sessionConstant;

	private SessionConstantsEnum(String aSessionConstant) {
		this.sessionConstant = aSessionConstant;
	}

	/**
	 * Gets the session constant.
	 * 
	 * @return the session constant
	 */
	public String getSessionConstant() {
		return this.sessionConstant;
	}
}
