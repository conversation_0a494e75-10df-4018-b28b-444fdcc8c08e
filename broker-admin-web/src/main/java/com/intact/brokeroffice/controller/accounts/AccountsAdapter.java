/**
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance 
 * Copyright (c) 2010, Intact Insurance, All rights reserved.<br>
 */

package com.intact.brokeroffice.controller.accounts;

import java.util.Date;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.BooleanUtils;
import org.springframework.stereotype.Component;

import com.ing.canada.cif.domain.enums.CifCompanyEnum;
import com.intact.canada.brm.domain.broker.BrokerWebOfficeAccess;
import com.intact.canada.brm.domain.profile.AccessProfile;
import com.intact.canada.brm.domain.profile.AccessProfileEnum;
import com.intact.canada.brm.domain.profile.UserAccessProfile;
import com.intact.canada.brm.domain.user.UserAccount;

/**
 * The Class is used to load a database account object into a view model and vice versa
 */
@Component
public class AccountsAdapter {


	/**
	 * Loads the database object.
	 * 
	 * @param userAccount the user account
	 * @param accountBean the account bean
	 * @param aCifCompanyEnum the a cif company enum
	 * @param accessProfileMap a map to fetch the AccessProfile persistent object ref in case we need to create and persist the link object UserAccessProfile 
	 */
	public void loadModel(UserAccount userAccount, AccountBean accountBean, CifCompanyEnum aCifCompanyEnum, Map<AccessProfileEnum, AccessProfile> accessProfileMap) {

		Object[] brokerWebOfficeAccesses = userAccount
				.getBrokerWebOfficeAccesses().toArray();

		// Remove previous accesses for current company
		for (Object brokerWebOfficeAccess : brokerWebOfficeAccesses) {
			String accessCompany = ((BrokerWebOfficeAccess)brokerWebOfficeAccess).getCompanyCode();
			String currentCompany = aCifCompanyEnum.getSubBrokerCompanyNumber();
			
			if (accessCompany != null && currentCompany != null && accessCompany.equals(currentCompany)) {
				userAccount.removeBrokerWebOfficeAccess((BrokerWebOfficeAccess)brokerWebOfficeAccess);
			}
		}
		
		// Add new selection of accesses for the user for the current company
		for (MasterBrokerBean masterBrokerBean : accountBean.getMasterBrokerBeans()) {
			if (BooleanUtils.isTrue(masterBrokerBean.getSelected())) {
				BrokerWebOfficeAccess brokerWebOfficeAccess = new BrokerWebOfficeAccess();
				brokerWebOfficeAccess.setUserAccount(userAccount);
				brokerWebOfficeAccess.setCompanyCode(aCifCompanyEnum.getSubBrokerCompanyNumber());
				brokerWebOfficeAccess.setMasterOwnerCode(masterBrokerBean.getNumber());
				brokerWebOfficeAccess.setEffectiveDate(new Date());
			}
		}
		
		for (AccessProfileBean accessProfileBean : accountBean.getAccessProfileBeans()) {			
			UserAccessProfile targetUserAccessProfile = null;
			
			for (UserAccessProfile uAccessProfile : userAccount.getUserAccessProfiles()) {
				if (accessProfileBean.getProfile() == uAccessProfile.getAccessProfile().getName()) {
					targetUserAccessProfile = uAccessProfile;
					break;
				}
			}
			if (targetUserAccessProfile == null) {
				targetUserAccessProfile = new UserAccessProfile();
				targetUserAccessProfile.setAccessProfile(accessProfileMap.get(accessProfileBean.getProfile()));
				targetUserAccessProfile.setEffectiveDate(new Date());
				userAccount.addUserAccessProfile(targetUserAccessProfile);
			}
				
			if (accessProfileBean.getSelected())
				targetUserAccessProfile.setExpiryDate(null);
			else
				targetUserAccessProfile.setExpiryDate(new Date());
		}
	}

	/**
	 * Loads the view model.
	 * 
	 * @param userAccount the user account
	 * @param accountBean the account bean
	 */
	public void loadForm(UserAccount userAccount, AccountBean accountBean) {
		
		loadUserInformation(userAccount, accountBean);
		
		for (BrokerWebOfficeAccess brokerWebOfficeAccess : userAccount.getBrokerWebOfficeAccesses()) {
			for (MasterBrokerBean masterBrokerBean : accountBean.getMasterBrokerBeans()) {
				if (masterBrokerBean.getNumber().equals(brokerWebOfficeAccess.getMasterOwnerCode())) {
					masterBrokerBean.setSelected(true);
				}
			}
		}
		
		for (AccessProfileBean accessProfileBean : accountBean.getAccessProfileBeans()) {			
			for (UserAccessProfile userAccessProfile : userAccount.getUserAccessProfiles()) {
				if (accessProfileBean.getProfile() == userAccessProfile.getAccessProfile().getName()) {
					if (userAccessProfile.getExpiryDate() == null)
						accessProfileBean.setSelected(true);
					else
						accessProfileBean.setSelected(false);
					break;
				}
			}
		}
	}
	
	/**
	 * Loads the user information
	 * @param userAccount
	 * @param accountBean
	 */
	public void loadUserInformation(UserAccount userAccount, AccountBean accountBean) {
		accountBean.setUserName(userAccount.getUid());		
		accountBean.setUserFullName(userAccount.getName());			
	}	
	
	
	/**
	 * loads a list of users
	 * @param userAccounts
	 * @param accountBeans
	 */
	public void LoadUsers(List<UserAccount> userAccounts, List<AccountBean> accountBeans){
		AccountBean anAccountBean;
		for(UserAccount aUserAccount : userAccounts){
			anAccountBean = new AccountBean();
			loadUserInformation(aUserAccount, anAccountBean);			
			accountBeans.add(anAccountBean);			
		}		
	}
	
	
}
