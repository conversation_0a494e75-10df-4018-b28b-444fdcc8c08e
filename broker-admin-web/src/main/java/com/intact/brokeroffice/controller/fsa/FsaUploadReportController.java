package com.intact.brokeroffice.controller.fsa;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import jakarta.faces.context.FacesContext;
import jakarta.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.ing.canada.cif.domain.IFsaLoad;
import com.ing.canada.cif.domain.IFsaMessage;
import com.ing.canada.cif.domain.impl.FsaParameters;
import com.ing.canada.cif.service.IFsaLoadService;
import com.intact.brokeroffice.controller.AbstractScrollerController;

@Component
@Scope("view")
public class FsaUploadReportController extends AbstractScrollerController {
	
	@Autowired
	private FsaAdapter fsaAdapter;

	@Autowired
	private IFsaLoadService fsaLoadService;

	private List<FsaMessageBean> fsaMessageBeans = new ArrayList<FsaMessageBean>();	

	private FsaMessageBean fsaMessageBean = new FsaMessageBean();	
	
	private static final String USER_SESSION_ID_LIST_MATCH_ATTRIBUTE = "SECURITY_ID_LIST_MATCH_ATTRIBUTE";

	private String selectedLoadId;
	private String uploadFileStatus;
	
	private FsaLoadBean fsaLoadBean = new FsaLoadBean();
	
	private static String FSA_UPLOAD_FAILED_STATUS 		= "FAIL";
	private static String FSA_UPLOAD_SUCCESS_STATUS 	= "SUCC";
	private static String FSA_UPLOAD_ABORT_STATUS		= "KILL";

	/**
	 * reset some parameters of controller
	 */
	//private void reset() {		
	//	this.fsaMessageBeans = null;
	//	this.selectedLoadId =  null;
	//	this.uploadFileStatus = null;		
	//}		

	/**
	 * Gets the list of FSA message bean
	 * @return
	 */
	public List<FsaMessageBean> getFsaMessageBeans() {		
		return this.fsaMessageBeans;
	}

	/**
	 * Sets the list of FSA message bean
	 * @param aFsaMessageBeans
	 */
	public void setFsaMessageBeans(List<FsaMessageBean> aFsaMessageBeans) {
		this.fsaMessageBeans = aFsaMessageBeans;
	}	
	
	/**
	 * Sets the upload file status
	 * @return
	 */
	public String getUploadFileStatus(){
		// msgReport requested with parameters
		this.uploadFileStatus = FacesContext.getCurrentInstance()
		.getExternalContext().getRequestParameterMap().get("uploadFileStatus");


		if (this.uploadFileStatus == null) {
			// Requests comming from the LIST QuoteView
			this.uploadFileStatus = (String) FacesContext.getCurrentInstance()
			.getViewRoot().getAttributes().get("uploadFileStatus");	

		} 
		else{
			FacesContext.getCurrentInstance().getViewRoot().getAttributes().put("uploadFileStatus", this.uploadFileStatus);					
		}
		viewUploadReport();
		return this.uploadFileStatus;
	}

	/**
	 * Sets the upload file status
	 * @param anUploadFileStatus
	 */
	public void setUploadFileStatus(String anUploadFileStatus) {
		this.uploadFileStatus = anUploadFileStatus;		
		FacesContext.getCurrentInstance().getViewRoot().getAttributes().put("uploadFileStatus", this.uploadFileStatus);
	}	
	
	/**
	 * Sets the selected load id
	 * @param aLoadId
	 */
	public void setSelectedLoadId(String aLoadId) {
		this.selectedLoadId = aLoadId;		
		FacesContext.getCurrentInstance().getViewRoot().getAttributes().put("selectedLoadId", this.selectedLoadId);
	}	


	/**
	 * Gets the select loadId
	 * @return
	 */
	public String getSelectedLoadId(){
		this.setScrollerPage(Integer.valueOf(1));
		// msgReport requested with parameters
		this.selectedLoadId = FacesContext.getCurrentInstance()
		.getExternalContext().getRequestParameterMap().get("selectedLoadId");

		if (this.selectedLoadId != null){
			
			FacesContext context = FacesContext.getCurrentInstance();
			HttpServletRequest request = (HttpServletRequest) context
					.getExternalContext().getRequest();
			
				this.selectedLoadId = ((HashMap<String, String>) request.getSession().getAttribute(USER_SESSION_ID_LIST_MATCH_ATTRIBUTE)).get(this.selectedLoadId);	
		}
		
		if (this.selectedLoadId == null) {
			// Requests comming from the FSA LOAD TABLE LIST
			this.selectedLoadId = (String) FacesContext.getCurrentInstance()
			.getViewRoot().getAttributes().get("selectedLoadId");
		} 
		else{
			FacesContext.getCurrentInstance().getViewRoot().getAttributes().put("selectedLoadId", this.selectedLoadId);						
		}		
		return this.selectedLoadId;
	}	

	/**
	 * VIEW UPLOAD REPORT
	 * BR5540  Launch pertinent Success or Failure report pop-up 
	 */
	private void viewUploadReport(){	

		//SUCCESSFULL UPLOAD
		if(this.getUploadSucceedInd()!=null && this.getUploadSucceedInd().booleanValue()) {
			FsaParameters fsaParameters = new FsaParameters();
			fsaParameters.setLoadId(Long.valueOf(this.selectedLoadId));

			List<IFsaMessage> fsaMsgObjects =  this.fsaLoadService.getFsaMessagesList(fsaParameters);
			if(fsaMsgObjects!=null){
				this.fsaAdapter.loadSuccesMessageBean(fsaMsgObjects.get(0),this.fsaMessageBean);				
			}
		}
		
		//KILL PROCESS		
		if(this.getUploadSucceedInd()!=null && !this.getUploadSucceedInd().booleanValue() && this.getCancelled()){					
			
			IFsaLoad fsaLoadObject =  this.fsaLoadService.findById(Long.valueOf(this.selectedLoadId));
			
			this.fsaLoadBean.setLoadId(fsaLoadObject.getLoadId());
			this.fsaLoadBean.setFileName(fsaLoadObject.getFileName());
			this.fsaLoadBean.setStartDate(fsaLoadObject.getStartDate());			
		} else {			
			FsaParameters fsaParameters = new FsaParameters();
			fsaParameters.setLoadId(Long.valueOf(this.selectedLoadId));

			this.fsaMessageBeans = new ArrayList<FsaMessageBean>();
			this.fsaAdapter.loadFsaMessageBeans(this.fsaLoadService.getFsaMessagesList(fsaParameters),this.fsaMessageBeans);
		}		
	}


	/**
	 * Gets the upload succeed indicator
	 * @return
	 */
	public Boolean getUploadSucceedInd(){		
		if(FSA_UPLOAD_SUCCESS_STATUS.equals(this.uploadFileStatus)){			
			return Boolean.TRUE;
		}
		else if(FSA_UPLOAD_FAILED_STATUS.equals(this.uploadFileStatus)){
			return Boolean.FALSE;
		}
		else{
			return Boolean.FALSE;
		}
	}	
	
	/**
	 * Checks Indicator for failed upload fsa 
	 * @return
	 */
	public boolean getFailed(){		
		if(FSA_UPLOAD_FAILED_STATUS.equals(this.uploadFileStatus)){
			return true;
		}
		return false;		
	}		
	
	/**
	 *  Checks Indicator for cancelled upload fsa
	 * @return
	 */
	public boolean getCancelled(){		
		if(FSA_UPLOAD_ABORT_STATUS.equals(this.uploadFileStatus)){
			return true;
		}
		return false;		
	}	

	/**
	 * Gets the FSA message bean
	 * @return
	 */
	public FsaMessageBean getFsaMessageBean() {
		return this.fsaMessageBean;
	}

	/**
	 * Sets the FSA Message bean
	 * @param anFsaMessageBean
	 */
	public void setFsaMessageBean(FsaMessageBean anFsaMessageBean) {
		this.fsaMessageBean = anFsaMessageBean;
	}	
	
	/**
	 * BR6170  Display total number of error 
	 * Gets the error list size
	 * @return
	 */
	public int getErrorListSize(){
		
		int size = 0;
		for(FsaMessageBean msgBean: this.getFsaMessageBeans()){
			size += msgBean.getNbRecord();
		}
		return size ;
	}

	/**
	 * Gets the fsa load bean
	 * @return
	 */
	public FsaLoadBean getFsaLoadBean() {
		return this.fsaLoadBean;
	}

	/**
	 * Sets the fsa load bean
	 * @param anFsaLoadBean
	 */
	public void setFsaLoadBean(FsaLoadBean anFsaLoadBean) {
		this.fsaLoadBean = anFsaLoadBean;
	}	
}
