/* DATEPICKER */
body .ui-button.ui-datepicker-trigger {
    background: #BCBEC0 url('../image/calendar.png') no-repeat;
    position: relative;
    top: -4px;
    border: none;
    width: 21px;
    height: 21px;
    border-radius: 5px;
    margin-left: 5px;
    cursor: pointer;
}

button.ui-datepicker-trigger span.ui-button-text {
    display: none;
}
.errorGrid .text-align{
    position: relative;
    top: -1px;
}

.ui-datepicker {
    background-color: var(--white1);
    border: thin solid var(--gray5);
}

.ui-datepicker-header {
    background-color: var(--black);
    padding: 0.6rem;
    display: flex;
    justify-content: space-evenly;
}

.ui-datepicker .ui-datepicker-prev {
    background: transparent url('../image/btnLeftArrow.png') no-repeat center center;
    padding: 0 5px;
}

.ui-datepicker .ui-datepicker-next {
    background: transparent url('../image/btnRightArrow.png') no-repeat center center;
    padding: 0 5px;
}
.ui-datepicker-calendar-container > table > thead {
    font-size: small;
}

.ui-datepicker-calendar-container > table > tbody a {
    display: flex;
    justify-content: center;
    color: var(--black);
    padding: 4px;
    font-size: small;
}

.ui-datepicker-calendar-container > table > tbody a:hover {
    text-decoration: none;
    background-color: var(--gray5);
}

.ui-datepicker-calendar-container > table > tbody .ui-state-active  {
    background-color: var(--red2);
    color: var(--white1) !important;
}

.ui-datepicker-calendar-container > table > tbody .ui-state-active:hover  {
    background-color: var(--red2);
}

.ui-datepicker-calendar-container > table > tbody .ui-datepicker-today {
    font-weight: bold;
}

.ui-datepicker-calendar-container > table > tbody .ui-datepicker-other-month a {
    color: var(--gray5);
}

.ui-datepicker-calendar-container > table > tbody .ui-datepicker-week-end a {
    color: var(--turquoise1);
}

.ui-datepicker-buttonbar > div {
    display: flex;
    justify-content: space-between;
    padding: 0.5rem;
}

.ui-datepicker-buttonbar > div .ui-today-button,
.ui-datepicker-buttonbar > div .ui-clear-button{
    background-color: transparent;
    padding: 3px 8px;
    border: 1px solid var(--black);
    border-radius: 5px;
}
