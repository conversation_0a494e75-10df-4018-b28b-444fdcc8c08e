/* Table */
.rich-table {
    font-size: 12px;
    border-top: solid 0;
    border-left: solid 0;
    empty-cells: show;
    border-collapse: collapse;
}

.refNoStyle {
    width: 100px;
}

.rich-panel-body {
    padding: 0px;
}

.rich-table-cell {
    border-right: solid 0;
    border-bottom: solid 0;
    padding: 4px 4px 4px 4px;
}

.rich-subtable-cell {
    border-right: solid 0;
    border-bottom: solid 0;
    padding: 4px 4px 4px 4px;
}

.rich-table-header {
}

.rich-table-headercell {
    border-right: solid 0;
    border-bottom: solid 0;
    padding: 0px;
    text-align: center;
}

.rich-table-thead {
    border-bottom: solid 0;

}
.rich-table-subheader {
    width: 880px;
}
.rich-table-subheadercell {
    border-right: solid 0;
    padding: 0px;
    text-align: left;
    white-space: nowrap;
    vertical-align: top;
}

.rich-table-sortable-header {
    background-position: right center;
    background-repeat: no-repeat;
    white-space: nowrap;
}

.rich-table-cursor-pointer {
    cursor: pointer;
}

.rich-inplace-edit, .rich-inplace-view {
    cursor: default;
}

.rich-sort-icon {
    vertical-align: middle;
}

.rich-table-footercell {
    border-right: solid 0;
    border-bottom: solid 0;
    padding: 4px 4px 4px 4px;
    text-align: left;
}

.rich-table-subfootercell {
    border-right: solid 0;
    border-bottom: solid 0;
    padding: 4px 4px 4px 4px;
    text-align: left;
}

.rich-subtable-headercell {
    border-right: solid 0;
    border-bottom: solid 0;
    padding: 4px 4px 4px 4px;
    text-align: center;
}

.rich-subtable-footercell {
    border-right: solid 0;
    border-bottom: solid 0;
    padding: 4px 4px 4px 4px;
    text-align: left;
}

.rich-sort-asc {
    background-image: url(../image/asc.gif);
}

.rich-sort-desc {
    background-image: url(../image/desc.gif);
}

/* Panel */

.rich-panel {
    border: 0px;
    padding-top: 0px;
    padding-right: 0px;
    padding-bottom: 0px;
    padding-left: 0px;
}

/* Tab */

.rich-tab-active {
    font-weight: bold;
    color: #666;
    border: 1px solid #1e1e1e;
    background: #fff url(../image/whiteTab.jpg) right bottom no-repeat;
    cursor: default;
}

.rich-tab-active a {
    color: #fff;
}

.rich-tab-inactive {
    color: #fff;
    background-color: #E3DED5;
    border: 1px solid #BCBEC0;
    background: url(../image/grayTab.jpg) right bottom no-repeat #FFF;
    cursor: pointer;
}

.rich-tab-header {
    padding: 5px 40px;
    text-align: center;
    font-size: 11px;
    font-family: Arial, Verdana, sans-serif;
}

.rich-tabhdr-side-border {
    background-image: none;
}

.rich-tabhdr-side-cell {
    padding: 0px 10px 0px 0px;
    border: 0px;
}

.rich-tabpanel {
    margin-top: 20px;
    width: 100%;
    margin-left: 10px;
}

.rich-tabpanel-content {
    border-width: 1px;
    border-style: solid;
    border-color: #c4c0b9;
    vertical-align: top;
    background-color: #FFF;
    color: #000;
    font-size: 11px;
    font-family: Arial, Verdana, sans-serif;
}

.rich-tabpanel-content-position {
    height: 100%;
    position: relative;
    width: 946px;
}

.rich-tab-bottom-line {
    border: 0px;
    padding-bottom: 10px
}


/* DataScroller */

.rich-dtascroller-table {
    border-width: 0px;
    border-collapse: separate;
    border-spacing: 5px;
}

.rich-datascr-button, .rich-datascr-ctrls-separator {
    border: 1px solid #C4C0B9;
    cursor: pointer;
    text-align: center;
    width: auto;
    color: #00b3be;
    padding: 3px;
}

.rich-datascr-button-dsbld {
    cursor: default;
}

.rich-datascr-act {
    border: 1px solid #655F5D;
    text-align: center;
    width: 25px;
    color: #655F5D;
}

.rich-datascr-inact {
    border: 1px solid #655F5D;
    cursor: pointer;
    text-align: center;
    padding: 3px;
    width: 25px;
    color: #00b3be;
}

.rich-datascr-inact:hover {
    text-decoration: underline;
    color: #655F5D;
    cursor: pointer;
}

.rich-datascr-button-dsbld {
    color: gray;
}

/* Modal panel */

.rich-modalpanel {
    left: 0;
    top: 0;
}

.rich-mpnl-header {
    padding: 2px;
    border-width: 1px;
    border-style: solid;
}

.rich-mpnl-header-cell {
    height: 20px;
    width: 100%;
    vertical-align: middle;
    z-index: 5;
}

.rich-mpnl-spacer, .rich-mpnl-resizer {

}

.rich-mpnl-ovf-hd {
    overflow: hidden;
}

.rich-mpnl-trim {
    position: relative;
    z-index: 0;
}

.rich-mpnl-iframe {
    position: absolute;
    left: 0;
    top: 0;
    background-color: white;
    overflow-y: hidden;
    z-index: -1;
}

.rich-mpnl-mask-div {
    position: fixed;
    top: 0;
    left: 0;
    border: 0;
    margin: 0;
    padding: 0;
    width: 100%;
    height: 100%;
    border-style: none;
    /*background-color:#E6E7E8;*/
}

.rich-mpnl-mask-div-opaque {
    /*filter:alpha(opacity=50);*/
    /*opacity:.5;*/
}

.rich-mpnl-mask-div-transparent {
    background-color: transparent;
}

.rich-mpnl-panel {
    position: fixed;
    margin: 0;
    padding: 0;
    background-color: inherit;
    z-index: 9;
    left: 0;
    top: 0;
}

.rich-mpnl-resizer {
    line-height: 1px;
    font-size: 1px;
    position: absolute;
}

.rich-mpnl-button {
    outline-style: none;
    position: absolute;
    clip: rect(0px 0 1px 1px);
    height: 10px;
    width: 10px;
    left: 0;
    top: 0;
    z-index: -300;
}

.rich-mpnl-controls {
    position: absolute;
    top: 3px;
    right: 3px;
    z-index: 1;
}

.rich-mpnl-content {
    border-width: 1px;
    border-style: solid;
    padding: 10px;
    background-color: #ffffff;
    font-size: 12px;
}

.rich-mpnl-body {
    padding: 0px;
    background-color: #ffffff;
    background-image: url(../image/gradient.png);
    background-repeat: repeat-x;
    background-attachment: scroll;
    background-position: center bottom;
}

.rich-mpnl-shadow {
    position: absolute;
    height: 100%;
    width: 100%;
    z-index: -1;
    top: 4px;
    left: 4px;
    filter: alpha(opacity=10);
    opacity: .1;
}


/* Calendar */

.rich-calendar-header {
    border-bottom-color: #BCBEC0;
    background-color: #000000;
    color: #ffffff;
    filter: alpha(opacity=100);
    font-size: 14px;
    font-family: Arial, Verdana, sans-serif;
}

.rich-calendar-cell {
    border-bottom-color: #BCBEC0;
    border-right-color: #BCBEC0;
    background-color: #ffffff;
    font-size: 11px;
    font-family: Arial, Verdana, sans-serif;
}

.rich-calendar-month {
    background-color: #000000;
    font-size: 11px;
    font-family: Arial, Verdana, sans-serif;
    font-weight: bold;
    color: #ffffff;
}

.rich-calendar-days {
    border-bottom-color: #BCBEC0;
    border-right-color: #BCBEC0;
    font-size: 12px;
    color: #000000;
    font-family: Arial, Verdana, sans-serif;
}

.rich-calendar-week {
    border-bottom-color: #ffffff;
    border-right-color: #BCBEC0;
    background-color: #E6E7E8;
    font-family: Arial, Verdana, sans-serif;
    font-size: 12px;
}

.rich-calendar-holly {
    background-color: #ffffff;
    color: #00b3be;
}

.rich-calendar-today {
    background-color: #FFFFFF;
    color: #000000;
}

.rich-calendar-select {
    background-color: #C60C30;
    color: #ffffff;
}

.rich-calendar-hover {
    background-color: #C4C0B9;
    color: #ffffff;
}

.rich-calendar-weekends {
    background-color: #ffffff;
    color: #000000;
}

.rich-calendar-footer {
    border-bottom-color: #C4C0B9;
    background-color: #ffffff;
    font-size: 14px;
    filter: alpha(opacity=100);
    font-family: Arial, Verdana, sans-serif;
}

.rich-calendar-popup {
    background-color: #ffffff;
}

.rich-calendar-boundary-dates {

}

.rich-calendar-date-layout {
    border-color: #BCBEC0;
    background-color: #ffffff;
    filter: alpha(opacity=100);
    font-size: 11px;
    font-family: Arial, Verdana, sans-serif;
}

.rich-calendar-date-layout-split {
    border-right-color: #BCBEC0;
}

.rich-calendar-date-layout-cancel {
    background-color: #ffffff;
    border-top-color: #BCBEC0;
}

.rich-calendar-date-layout-ok {
    background-color: #ffffff;
    border-top-color: #BCBEC0;
}

/* Simple Toggle Panel  */

.rich-stglpanel {
    border: none;
    padding: 0;
    background: none;
}

.rich-stglpnl-marker {
    padding: 0px;
}

.rich-stglpanel-header {
    border: solid 1px #CCCCCC;
    cursor: pointer;
    padding: 5px 5px 0px 10px;
    background-color: #EAEAEC;
    height: 25px;
    vertical-align: middle;
}

.rich-stglpanel-body {
    padding: 0px;
    text-align: left;
    width: 100%;
    overflow: inherit;
}

.rich-stglpanel-marker {
    float: right;
}


.rich-calendar-tool-close {

}

/*
.rich-stglpanel{border-width:1px;border-style:solid;padding:1px;background-position:top right;background-repeat:no-repeat;}
.rich-stglpnl-marker{padding-right:3px;}
.rich-stglpanel-header{border-width:1px;border-style:solid;cursor:pointer;padding:2px;background-position:top left;background-repeat:repeat-x;}
.rich-stglpanel-body{padding:10px;text-align:left;vertical-align:top;overflow:auto;}
.rich-stglpanel-marker{float:right;}
.rich-stglpanel-header{background-image:url(/BrokerOffice/a4j/g/3_3_3.Finalorg.richfaces.renderkit.html.GradientA.jsf);}
*/
