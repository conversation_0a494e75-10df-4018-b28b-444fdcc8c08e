/* VARIABLES */
:root {
    --black: #000000;
    --white1: #ffffff;
    --white2: #f1f1f1;
    --transparentWhite: rgb(216 216 216 / 50%);
    --turquoise1: #00b3be;
    --turquoise2: #27688f;
    --turquoise3: #b2dee7;
    --turquoise4: #d9f4f5;
    --turquoise5: #007b87;
    --turquoise6: #00626b;
    --brown1: #655f5d;
    --brown2: #c4c0b9;
    --gray1: #cccccc;
    --gray2: #404040;
    --gray3: #58585a;
    --gray4: #1e1e1e;
    --gray5: #BCBEC0;
    --gray6: #EEEEEF;
    --red1: #c60c30;
    --red2: #c00c30;
    --red3: #ae275f;
    --red4: #ae0b2a;
    --orange: #ff8c00;
    --yellow: #FFFEEF;
}

/* TAGS */
table.formSearch > tbody > tr > td {
    border-bottom: thin solid var(--gray5);
    padding: 10px 15px;
}

/* CLASSES */

/* IDS */

/* MEDIA QUERIES */

/* header & layout */

html, body {
    margin: 0;
    padding: 0;
    height: 100%;
    width: 100%;
}

img.breadcrumb{
    width: 100%;
}

body {
    background-image: url(../image/bgPage.jpg);
    background-repeat: repeat;
    background-attachment: fixed;
    background-position: center 0pt;
    font-size: 12px;
    font-family: Helvetica,Arial,Verdana,sans-serif;
}

.main {
    position: relative;
    padding: 0 8px 30px 8px;
    margin: auto;
    box-sizing: border-box;
    width: 1004px;
    background-image: url(../image/wrapBg.png);
    background-repeat: repeat-y;
    background-position: center 0pt;
    min-height: 100%;
}

.header {
    background-image: url(../image/bgHeader.jpg);
    background-repeat: no-repeat;
    background-position: right 0pt;
    position: relative;
    padding-bottom: 20px;
    margin-bottom: 10px;
}

.contents {
    padding: 0 10px;
    box-sizing: border-box;
    width: 100%;
    position: relative;
}

table{
    font-size: 12px;
    border-spacing: 0;
}

.province {
    position: absolute;
    right: 15px;
    bottom: 30px;
}

.input_province{
    display: inline-block;
}

.input_province .ui-selectonemenu-label{
    display: none;
}

.search-panel-tab {
    border: solid 1px var(--gray5);
    margin-bottom: 20px;
    padding-top: 25px;
    border-bottom: none;
}

.search-content-tab {
    box-sizing: border-box;
    margin: 20px auto 20px auto;
    width: 916px;
}

a {
    text-decoration: none;
    color: var(--turquoise1);
}

a:hover {
    text-decoration: underline;
    cursor: pointer;
    color: var(--brown1);
}

.top-padding15{
    padding-top: 15px !important;
}

.left-margin10{
    margin-left: 10px !important;
}

.left-margin15{
    margin-left: 15px !important;
}

.margin-vertical15{
    margin: 15px 0;
}

a.margin-vertical15{
    display: block;
}

.padding-vertical15{
    padding: 15px 0;
}

.center-display{
    text-align: center;
}

.displayBlock{
    display: block;
}

.section-hide{
    display: none;
}

.inline-heading{
    display: inline-block;
    margin: 15px 0;
}

.errorMessage {
    color: red;
}

.errorMessage br{
    display: none;
}

/* tabs */

.ui-tabs-panel {
    overflow: visible;
    background-image: url(../image/contentMid966.png);
    background-repeat: repeat-y;
}

.info-content-in-tab {
    box-sizing: border-box;
    padding: 0 20px;
    overflow: visible;
}

ul.ui-tabs-nav {
    list-style: none;
    padding: 0;
    display: flex;
    margin-bottom: 10px;
}

.ui-tabs-nav li{
    margin-right: 15px;
}

.ui-tabs-nav a {
    border: 1px solid var(--red1);
    background: var(--red1);
    color: var(--white1);
    display: inline-block;
    padding: 8px 40px;
    text-decoration: none;
    font-size: 11px;
    font-weight: bold;
}

.ui-tabs-nav li.ui-state-active a{
    color: var(--red1);
    background: var(--white1);
}

.ui-tabs-nav li.ui-state-active a:hover{
    cursor: default;
}

.ui-tabs-nav a:hover{
    background: var(--red4);
}

.ui-tabs-panels .ui-tabs-nav a{
    border: 1px solid var(--gray5);
    background: var(--gray5);
    color: var(--white1);
}

.ui-tabs-panels .ui-tabs-nav a:hover{
    background: var(--white1);
    color: var(--turquoise1);
}

.ui-tabs-panels .ui-tabs-nav li.ui-state-active a{
    border: 1px solid var(--gray4);
    background: var(--white1);
    color: var(--gray4);
}

.ui-tabs-panels .ui-tabs-nav li.ui-state-active a:hover{
    cursor: default;
}

.info-content-in-tab {
    margin-top: 30px;
    position: relative;
}

.leftfullpart {
    background-image: url(../image/ligneGauche.png);
    height: 8px;
    display: block;
    margin: 15px 0;
}

.rightpart {
    position: absolute;
    top: 0px;
    right: 10px;
}

.title {
    font-size: 16px;
    font-weight: bold;
}

.boldFont{
    font-weight: bold;
}

.info {
    font-size: 11px;
    font-weight: bold;
    margin-top: 5px;
}

.perPage {
    display: flex;
    justify-content: end;
    margin: 15px;
}

.perPage select{
    margin: 0 8px;
}

/* table */

.ui-datatable-tablewrapper thead tr th:has(.ui-icon) {
    padding-right: 12px;
}

.ui-datatable-tablewrapper tbody.ui-widget-content td,
.ui-datatable-tablewrapper th{
    padding: 5px 15px;
}

.ui-datatable-tablewrapper th:first-child{
    padding-left: 0;
}

.ui-datatable-tablewrapper th{
    text-align: left;
    vertical-align: middle;
}

.ui-datatable-tablewrapper th .ui-column-title td,
.ui-datatable-tablewrapper th .ui-column-title{
    font-weight: bold;
    white-space: nowrap;
}

.ui-datatable-tablewrapper tbody.ui-widget-content tr:nth-child(even) {
    background: var(--white2);
}

.ui-helper-hidden{
    display: none;
}

.ui-datatable-tablewrapper table {
    border-collapse: collapse;
    font-size: 12px;
    width: 100%;
}

.numCol input{
    width: 100px;
}

.nameCol input{
    width: 220px;
}

.locationCol input{
    width: 120px;
}

.numCol, .nameCol{
    font-weight: bold;
}

td.actionCol button{
    background: none;
    border: none;
    padding: 0;
    color: var(--turquoise1);
}

td.actionCol button:hover{
    text-decoration: underline;
    cursor: pointer;
    color: var(--brown1);
}

th.ui-sortable-column {
    font-weight: normal;
}

th.ui-sortable-column .ui-column-title img{
    cursor: pointer;
}

.prime-table-cell, [id*="subQuoteList_row_"] > td {
    padding: 4px;
}

.deselectAllBtn-, .selectAllBtn- {
    border: 0;
    cursor: pointer;
    width: 18px;
    height: 18px;
}

.deselectAllBtn- span, .selectAllBtn- span {
    display: none;
}

.deselectAllBtn- {
    background: url("../image/iconDeselectAll.png") no-repeat;
}

.selectAllBtn- {
    background: url("../image/iconSelectAll.png") no-repeat;
}

.ui-expanded-row-content {
    display: none;
}

/* loading spinner */

#contentWaiter {
    font-size: 0;
}

#contentWaiter > div > [id*="_start"] {
    background-color: var(--transparentWhite);
    position: fixed;
    bottom: 0;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1;
}

#contentWaiter > div > [id*="_start"]::after {
    background: var(--white1) url("../image/waiting.gif") no-repeat;
    background-position: center;
    background-size: 50px 50px, auto;
    border: 1px solid var(--black);
    display: block;
    content: '';
    left: 50%;
    top: 50%;
    margin-left: -40px;
    margin-top: -40px;
    position: absolute;
    height: 80px;
    width: 80px;
}

/* Primefaces OverlayPanel component specific CSS */
/* See https://primefaces.github.io/primefaces/13_0_0/#/components/overlaypanel */

.ui-overlay-visible {
    visibility: visible
}

ui-overlay-visible * {
    visibility: visible !important
}

.ui-overlay-hidden {
    visibility: hidden
}

.ui-overlay-hidden * {
    visibility: hidden !important
}

.ui-overflow-hidden {
    overflow: hidden
}

.ui-connected-overlay-enter {
    opacity: 0;
    transform: scaleY(0.8)
}

.ui-connected-overlay-enter-active {
    opacity: 1;
    transform: scaleY(1);
    transition: transform .12s cubic-bezier(0, 0, 0.2, 1), opacity .12s cubic-bezier(0, 0, 0.2, 1)
}

.ui-connected-overlay-enter-done {
    transform: none
}

.ui-connected-overlay-exit {
    opacity: 1
}

.ui-connected-overlay-exit-active {
    opacity: 0;
    transition: opacity .1s linear
}

.ui-dialog.ui-overlay-hidden {
    display: block
}

.ui-overlaypanel {
    margin: 0;
    position: absolute;
    display: none
}

.ui-overlaypanel-close {
    position: absolute;
    top: -10px;
    right: -10px;
    -moz-border-radius: 10px;
    -webkit-border-radius: 10px;
    border-radius: 10px
}

.ui-overlaypanel-mask {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    user-select: none
}

/*ACTION BUTTONS: Reassign quote, search */
.actionBtn span img{
    position: relative;
    top: 1px;
}
.actionBtn span.right img{
    margin-left: 5px;
}

.actionBtn span.left img {
    margin-right: 5px;
}

.actionBtn:hover {
    background:url(../image/btnRightLongOver.png) right no-repeat;
    text-decoration: none;
    color: var(--white1);
}

.actionBtn:focus-visible{
    outline: none;
}

.actionBtn {
    border-radius: 5px;
    color: #FFF;
    cursor:pointer;
    font-weight: bold;
    font-size: 1.2em;
    background : url(../image/btnRightLong.png) no-repeat right;
    padding:3px 15px;
    text-decoration: none;
    border: none;
}

.actionBtn.btnArrow{
    padding-right: 30px;
    position: relative;
}

.actionBtn.btnArrow::after{
    content: '';
    background: url("../image/btnRightArrow.png");
    display: block;
    width: 13px;
    height: 13px;
    position: absolute;
    right: 12px;
    top: 4px;
}

.flex-end {
    display: flex;
    justify-content: end;
}

.link_reassignQuote {
    margin-bottom: 15px;
    display: inline-block;
}

/* PAGINATION*/
.ui-paginator-bottom {
    padding: 20px 0;
    display:flex;
    justify-content: end;
}

.ui-paginator-pages {
    margin-top: 4px;
}

.ui-paginator-prev,
.ui-paginator-next {
    border: 1px solid var(--brown2);
    padding: 3px 9px;
    margin: 0 2px;
}

.ui-paginator-page {
    border: 1px solid var(--brown1);
    padding: 3px 12px;
    margin: 0 2px;
}

.ui-paginator .ui-state-active {
    color: var(--brown1);
    padding: 3px 9px;
}

.ui-paginator .ui-paginator-prev:before {
    content: "<";
}

.ui-paginator .ui-paginator-next:before {
    content: ">";
}

.ui-paginator .ui-icon-seek-prev,
.ui-paginator .ui-icon-seek-next {
    display: none;
}

.ui-paginator .ui-state-disabled {
    color: var(--brown2);
}

.ui-paginator .ui-state-disabled:hover {
    text-decoration: none;
    cursor: auto;
}

.notification {
    display: block;
    margin: 15px 0;
    color: var(--turquoise1);
}

/*broker admin */
.content-block{
    margin: 30px 15px;
}

.contentsSpoe{
    text-align: center;
}
.contentsSpoe .session-expire{
    display: block;
    font-size: 16px;
    margin: 50px 0 15px 0;
}

.content-block .ui-tabs-panels{
    padding: 15px 10px;
    border: 1px solid var(--brown2);
}
.content-block .ui-tabs-panels .ui-tabs-panel{
    background: none;
}
.content-block .ui-tabs-panels .ui-tabs-panel{
    background-image: none;
}

#spoeForm{
    margin: 30px 15px;
}

.sub-title{
    font-size: 14px;
    font-weight: bold;
    margin: 30px 0 5px 0;
    display: block;
}

.icon-arrow::after{
    font-size: 12px;
    font-weight: normal;
    content: '>';
    margin-left: 8px;
    position: relative;
    top: -1px;
}

.input-padding{
    padding: 4px 2px;
}

#spoeForm .select-list .ui-datagrid-column{
    margin: 5px 0;
}

.ui-datagrid-column input{
    position: relative;
    top: 2px;
    margin-right: 5px;
}

.submit-btn{
    padding: 8px 25px;
    font-size: 14px;
    margin-top: 15px;
    color: var(--white1);
    background: var(--red1);
    border: 1px solid var(--red1);
    border-radius: 6px;
}

.masterBroker-owner-links a{
    display: block;
    padding: 2px 0;
}

.info-accessdenied-in-tab{
    padding: 15px 35px;
}

.title-description{
    color: var(--gray2);
}

/* modify form */

.modify-form{
    margin: 30px 0;
}

.modify-form table{
    width: 100%;
}

.formCol-title{
    font-weight: bold;
    color: var(--gray4);
}

.sub-block-line{
    border-top: 2px solid var(--white1);
}

.user-modify{
    background: url(../image/gradient.png) repeat-x center bottom;
}

.user-modify > .ui-panel-content > .ui-panelgrid > .ui-panelgrid-content > .ui-g {
    display: grid;
    grid-template-columns: 2fr 1fr 8fr;
    border-bottom: 2px solid var(--white2);
    margin-bottom: 24px;
    padding-bottom: 8px;
}

.errorGrid tr td{
    border-top: none !important;
    padding: 0 !important;
}

.access-list .ui-datagrid-column{
    margin: 5px 0;
}

.btn-group{
    margin-top: 30px;
}

.btn-group .ui-panel-content,
.center-flex{
    display: flex;
    justify-content: space-evenly;
}

.updateBtn-group .buttonUpdate{
    margin-left: 15px;
}

a.link_returnTopPointSaleList{
    display: inline-block;
    margin: 15px 0;
}

table.subbroker-form,
table.subbroker-form table {
    width: 100%;
}

.modify-table-main{
    margin: 30px 0;
}

.modify-sale-block{
    padding: 0 15px;
    background: var(--transparentWhite);
}

.modify-sale-block:last-child{
    padding-bottom: 10px;
}

.modify-sale-block.add-table{
    padding-bottom: 0;
}

.modify-sale-block.add-table td{
    padding: 25px 0;
}

.modify-sale-block.add-table tr:nth-child(2) td{
    border-top: 2px solid var(--white1);
}

.formRow-title{
    font-weight: bold;
    margin: 15px 0 5px 0;
    display: block;
}

.line-identity{
    background: var(--transparentWhite);
    padding: 15px 15px 0;
}

.td1{
    width: 200px;
}

.modify-sale-block td,
.line-identity td{
    padding: 5px 0;
}

.modify-sale-block td:has(.formRow-title){
    padding: 0;
}

.modify-sale-block td:empty,
.line-identity td:empty{
    padding: 0;
}

.group-blocks{
    display: flex;
    flex-direction: column;
    margin-top: 10px
}

.group-blocks .phone-detail{
    margin: 5px 0;
}

.phone-detail .org-name{
    width: 20%;
    display: inline-block;
}

.phone-detail .org-name::after{
    content: ':';
    font-size: 12px;
    margin: 0 5px;
}

.phone-detail .input_brokerSource{
    margin-left: 15px;
}

.service-input-block{
    margin: 5px 0;
    display: block;
}

.sub-block-line td:has(.service-block):first-child{
    padding-right: 40px;
    width: 35%;
}

.word-wrap{
    white-space: nowrap;
    margin-right: 10px;
}

.subbroker-form table.radioBtn-table{
    width: 48%;
}

input[type='radio'] + label{
    position: relative;
    top: -2px;
}

.followupTable td{
    padding-right: 15px;
}

.point-modify-group{
    margin: 10px;
}

.point-modify-group .nowrap{
    display: flex;
}

.point-modify-group .nowrap input{
    align-self: center;
}

.point-modify-group td:has(.errorMessage){
    padding: 0;
}

.errorMessage ul{
    margin: 0;
    padding: 0;
    list-style: none;
}

.errorMessage{
    display: block;
    padding-top: 4px;
}

.errorMessage:empty{
    padding: 0;
}

.border-position{
    position: relative;
    top: 5px;
}

.fsa-action .ui-panel-content{
    margin: 15px 0;
}

.fsa-action .ui-panel-content a:nth-child(2){
    margin-left: 25px;
}

.fsa-graph{
    position: relative;
}
.fsa-graph .leftfullpart{
    width: calc(100% - 60px);
}

.fsaPanelContent th.ui-sortable-column{
    padding-bottom: 15px;
}

.upload-status-title{
    display: block;
    margin-bottom: 15px;
}
.fail{
    color: var(--red1);
}

.success{
    color: var(--turquoise6);
}

.upload-status-wrap{
    padding: 0 15px;
}

.upload-status-detail tr,
.upload-status-wrap .upload-details-tableList .ui-widget-content{
    display: flex;
    flex-direction: column;
    gap: 10px
}

.upload-status-detail td:nth-child(2):not(:has(.errorSize)){
    display: none;
}

.upload-status-detail td:empty{
    display: none;
}

.upload-error-intro{
    font-size: 14px;
    font-weight: bold;
}

.upload-cancle-table{
    display: block;
}

.upload-cancle-table tr td{
    padding: 2px 0 2px 15px;
}

.report-content-tab{
    padding: 5px 15px;
}

.changePeriod-wrap .ui-g .ui-panelgrid-cell {
    padding: 0 6px;
    display: table-cell;
}

.changePeriod-wrap tr td:first-child{
    width: 25%;
    font-weight: bold;
}

.changePeriod-wrap .ui-calendar input{
    padding: 3px 2px;
}

.changePeriod-wrap .ui-calendar .ui-datepicker-trigger{
    top: 5px;
}

/* performance indicator */
.indicator-selectors{
    margin-bottom: 30px;
}

.expand-collapse .ui-panel-content{
    display: flex;
    justify-content: flex-end;
}

.expand-collapse a{
    margin: 5px 10px;
}

.performance-table th{
    text-align: center;
    vertical-align: middle;
}

.performance-table th,
.performance-table tbody.ui-widget-content td{
  padding: 5px 3px;
}

.performance-table th .ui-column-title{
    display: block;
}

.performance-table th .ui-column-title{
    white-space: normal;
}

.performance-table th:first-child{
    padding-left: 3px;
}

.performance-table tbody  tr.ui-widget-content td:first-child,
.performance-table tfoot  td:first-child,
.performance-table tbody  tr.ui-datatable-summaryrow td:first-child{
    border-left: 1px solid var(--turquoise1);
}

.performance-table tbody  tr.ui-widget-content td:last-child,
.performance-table tfoot  td:last-child,
.performance-table tbody  tr.ui-datatable-summaryrow td:last-child{
    border-right: 1px solid var(--turquoise1);
}

.performance-table tfoot  td{
    padding: 10px 3px;
    border-bottom: 1px solid var(--turquoise1);
    border-top: 1px solid var(--turquoise1);
    background: var(--yellow);
}

.performance-table tbody.ui-widget-content tr:nth-child(even){
    background: var(--white1);
}

.performance-table tbody  tr.ui-datatable-summaryrow{}

.performance-table tbody tr.ui-datatable-empty-message td{
    font-weight: bold;
    font-size: 14px;
}

.search-refDate{
    margin-top: 15px;
}

.performance-table tbody tr.ui-datatable-empty-message td:first-child,
.performance-table tbody tr.ui-datatable-empty-message td:last-child {
    border: 0;
}

/* error page */
.techErrorGrid{
    margin: 30px;
    line-height: 20px;
}

.errorTitle{
    font-size: 16px;
    font-weight: bold;
    margin: 30px 0 8px 0;
    display: block;
}
.techErrorTime{
    font-size: 14px;
}

.footer{
    display: flex;
    justify-content: center;
    padding: 30px 0;
}

.green-bgrnd-top-border-cell{
    background-color: #00b3be;
    color: var(--white1);
    border-width: 1px;
    border-color: #00b3be;
    border-style: solid;
    line-height: 20px;
}

.alignRight{
    text-align:right;
    line-height: 30px;
}

.alignCenter{
    text-align:center;
}

.white-bgrnd-rb-border-cell{
    border-width: 1px;
    border-color: #00b3be;
    border-style: solid;
    background-color: #FFFFFF;
    color: #000000;
}

.alignLeft{
    text-align:left;
}

.white-bgrnd-border-cell{
    border-width: 1px;
    border-color: #00b3be;
    border-style: solid;
    background-color: #FFFFFF;
    color: #000000;
}

.grey-bgrnd-cell{
    border-width: 1px;
    border-style: solid;
    border-color: #00b3be;
    background: var(--transparentWhite) ;
    color: #00b3be;
}

.white-bgrnd-lr-border-cell{
    border-width: 1px;
    border-color: #00b3be;
    border-style: solid;
    background-color: #FFFFFF;
    color: #000000;
}

.white-bgrnd-b-border-cell{
    border-width: 1px;
    border-color: #00b3be;
    border-style: solid;
    background-color: #FFFFFF;
    color: #000000;
}

.preformatted-text {
    white-space: pre-wrap;
}

/* ui fix for jdk17 */
.modify-sale-block, .modify-sale-block .ui-widget,
.modify-sale-block.hourTable > .ui-widget-content{
    display: table;
    width: 100%;
    box-sizing: border-box;
}

.modify-sale-block button.ui-widget{
    width: auto;
}

.modify-sale-block .ui-widget-content,
.modify-sale-block.hourTable > .ui-widget-content > .ui-g{
    display: table-row-group;
}

.modify-sale-block .point-modify-group{
    padding: 10px;
    margin: 0;
}
/* new */

.modify-sale-block .ui-g,
.line-identity .ui-g,
.modify-sale-block.hourTable > .ui-widget-content > .ui-g > .ui-panelgrid-cell{
    display: table-row;
}

.modify-sale-block .ui-g .ui-panelgrid-cell,
.line-identity .ui-g .ui-panelgrid-cell,
.modify-sale-block.hourTable > .ui-widget-content > .ui-g > .ui-panelgrid-cell > .ui-panelgrid{
    display: table-cell;
    vertical-align: middle;
}

.modify-sale-block.hourTable > .ui-widget-content > .ui-g > .ui-panelgrid-cell > .ui-panelgrid{
    width: auto;
}

.modify-sale-block.add-table .ui-g:nth-child(2) .ui-panelgrid-cell{
    border-top: 2px solid var(--white1);
}

.modify-sale-block.add-table .ui-panelgrid-cell{
    padding: 25px 0;
}


.modify-sale-block .ui-panelgrid-cell,
.line-identity .ui-panelgrid-cell{
    padding: 5px 0;
}

.modify-sale-block .ui-panelgrid-cell:has(.formRow-title){
    padding: 0;
}

.modify-sale-block .ui-panelgrid-cell:empty,
.line-identity .ui-panelgrid-cell:empty{
    padding: 0;
}

.modify-sale-block .point-modify-group .ui-panelgrid-cell:has(.errorMessage){
    padding: 0;
}

.modify-sale-block.hourTable{
    display: block;
}

.modify-sale-block .hourTable .ui-g .ui-panelgrid-cell .ui-panelgrid{
    display: table-cell;
    padding: 5px 0;
}

.changePeriod-wrap .ui-g{
    display: table-row;
}

.changePeriod-wrap .ui-g .ui-panelgrid-cell:first-child{
    width: 25%;
    font-weight: bold;
}

.changePeriod-wrap .ui-calendar input{
    padding: 3px 2px;
}

.changePeriod-wrap .ui-calendar .ui-datepicker-trigger{
    top: 5px;
}

.modify-sale-block input[type='checkbox']{
    position: relative;
    top: 2px;
}

.line-identity > .ui-widget-content{
    display: table;
    width: 100%;
}

table.input_webAccessType{
    width: 100%;
}

.sub-block-line div:has(.service-block):first-child{
    padding-right: 40px;
    width: 35%;
}

.sub-block-line .service-block table{
    width: 100%;
}
