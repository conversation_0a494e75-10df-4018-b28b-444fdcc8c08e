function collapsePanels() {	
	
	var sections = document.querySelectorAll('.section');
	
	for (var i=0; i < sections.length; i++) {
	  	if (SimpleTogglePanelManager.panels.get(sections[i].id) != null && SimpleTogglePanelManager.panels.get(sections[i].id).status == "true"){
	    	SimpleTogglePanelManager.toggleOnClient('onclick', sections[i].id);
	  	}
	}
	document.getElementById("viewQuoteForm:collapseAllaqua").style.display="none";document.getElementById("viewQuoteForm:expandAllaqua").style.display="";
}


function expandPanels() {	
 
	var sections = document.querySelectorAll('.section');
	
    for (var i=0; i < sections.length; i++) {
      if (SimpleTogglePanelManager.panels.get(sections[i].id) != null && SimpleTogglePanelManager.panels.get(sections[i].id).status == "false"){
         SimpleTogglePanelManager.toggleOnClient('onclick', sections[i].id);
      }
    }
    
    document.getElementById("viewQuoteForm:collapseAllaqua").style.display="";
    document.getElementById("viewQuoteForm:expandAllaqua").style.display="none";
}

// RichFaces datatable select all -checkbox
function checkAllCheckboxesInTable( inputId, state ){
  var tableId = inputId.substr(0, inputId.lastIndexOf(':'));
  var tableElement = document.getElementById( tableId );
  var inputs = tableElement.getElementsByTagName('input');
  for (var i = 0; i <= inputs.length; i++){
    var input = inputs[i];
    if (input != undefined) {
      if( input.getAttribute('type') == 'checkbox' && state){
        input.setAttribute('checked', state);
      } else{
        input.setAttribute('checked', false);
        input.removeAttribute('checked');
      }
    }
  }
} 
  
function isNumberKey(evt){    	
  var charCode = (evt.which) ? evt.which : evt.keyCode;
  if (charCode > 31 && (charCode < 48 || charCode > 57)){
    return false;
  }
  return true;}

function closeAndRefresh(){		
	var obj_window = window.open('', '_self');
	obj_window.opener = window;
	obj_window.focus();
	opener=self;
	self.close();	
}

function logout(sentToUrl){	
  	document.logoutForm.action = sentToUrl;	document.logoutForm.method="POST";document.logoutForm.submit();closeAndRefresh();
}

var gizmo;

function stopClock(){
	clearTimeout(gizmo);
}

function yourClock(){	
	var monthname=new Array("Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec");
	var nd = new Date();
	var dd, MMM, yyyy, h, m, s;
	var dateTime=" ";
	dd = nd.getDate();MMM = monthname[nd.getMonth()];yyyy = nd.getFullYear();h = nd.getHours();m = nd.getMinutes();s = nd.getSeconds();
	if (s <=9) s="0" + s;
	if (m <=9) m="0" + m;
	if (h <=9) h="0" + h;
	dateTime+=dd + "-" + MMM + "-" + yyyy + " " +  h + ":" + m + ":" + s;document.getElementById("the_clock").innerHTML=dateTime;	
}
