
function triggerSort(tableWidgetVar, headerNum){
    var tableWidget = PF(tableWidgetVar);
    if (tableWidget instanceof PrimeFaces.widget.DataTable) {
        var sortableColumn = tableWidget.sortableColumns[headerNum];
        if (sortableColumn) {
            sortableColumn.click();
        }
    }
}

function setDatatablePageNumber(widgetNames) {
    widgetNames.forEach( widgetName => {
        let widget = PF(widgetName)
        if (widget) {
            widget.getPaginator()?.setPage(0);
        }
    })
}


function startSort(widgetName, callback) {
    let pageNumber = PF(widgetName).getPaginator()?.getCurrentPage();
    if (pageNumber || pageNumber == 0) {
        callback(pageNumber);
    }
}

function completeSort(widgetName, pageNumber) {
    PF(widgetName).getPaginator()?.setPage(pageNumber, true);
}
