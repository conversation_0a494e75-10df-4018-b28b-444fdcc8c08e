<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">

<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui">
    <f:view locale="#{languageController.locale}"/>

    <f:loadBundle var="global" basename="com.intact.brokeroffice.resources.global"/>
    <f:loadBundle var="legend" basename="com.intact.brokeroffice.controller.legend.legend"/>

    <head>
        <!-- PM10280, Add compatibilty tag to insure BrokerAdmin works in IE9 and up -->
        <meta http-equiv="X-UA-Compatible" content="IE=8"/>

        <link rel="stylesheet" type="text/css" href="style/richComponent.css" media="all"/>
        <link rel="stylesheet" type="text/css" href="style/primefacesComponent.css" media="all"/>
        <link rel="stylesheet" type="text/css" href="style/main.css" media="all"/>
        <script src="javascript/richComponent.js" type="text/javascript"/>
        <script src="javascript/phoneValidator.js" type="text/javascript"/>

        <link rel="stylesheet" type="text/css" href="style/xtra.css" media="all"/>
        <script type="text/javascript" language="JavaScript">
            var timeoutLen = 105 * 60 * 1000;
            var timeout;

            setTimer();

            function initWindowTitle() {
            }

            function setTimer() {
                clearTimeout(timeout);
                timeout = setTimeout('redirecttologinpage()', timeoutLen);
            }

            function redirecttologinpage() {
                window.location.href = "timeout.jsf";
            }

            document.onkeydown = function (e) {
                if (check4Backspace(e) == false) return false;
                if (check4Refresh(e) == false) return false;
            }
            document.onkeypress = function (e) {
                return check4Backspace(e);
            }

            function check4Backspace(event) {
                var event = event || window.event;
                if (event.keyCode == 8) {
                    var elements = "HTML, BODY, TABLE, TBODY, TR, TD, DIV, BUTTON";
                    var d = event.srcElement || event.target;
                    var regex = new RegExp(d.tagName.toUpperCase());

                    if (d.contentEditable != 'true') { //it's not REALLY true, checking the boolean value (!== true) always passes, so we can use != 'true' rather than !== true/
                        if (regex.test(elements)) {
                            event.preventDefault ? event.preventDefault() : event.returnValue = false;
                        }
                    }
                }
            }

            function check4Refresh(event) {
                var event = event || window.event;
                if (event.keyCode == 116) {
                    event.returnValue = false;
                    event.keyCode = 0;
                    return false;
                }
                if (event.keyCode == 82) {
                    if (event.ctrlKey) {
                        event.returnValue = false;
                        event.keyCode = 0;
                        return false;
                    }
                }
            }
        </script>
        <title>
            <h:outputFormat escape="false" value="#{global['quotes.window.title.brokeradmin']}"/>
        </title>
    </head>

    <p:panel id="indexPanel" styleClass="main">

        <ui:include src="/templates/basePage.xhtml"/>

        <p:panel styleClass="header">
            <p:spacer height="100px"/>
            <h:graphicImage url="#{global['global.image.relative']}/intactLogoLeftLines.png"/>
            <h:form id="logoutForm">
                <h:commandLink action="#{authentificationController.logout}" styleClass="logout"
                               rendered="#{spoeController.isSpoeMode == 'true' and authentificationController.exitBtnDisplayed}">
                    <h:graphicImage url="#{global['global.image.relative']}/btnExit.png"/>
                </h:commandLink>
                <h:commandLink onclick="javascript:logout('#{authentificationController.urlToExit}');"
                               styleClass="logout"
                               rendered="#{spoeController.isSpoeMode == 'false' and authentificationController.exitBtnDisplayed}">
                    <h:graphicImage url="#{global['global.image.relative']}/btnExit.png"/>
                </h:commandLink>
            </h:form>

            <h:form id="provinceForm" styleClass="province"
                    rendered="#{not empty provinceController.companies}">
                <h:outputText value="#{global['subscription.company']}"/>
                <p:spacer width="10px"/>
                <p:selectOneMenu id="subscriptionCompanyOptions" value="#{provinceController.selectedCompany}"
                                 styleClass="input_province" >
                    <f:selectItems value="#{provinceController.companies}" var="subscriptionCompany"
                                   itemValue="#{subscriptionCompany}" itemLabel="#{subscriptionCompany.label}"/>

                    <p:ajax listener="#{provinceController.modifyCompany}"
                            update="indexPanel" />
                </p:selectOneMenu>
            </h:form>
        </p:panel>

<!--        TODO: check where those css classes come from? tab_accessManagement, tab_reports-->
        <p:panel styleClass="contents">
            <p:tabView styleClass="first-tabpanel" id="mainTab">

                <p:tab id="tab_accessManagement" title="#{global['common.tab.brokers']}"
                       action="#{subBrokersController.listPage}"
                       rendered="#{permissionController.checkAccountManagement}" styleClass="tab_accessManagement">
                    <h:graphicImage url="/image/contentTopGrey.png" styleClass="breadcrumb"/>
                    <ui:include src="/pages/brokers/brokers.xhtml"/>
                    <h:graphicImage url="/image/contentBot966.png"/>
                </p:tab>

                <p:tab id="tab_reports" title="#{global['common.tab.reports']}"
                       action="#{reportsTabController.reports}" rendered="#{permissionController.checkReports}"
                       styleClass="tab_reports">
                    <h:graphicImage url="/image/contentTopGrey.png" styleClass="breadcrumb"/>
                    <ui:include src="/pages/reports/reports.xhtml"/>
                    <h:graphicImage url="/image/contentBot966.png"/>
                </p:tab>

            </p:tabView>
        </p:panel>
    </p:panel>
</html>
