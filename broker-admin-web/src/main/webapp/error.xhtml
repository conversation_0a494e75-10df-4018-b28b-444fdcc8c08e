<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">

<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui">

<f:view locale="#{languageController.localeUrl}"/>
<h:head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <link href="#{facesContext.externalContext.context.contextPath}/style/richComponent.css" rel="stylesheet"
          type="text/css" media="all"/>
    <link href="#{facesContext.externalContext.context.contextPath}/style/primefacesComponent.css" rel="stylesheet"
          type="text/css" media="all"/>
    <link href="#{facesContext.externalContext.context.contextPath}/style/main.css" rel="stylesheet" type="text/css"
          media="all"/>
    <script src="#{facesContext.externalContext.context.contextPath}/javascript/richComponent.js"
            type="text/javascript"/>
</h:head>

<f:loadBundle var="global" basename="com.intact.brokeroffice.resources.global"/>

<p:panel styleClass="main" >
    <p:panel styleClass="header">
        <p:spacer height="100px"/>
        <h:graphicImage url="/image/IntactLogo.gif"/>
    </p:panel>

    <h:form id="errorForm" styleClass="techErrorGrid">
        <p:spacer height="30"/>
        <h:outputText styleClass="techErrorTime" value="#{currentDate}">
            <f:convertDateTime pattern="dd-MMM-yyyy HH:mm:ss" type="date"/>
        </h:outputText>
        <p:spacer height="30"/>
        <h:panelGrid>
            <h:outputText value="  Technical Problem" styleClass="errorTitle"/>
            <h:outputText
                    value="We are currently experiencing technical difficulties and are unable to continue. Every effort is being made to fix the problem as quickly as possible."/>
            <h:outputText
                    value="We apologise for any inconvenience this may cause and invite you to return at a later time. Thank you for your understanding."/>
        </h:panelGrid>

        <h:panelGrid>
            <h:outputText value="  Difficultés techniques" styleClass="errorTitle"/>
            <h:outputText
                    value="Nous éprouvons à l'heure actuelle des difficultés techniques qui nous empêchent de poursuivre. Nous mettons tout en oeuvre afin de pouvoir remédier à cette situation dans les plus brefs délais."/>
            <h:outputText
                    value="Nous tenons vous à présenter nos excuses pour ce désagrément et nous vous invitons à revenir plus tard."/>
            <h:outputText value="Merci pour votre compréhension."/>
        </h:panelGrid>
    </h:form>
    <p:panel styleClass="footer">
        <h:outputText value="©#{global['global.copyright.footer']}"/>
    </p:panel>
</p:panel>
</html>
