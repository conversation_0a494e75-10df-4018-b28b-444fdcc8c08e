<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:jee="http://www.springframework.org/schema/jee"
	xmlns:context="http://www.springframework.org/schema/context"
	xsi:schemaLocation="http://www.springframework.org/schema/beans
						http://www.springframework.org/schema/beans/spring-beans.xsd
						http://www.springframework.org/schema/context
						http://www.springframework.org/schema/context/spring-context.xsd
						http://www.springframework.org/schema/jee
						http://www.springframework.org/schema/jee/spring-jee.xsd">

	<context:annotation-config />

	<import resource="classpath:plp-services-beans.xml" />
	<import resource="classpath:cif-services-beans.xml" />
	<import resource="classpath:brm-services-beans.xml" />

	<import resource="classpath:transaction-beans.xml" />
	<import resource="classpath:brm-transaction-beans.xml" />
	<import resource="classpath:broker-admin-service.xml" />
	
	<import resource="classpath:broker-admin-web.xml" />
	<import resource="classpath:dw-services-beans.xml" />
	<import resource="classpath:broker-admin-security-off.xml" />
	<import resource="classpath:tools-string-service.xml" />
	<import resource="classpath:tools-comparison-service.xml" />

	<bean class="org.springframework.beans.factory.config.CustomScopeConfigurer">
		<property name="scopes">
			<map>
				<entry key="view">
					<bean class="com.intact.brokeroffice.customscope.ViewScope" />
				</entry>
			</map>
		</property>
	</bean>
</beans>

