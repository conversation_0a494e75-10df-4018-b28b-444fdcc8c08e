<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core">

    <f:facet name="footer">
        <h:panelGroup>
            <!-- // TODO 2024-06-18 rpesf382  Fix datascroller if necessary (was not done in Webzone) -->
            <!--            <rich:datascroller id="#{listName}FooterPage" styleClass="cell-spacing-table" for="#{listName}"-->
            <!--                               page="#{scrollerPage}"-->
            <!--                               pageIndexVar="pageIndex" pagesVar="pages" align="right" fastControls="hide"-->
            <!--                               boundaryControls="hide" stepControls="hide" ajaxSingle="false">-->
            <!--                <f:facet name="pages">-->
            <!--                    <h:outputFormat value="#{global['ds.footer.pageOf']}">-->
            <!--                        <f:param value="#{pageIndex}"/>-->
            <!--                        <f:param value="#{pages}"/>-->
            <!--                    </h:outputFormat>-->
            <!--                    <p:spacer width="10px"/>-->
            <!--                </f:facet>-->
            <!--            </rich:datascroller>-->

            <!--            <rich:datascroller id="#{listName}dsFooterIndex" styleClass="cell-spacing-table" for="#{listName}"-->
            <!--                               update="#{listName}Header #{listName}FooterPage" page="#{scrollerPage}"-->
            <!--                               align="right" fastControls="hide" boundaryControls="hide" stepControls="auto"-->
            <!--                               ajaxSingle="false">-->

            <!--                <f:facet name="previous">-->
            <!--                    <h:outputText styleClass="pagination" value="#{global['ds.footer.previous']}"/>-->
            <!--                </f:facet>-->

            <!--                <f:facet name="next">-->
            <!--                    <h:outputText styleClass="pagination" value="#{global['ds.footer.next']}"/>-->
            <!--                </f:facet>-->

            <!--            </rich:datascroller>-->

        </h:panelGroup>
    </f:facet>

</ui:composition>
