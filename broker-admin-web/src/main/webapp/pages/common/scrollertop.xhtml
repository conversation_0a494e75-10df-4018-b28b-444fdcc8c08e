<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                >

    <ui:param name="ds" value="#{listName}dsFooterIndex"/>
    <p:panelGrid styleClass="perPage" cellspacing="0" cellpadding="0" columns="1">
        <h:panelGroup>
            <h:outputText value="#{global['ds.header.display']}"/>
            <h:selectOneMenu id="#{listName}Header" value="#{scrollerPerPage}" immediate="true"
                             styleClass="input_numberItemsPerPage">
                <f:selectItems value="#{scrollerPerPageList}"/>

                <!-- // TODO 2024-06-18 rpesf382 Commented out because oncomplete attribute use "rich:" function -->
                <!-- // Re-enable it if necessary -->
<!--                 <p:ajax event="change" update="#{listName}" oncomplete="#{rich:component(ds)}.first()" />-->
                 <p:ajax event="change" update="#{listName}"  />
            </h:selectOneMenu>
            <h:outputText value="#{global['ds.header.perPage']}"/>
        </h:panelGroup>
    </p:panelGrid>

</ui:composition>
