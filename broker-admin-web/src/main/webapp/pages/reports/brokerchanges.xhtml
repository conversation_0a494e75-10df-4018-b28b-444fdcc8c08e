<ui:composition xmlns="http://www.w3.org/1999/xhtml"
				xmlns:ui="http://java.sun.com/jsf/facelets"
				xmlns:h="http://java.sun.com/jsf/html"
				xmlns:f="http://java.sun.com/jsf/core"
				xmlns:p="http://primefaces.org/ui"
				>

	<script src="javascript/utils.js" type="text/javascript"/>
	<script type="text/javascript">

		var brokerChangeListPage = 0;

		function setBrokerChangeListPage(pageNumber) {
			if (pageNumber || pageNumber == 0) {
				this.brokerChangeListPage = pageNumber;
			}
		}
	</script>

	<h:form styleClass="report-content-tab" id="brokerChangesForm">
		<h:inputHidden id="AntiCSRFToken" value="#{brokerChangesController.tokenCSRF}"/>
		<f:loadBundle var="msg_brkchange" basename="com.intact.brokeroffice.controller.brokerchanges.brokerchanges"/>

		<ui:param name="listName" value="brokerChangeList"/>
		<ui:param name="scrollerPage" value="#{brokerChangesController.scrollerPage}"/>

		<h:outputText value="#{msg_brkchange['title']}" styleClass="title"/>

		<p:panel styleClass="fsa-graph">
			<p:panel styleClass="leftfullpart"/>
			<h:graphicImage url="/image/ligneVirgule.png" styleClass="rightpart"/>
		</p:panel>

		<p:panel>
			<p:panelGrid columns="3" styleClass="changePeriod-wrap">

				<h:outputText value="#{msg_brkchange['date.from']}"/>

				<p:datePicker id="calendarFrom"
							  widgetVar="brokerCalendarFromVar"
							  value="#{brokerChangesController.dateFrom}"
							  showIcon="true" showOnFocus="false" showButtonBar="true"
							  monthNavigator="true" yearNavigator="true" showOtherMonths="true"
							  selectOtherMonths="true" pattern="yyyy-MM-dd"
							  mask="[9][9][9][9]-[9][9]-[9][9]" keepInvalid="true"
							  styleClass="input_calendarFrom" buttonClass="input_calendarFromButton"
							  inputClass="input_calendarFromTextfield"
							  converterMessage="#{msg_brkchange['error.date.InvalidFormat']}">

					<f:facet name="footer">
						<h:panelGrid columns="3" columnClasses="calendar-footer-center-cell, calendar-footer-center-cell, calendar-footer-center-cell">
							<h:outputText value="{selectedDateControl}" />
							<h:outputLabel value="{cleanControl}"/>
							<h:outputText value="{todayControl}" />
						</h:panelGrid>
					</f:facet>

					<p:ajax event="dateSelect" onstart="#{brokerChangesController.validateDates()}"
							onsuccess="brokerCalendarFromRC()"
							update="brokerChangesForm:calendarFromMessages"/>

					<p:remoteCommand name="brokerCalendarFromRC"
									 actionListener="#{brokerChangesController.findChanges}"
									 update="#{listName}, brokerChangesForm:calendarFromMessages, brokerChangesForm:calendarToMessages"/>

					<p:ajax event="change" onstart="#{brokerChangesController.validateDates()}"
							onsuccess="brokerCalendarFromRC()"
							update="brokerChangesForm:calendarFromMessages"/>
				</p:datePicker>

				<h:outputText value="#{msg_brkchange['date.format']}"/>
			</p:panelGrid>
			<p:panelGrid layout="tabular">
				<h:panelGroup id="calendarFromMessages" >
					<p:messages styleClass="errorMessage"
								showSummary="false"
								showDetail="true"
								for="brokerChangesForm:calendarFrom">
					</p:messages>
				</h:panelGroup>
			</p:panelGrid>
			<p:panelGrid columns="3" styleClass="changePeriod-wrap top-padding15">
				<h:outputText value="#{msg_brkchange['date.to']}"/>

				<p:datePicker id="calendarTo"
							  widgetVar="brokerCalendarToVar"
							  value="#{brokerChangesController.dateTo}"
							  showIcon="true" showOnFocus="false" showButtonBar="true"
							  monthNavigator="true" yearNavigator="true" showOtherMonths="true"
							  selectOtherMonths="true"
							  pattern="yyyy-MM-dd" mask="[9][9][9][9]-[9][9]-[9][9]" keepInvalid="true"
							  styleClass="input_calendarTo" buttonClass="input_calendarToButton"
							  inputClass="input_calendarToTextfield"

							  converterMessage="#{msg_brkchange['error.date.InvalidFormat']}">

					<f:facet name="footer">
						<h:panelGrid columns="3" columnClasses="calendar-footer-center-cell, calendar-footer-center-cell, calendar-footer-center-cell">
							<h:outputText value="{selectedDateControl}" />
							<h:outputLabel value="{cleanControl}"/>
							<h:outputText value="{todayControl}" />
						</h:panelGrid>
					</f:facet>

					<p:ajax event="dateSelect" onstart="#{brokerChangesController.validateDates()}"
							onsuccess="brokerCalendarToRC()"
							update="brokerChangesForm:calendarToMessages"/>

					<p:remoteCommand name="brokerCalendarToRC"
									 actionListener="#{brokerChangesController.findChanges}"
									 update="#{listName}, brokerChangesForm:calendarFromMessages, brokerChangesForm:calendarToMessages"/>

					<p:ajax event="change" onstart="#{brokerChangesController.validateDates()}"
							onsuccess="brokerCalendarToRC()"
							update="brokerChangesForm:calendarToMessages"/>
				</p:datePicker>

                <h:outputText value="#{msg_brkchange['date.format']}"/>
			</p:panelGrid>

			<p:panelGrid>
				<h:panelGroup id="calendarToMessages" >
					<p:messages styleClass="errorMessage"
								showSummary="false"
								showDetail="true"
								for="brokerChangesForm:calendarTo">
					</p:messages>
				</h:panelGroup>
			</p:panelGrid>

		</p:panel>
		<ui:include src="/pages/common/scrollertop.xhtml">
			<ui:param name="listName" value="brokerChangeList"/>
			<ui:param name="scrollerPerPage" value="#{brokerChangesController.scrollerPerPage}"/>
			<ui:param name="scrollerPerPageList" value="#{brokerChangesController.scrollerPerPageList}"/>
		</ui:include>

		<p:dataTable id="#{listName}" rowKeyVar="rowIndex" value="#{brokerChangesController.brokerChangeBeans}" var="bcb" width="100%"
					 rows="#{brokerChangesController.scrollerPerPage}" rowClasses="oddRow, evenRow"
					 widgetVar="brokerChangeListVar"
					 paginator="true"
					 paginatorPosition="bottom"
					 paginatorTemplate="{PreviousPageLink} {PageLinks} {NextPageLink}"
					 stripedRows="true">

			<p:ajax event="sort" onstart="startSort('brokerChangeListVar', setBrokerChangeListPage)" oncomplete="completeSort('brokerChangeListVar', brokerChangeListPage)" />

			<p:column sortBy="#{bcb.dateOfChange}" headerClass="link_sortByDateOfChange">
				<f:facet name="header">
					<h:outputText value="#{msg_brkchange['table.label.dateofchange']}"/>
					<h:graphicImage url="/image/ascdesc.gif"  onclick="triggerSort('#{listName}Var', 0)"/>
				</f:facet>
				<h:outputText styleClass="dateOfChange-#{rowIndex}"  value="#{bcb.dateOfChange}" rendered="#{!provinceController.company3}">
					<f:convertDateTime type="date" pattern="yyyy-MM-dd" timeZone="US/Eastern"/>
				</h:outputText>
				<h:outputText styleClass="dateOfChange-#{rowIndex}" value="#{bcb.dateOfChange}" rendered="#{provinceController.company3}">
					<f:convertDateTime type="date" pattern="yyyy-MM-dd" timeZone="Canada/Mountain"/>
				</h:outputText>

			</p:column>

			<p:column sortBy="#{bcb.clientFullName}" headerClass="link_sortByClientFullName">
				<f:facet name="header">
					<h:outputText value="#{msg_brkchange['table.label.client']}"/>
					<h:graphicImage url="/image/ascdesc.gif"  onclick="triggerSort('#{listName}Var', 1)"/>
				</f:facet>
				<h:outputText styleClass="clientFullName-#{rowIndex}" value="#{bcb.clientFullName}"/>

			</p:column>

			<p:column sortBy="#{bcb.previousBrokerName}" headerClass="link_sortByPreviousBrokerName">
				<f:facet name="header">
					<h:outputText value="#{msg_brkchange['table.label.former.servicing.broker']}"/>
					<h:graphicImage url="/image/ascdesc.gif"  onclick="triggerSort('#{listName}Var', 2)"/>
				</f:facet>
				<h:outputText styleClass="oldStuff-#{rowIndex}" value="#{bcb.previousBrokerName} - #{bcb.previousBrokerCity} #{msg_brkchange['brokerchange.office']}"/>
			</p:column>

			<p:column sortBy="#{bcb.newBrokerName}" headerClass="link_sortByNewBrokerName">
				<f:facet name="header">
					<h:outputText value="#{msg_brkchange['table.label.new.servicing.broker']}"/>
					<h:graphicImage url="/image/ascdesc.gif"  onclick="triggerSort('#{listName}Var', 3)"/>
				</f:facet>
				<h:outputText styleClass="newStuff-#{rowIndex}" value="#{bcb.newBrokerName} - #{bcb.newBrokerCity} #{msg_brkchange['brokerchange.office']}"/>
			</p:column>

			<p:column sortBy="#{bcb.reasonForChange}" headerClass="link_sortByReasonForChange">
				<f:facet name="header">
					<h:outputText value="#{msg_brkchange['table.label.reason.for.change']}"/>
					<h:graphicImage url="/image/ascdesc.gif"  onclick="triggerSort('#{listName}Var', 4)"/>
				</f:facet>
				<h:outputText styleClass="reasonChange-#{rowIndex}" value="#{msg_brkchange[bcb.reasonForChangeKey]}" rendered="#{not empty bcb.reasonForChange and empty bcb.assignorUserId }" />
				<h:outputText styleClass="reasonChange-#{rowIndex}" value="#{msg_brkchange[bcb.reasonForChangeKey]} #{bcb.assignorUserId}" rendered="#{not empty bcb.reasonForChange and not empty bcb.assignorUserId}" />
			</p:column>

			<ui:include src="/pages/common/scrollerbottom.xhtml" />

		</p:dataTable>
	</h:form>

</ui:composition>

