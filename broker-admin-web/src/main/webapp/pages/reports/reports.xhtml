<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui">


    <f:loadBundle var="msg_perf" basename="com.intact.brokeroffice.controller.performancemetrics.performancemetrics"/>
    <f:loadBundle var="msg_brkchanges" basename="com.intact.brokeroffice.controller.brokerchanges.brokerchanges"/>

    <p:tabView id="reportPanel" styleClass="content-block">
        <p:tab id="tab_POSChangesReports" title="#{msg_brkchanges['tab.title']}"
               action="#{reportsTabController.brokerChanges}"
               rendered="#{permissionController.checkViewChangeReport}"
               styleClass="tab_brokerChanges">
            <ui:include src="/pages/reports/brokerchanges.xhtml"/>
        </p:tab>

        <p:tab id="tab_PerformanceIndicators" title="#{msg_perf['tab.title']}"
               action="#{reportsTabController.performanceMetrics}"
               rendered="#{permissionController.checkViewKpiReport}"
               styleClass="tab_performanceMetrics">
            <ui:include src="/pages/reports/performancemetrics.xhtml"/>
        </p:tab>
        <p:spacer height="10px"/>
    </p:tabView>
</ui:composition>

