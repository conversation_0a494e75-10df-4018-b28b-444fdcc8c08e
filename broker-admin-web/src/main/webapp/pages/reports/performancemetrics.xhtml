<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                >

    <script src="javascript/utils.js" type="text/javascript"/>

    <h:form id="performanceMetricForm" styleClass="report-content-tab">
        <h:inputHidden id="AntiCSRFToken" value="#{performanceMetricsController.tokenCSRF}"/>
        <f:loadBundle var="msg_perf" basename="com.intact.brokeroffice.controller.performancemetrics.performancemetrics"/>

        <ui:param name="listName" value="performanceMetricList"/>
        <ui:param name="periodChoosen" value="periodOption"/>

        <h:outputText value="#{msg_perf['title']}" styleClass="title"/>
        <p:panel styleClass="fsa-graph">
            <p:panel styleClass="leftfullpart line845"/>
            <h:graphicImage url="/image/ligneVirgule.png" styleClass="rightpart"/>
        </p:panel>

        <p:panel styleClass="indicator-selectors">
            <p:panelGrid columns="2" columnClasses="errorMsg-from-kpi, errorMsg-to-kpi">
                <h:panelGroup styleClass="errorGrid" cellspacing="0" cellpadding="0" >
                    <p:messages styleClass="errorMessage" showDetail="true" globalOnly="true" layout="table" />
                    <p:message styleClass="errorMessage" for="calendarFrom" />
                </h:panelGroup>
                <h:panelGroup styleClass="errorGrid" cellspacing="0" cellpadding="0" >
                    <p:messages styleClass="errorMessage" showDetail="true" globalOnly="true" layout="table" />
                    <p:message styleClass="errorMessage" for="calendarTo" />
                </h:panelGroup>
            </p:panelGrid>

            <p:panelGrid id="performanceDisplay" columns="3" columnClasses="perf-period-to-from, perf-quote-source-col1, perf-expand-collapse">
                <p:panelGrid columns="3" styleClass="changePeriod-wrap">
                   <h:outputText value="#{msg_perf['performance.metric.label.start.period']}" />
                    <p:datePicker id="calendarFrom" widgetVar="performanceCalendarFromVar"
                                  value="#{performanceMetricsController.dateFrom}"
                                  showIcon="true" showOnFocus="false"
                                  showButtonBar="true" monthNavigator="true"
                                  yearNavigator="true" showOtherMonths="true"
                                  selectOtherMonths="true" pattern="yyyy-MM-dd"
                                  mask="[9][9][9][9]-[9][9]-[9][9]" keepInvalid="true"

                                  converterMessage="#{msg_perf['error.date.InvalidFormat']}">

                        <f:facet name="footer">
                            <h:panelGrid columns="3" columnClasses="calendar-footer-center-cell, calendar-footer-center-cell, calendar-footer-center-cell">
                                <h:outputText value="{selectedDateControl}" />
                                <h:outputLabel value="{cleanControl}"/>
                                <h:outputText value="{todayControl}" />
                            </h:panelGrid>
                        </f:facet>

                        <p:ajax event="dateSelect" onstart="#{performanceMetricsController.validateDates()}"
                                onsuccess="performanceCalendarFromRC()"
                                update="performanceMetricForm:calendarFromMessages"/>

                        <p:remoteCommand name="performanceCalendarFromRC"
                                         actionListener="#{performanceMetricsController.processMetric}"
                                         update="performanceMetricList, performanceMetricForm:calendarFromMessages, performanceMetricForm:calendarToMessages"/>

                        <p:ajax event="change" onstart="#{performanceMetricsController.validateDates()}"
                                onsuccess="performanceCalendarFromRC()"
                                update="performanceMetricForm:calendarFromMessages"/>

                        <f:facet name="footer">
                            <h:panelGrid columns="3" columnClasses="calendar-footer-center-cell, calendar-footer-center-cell, calendar-footer-center-cell">
                                <h:outputText value="{selectedDateControl}" />
                                <h:outputLabel value="{cleanControl}"/>
                                <h:outputText value="{todayControl}" />
                            </h:panelGrid>
                        </f:facet>
                    </p:datePicker>
                    <h:outputText value="#{msg_perf['date.format']}" />
                </p:panelGrid>
                <p:panelGrid>
                    <h:panelGroup id="calendarFromMessages">
                        <p:messages styleClass="errorMessage"
                                    showSummary="false"
                                    showDetail="true"
                                    for="performanceMetricForm:calendarFrom">
                        </p:messages>
                    </h:panelGroup>
                </p:panelGrid>

                <p:panelGrid columns="3" styleClass="changePeriod-wrap top-padding15">
                    <h:outputText value="#{msg_perf['performance.metric.label.end.period']}"/>
                    <p:datePicker id="calendarTo" widgetVar="performanceCalendarToVar"
                                  value="#{performanceMetricsController.dateTo}"
                                  showIcon="true" showOnFocus="false" showButtonBar="true"
                                  monthNavigator="true" yearNavigator="true"
                                  showOtherMonths="true" selectOtherMonths="true"
                                  pattern="yyyy-MM-dd" mask="[9][9][9][9]-[9][9]-[9][9]" keepInvalid="true"

                                  converterMessage="#{msg_perf['error.date.InvalidFormat']}">

                        <f:facet name="footer">
                            <h:panelGrid columns="3" columnClasses="calendar-footer-center-cell, calendar-footer-center-cell, calendar-footer-center-cell">
                                <h:outputText value="{selectedDateControl}" />
                                <h:outputLabel value="{cleanControl}"/>
                                <h:outputText value="{todayControl}" />
                            </h:panelGrid>
                        </f:facet>

                        <p:ajax event="dateSelect" onstart="#{performanceMetricsController.validateDates()}"
                                onsuccess="performanceCalendarToRC()"
                                update="performanceMetricForm:calendarToMessages"/>

                        <p:remoteCommand name="performanceCalendarToRC"
                                         actionListener="#{performanceMetricsController.processMetric}"
                                         update="performanceMetricList, performanceMetricForm:calendarFromMessages, performanceMetricForm:calendarToMessages"/>

                        <p:ajax event="change" onstart="#{performanceMetricsController.validateDates()}"
                                onsuccess="performanceCalendarToRC()"
                                update="performanceMetricForm:calendarToMessages"/>

                    </p:datePicker>
                    <h:outputText value="#{msg_perf['date.format']}" />
                </p:panelGrid>
                <p:panelGrid>
                    <h:panelGroup id="calendarToMessages">
                        <p:messages styleClass="errorMessage"
                                    showSummary="false"
                                    showDetail="true"
                                    for="performanceMetricForm:calendarTo">
                        </p:messages>
                    </h:panelGroup>
                </p:panelGrid>

                <h:panelGroup colspan="1" styleClass="top-padding15 displayBlock" cellspacing="0" cellpadding="0" >
                    <h:selectOneRadio styleClass="followupTable radioBtn-table" id="quoteSources"
                                      value="#{performanceMetricsController.selectedQuoteSource}" >
                        <f:selectItems value="#{performanceMetricsController.quoteSources}" var="quoteSource" itemValue="#{quoteSource}"  itemLabel="#{msg_perf[quoteSource]}"/>
                        <p:ajax event="change" listener="#{performanceMetricsController.processMetric}" update="performanceDisplay, expand-collapse-all, performanceMetricList"/>
                    </h:selectOneRadio>
                </h:panelGroup>
            </p:panelGrid>
        </p:panel>

        <p:panel id = "expand-collapse-all" styleClass="expand-collapse">
            <p:commandLink  rendered="#{not empty performanceMetricsController.consolidatedPerformanceMetricBeans}"
                            action="#{performanceMetricsController.display}"
                            update="performanceMetricList" styleClass="link_displayMetrics">
                <h:graphicImage url="/image/iconExpandAllaqua.gif"  />
            </p:commandLink>
            <p:commandLink rendered="#{not empty performanceMetricsController.consolidatedPerformanceMetricBeans}"
                           action="#{performanceMetricsController.hide}"
                           update="performanceMetricList" styleClass="link_hideMetrics">
                <h:graphicImage url="/image/iconCollapseAllaqua.gif"  />
            </p:commandLink>
        </p:panel>

        <p:dataTable id="performanceMetricList" rowKeyVar="row"
                     cellpadding="0" cellspacing="0" styleClass="performance-table" width="100%" border="0"
                     var="consolidateMetricItem"
                     rowKey="#{consolidateMetricItem.brokerName}"
                     expandedRow="#{consolidateMetricItem.show == true}"
                     tableStyle="table-layout:auto; word-wrap: break-word"
                     value="#{performanceMetricsController.consolidatedPerformanceMetricBeans}">

            <p:columnGroup type="header"
                           rendered="#{not empty performanceMetricsController.consolidatedPerformanceMetricBeans}">
                <p:column>
                    <f:facet name="header">
                        <p:row>
                            <p:column rowspan="3" styleClass="white-bgrnd-border-cell">
                                <f:facet name="header">
                                    <h:outputText styleClass="displayBlock"
                                            value="#{msg_perf['performance.metric.masterboker']}"
                                            escape="false"/>
                                    <h:outputText
                                            value="#{msg_perf['performance.metric.subbroker']}"
                                            escape="false"/>
                                </f:facet>
                            </p:column>

                            <p:column colspan="8" headerText="#{msg_perf['performance.metric.quote.status']}"
                                      styleClass="green-bgrnd-top-border-cell"/>
                            <p:column rowspan="3" headerText="#{msg_perf['performance.metric.total']}"
                                      styleClass="grey-bgrnd-cell"/>
                            <p:column colspan="4" headerText="#{msg_perf['performance.metric.followup']}"
                                      styleClass="green-bgrnd-top-border-cell"/>
                            <p:column rowspan="3" headerText="#{msg_perf['performance.metric.total']}"
                                      styleClass="grey-bgrnd-cell"/>
                        </p:row>
                        <p:row>
                            <p:column colspan="2" styleClass="white-bgrnd-lr-border-cell" breakBefore="true"
                                      headerText="#{msg_perf['performance.metric.quotes']}"/>
                            <p:column colspan="2" styleClass="white-bgrnd-lr-border-cell" breakBefore="true"
                                      headerText="#{msg_perf['performance.metric.purchase.req']}"/>
                            <p:column colspan="3" styleClass="white-bgrnd-lr-border-cell" breakBefore="true"
                                      headerText="#{msg_perf['performance.metric.uploaded.quotes']}"/>
                            <p:column rowspan="2" styleClass="white-bgrnd-border-cell" breakBefore="true"
                                      headerText="#{msg_perf['performance.metric.exp']}"/>

                            <p:column rowspan="2" width="60" styleClass="white-bgrnd-border-cell preformatted-text"
                                      headerText="#{msg_perf['performance.metric.never.contacted']}"/>
                            <p:column rowspan="2" width="60" styleClass="white-bgrnd-border-cell preformatted-text"
                                      headerText="#{msg_perf['performance.metric.further.contact.needed']}"/>
                            <p:column rowspan="2" width="60" styleClass="white-bgrnd-border-cell preformatted-text"
                                      headerText="#{msg_perf['performance.metric.no.contact.needed']}"/>
                            <p:column rowspan="2" width="60" styleClass="white-bgrnd-border-cell preformatted-text"
                                      headerText="#{msg_perf['performance.metric.never.contacted.no.contact.needed']}"/>
                        </p:row>
                        <p:row>
                            <p:column breakBefore="true" styleClass="white-bgrnd-b-border-cell"
                                      headerText="#{msg_perf['performance.metric.incomplete']}"/>
                            <p:column styleClass="white-bgrnd-rb-border-cell"
                                      headerText="#{msg_perf['performance.metric.complete']}"/>
                            <p:column styleClass="white-bgrnd-b-border-cell"
                                      headerText="#{msg_perf['performance.metric.incomplete']}"/>
                            <p:column styleClass="white-bgrnd-rb-border-cell"
                                      headerText="#{msg_perf['performance.metric.complete']}"/>

                            <p:column styleClass="white-bgrnd-b-border-cell"
                                      headerText="#{msg_perf['performance.metric.not.proceed']}"
                                      rendered="#{provinceController.companyA}"/>

                            <p:column styleClass="white-bgrnd-b-border-cell"
                                      headerText="#{msg_perf['performance.metric.accepted']}"
                                      rendered="#{provinceController.companyA}"/>

                            <p:column styleClass="white-bgrnd-rb-border-cell"
                                      headerText="#{msg_perf['performance.metric.refused']}"
                                      rendered="#{provinceController.companyA}"/>
                        </p:row>
                    </f:facet>
                </p:column>
            </p:columnGroup>

            <p:headerRow groupBy="#{consolidateMetricItem.brokerNbr}" expanded="true">
                <p:column colspan="14" styleClass="green-bgrnd-top-border-cell">
                    <h:outputText value="#{consolidateMetricItem.brokerName} #{consolidateMetricItem.brokerNbr}"/>
                </p:column>

                <p:column styleClass="alignRight green-bgrnd-top-border-cell">
                    <h:panelGroup styleClass="alignRight">
                        <p:commandLink rendered="#{consolidateMetricItem.show == true}"
                                       action="#{consolidateMetricItem.change}"
                                       update="performanceMetricList" styleClass="link_displayMetrics">
                            <h:graphicImage value="/image/iconCollapsewhite.gif"/>
                        </p:commandLink>
                        <p:commandLink rendered="#{consolidateMetricItem.show == false}"
                                       action="#{consolidateMetricItem.change}"
                                       update="performanceMetricList" styleClass="link_displayMetrics">
                            <h:graphicImage value="/image/iconExpandwhite.gif"/>
                        </p:commandLink>
                    </h:panelGroup>
                </p:column>
            </p:headerRow>

            <p:rowExpansion>
                <p:subTable id="details" var="metricItem"
                            value="#{consolidateMetricItem.performanceMetricBeans}"
                            rendered="#{consolidateMetricItem.show == true}">
                    <p:column>
                        <h:outputText value="#{metricItem.subBrokerName} #{metricItem.subBrokerNbr}"/>
                    </p:column>

                    <p:column styleClass="alignCenter">
                        <h:outputText value="#{metricItem.nbrOfQuotesIncomplete}"/>
                    </p:column>

                    <p:column styleClass="alignCenter">
                        <h:outputText value="#{metricItem.nbrOfQuotesComplete}"/>
                    </p:column>

                    <p:column styleClass="alignCenter">
                        <h:outputText value="#{metricItem.nbrOfPurchaseRequestIncomplete}"/>
                    </p:column>

                    <p:column styleClass="alignCenter">
                        <h:outputText value="#{metricItem.nbrOfPurchaseRequestComplete}"/>
                    </p:column>

                    <p:column styleClass="alignCenter">
                        <h:outputText value="#{metricItem.nbrOfUploadedQuotesNotProceed}"
                                      rendered="#{provinceController.companyA}"/>
                        <h:outputText value="&#160;" rendered="#{!provinceController.companyA}"/>
                    </p:column>

                    <p:column styleClass="alignCenter">
                        <h:outputText value="#{metricItem.nbrOfUploadedQuotesAccepted}"/>
                    </p:column>

                    <p:column styleClass="alignCenter">
                        <h:outputText value="#{metricItem.nbrOfUploadedQuotesRefused}"
                                      rendered="#{provinceController.companyA}"/>
                        <h:outputText value="&#160;" rendered="#{!provinceController.companyA}"/>
                    </p:column>

                    <p:column styleClass="alignCenter">
                        <h:outputText value="#{metricItem.nbrOfQuotesExpired}"/>
                    </p:column>

                    <p:column styleClass="alignCenter boldFont">
                        <h:outputText value="#{metricItem.nbrOfQuotesTotal}"/>
                    </p:column>

                    <p:column styleClass="alignCenter">
                        <h:outputText value="#{metricItem.nbrOfFollowUpNeverContacted}"/>
                    </p:column>

                    <p:column styleClass="alignCenter">
                        <h:outputText value="#{metricItem.nbrOfFollowUpContactNeeded}"/>
                    </p:column>

                    <p:column styleClass="alignCenter">
                        <h:outputText value="#{metricItem.nbrOfFollowUpNoContactNeeded}"/>
                    </p:column>

                    <p:column styleClass="alignCenter">
                        <h:outputText value="#{metricItem.nbrOfFollowUpNoneRequired}"/>
                    </p:column>

                    <p:column styleClass="alignCenter boldFont">
                        <h:outputText value="#{metricItem.nbrOfFollowUpTotal}"/>
                    </p:column>
                </p:subTable>
            </p:rowExpansion>

            <p:summaryRow>
                <p:column>
                    <h:outputText styleClass="alignCenter boldFont"
                                  value="#{msg_perf['performance.metric.sub.total']} #{consolidateMetricItem.brokerName} #{consolidateMetricItem.brokerNbr}"/>
                </p:column>

                <p:column styleClass="alignCenter boldFont">
                    <h:outputText value="#{consolidateMetricItem.nbrOfQuotesIncomplete}"/>
                </p:column>

                <p:column styleClass="alignCenter boldFont">
                    <h:outputText value="#{consolidateMetricItem.nbrOfQuotesComplete}"/>
                </p:column>

                <p:column styleClass="alignCenter boldFont">
                    <h:outputText value="#{consolidateMetricItem.nbrOfPurchaseRequestIncomplete}"/>
                </p:column>

                <p:column styleClass="alignCenter boldFont">
                    <h:outputText value="#{consolidateMetricItem.nbrOfPurchaseRequestComplete}"/>
                </p:column>

                <p:column styleClass="alignCenter boldFont">
                    <h:outputText value="#{consolidateMetricItem.nbrOfUploadedQuotesNotProceed}"
                                  rendered="#{provinceController.companyA}"/>
                    <h:outputText value="&#160;" rendered="#{!provinceController.companyA}"/>
                </p:column>

                <p:column styleClass="alignCenter boldFont">
                    <h:outputText value="#{consolidateMetricItem.nbrOfUploadedQuotesAccepted}"/>
                </p:column>

                <p:column styleClass="alignCenter boldFont">
                    <h:outputText value="#{consolidateMetricItem.nbrOfUploadedQuotesRefused}"
                                  rendered="#{provinceController.companyA}"/>
                    <h:outputText value="&#160;" rendered="#{!provinceController.companyA}"/>
                </p:column>

                <p:column styleClass="alignCenter boldFont">
                    <h:outputText value="#{consolidateMetricItem.nbrOfQuotesExpired}"/>
                </p:column>

                <p:column styleClass="alignCenter boldFont">
                    <h:outputText value="#{consolidateMetricItem.nbrOfQuotesTotal}"/>
                </p:column>

                <p:column styleClass="alignCenter boldFont">
                    <h:outputText value="#{consolidateMetricItem.nbrOfFollowUpNeverContacted}"/>
                </p:column>

                <p:column styleClass="alignCenter boldFont">
                    <h:outputText value="#{consolidateMetricItem.nbrOfFollowUpContactNeeded}"/>
                </p:column>

                <p:column styleClass="alignCenter boldFont">
                    <h:outputText value="#{consolidateMetricItem.nbrOfFollowUpNoContactNeeded}"/>
                </p:column>

                <p:column styleClass="alignCenter boldFont">
                    <h:outputText value="#{consolidateMetricItem.nbrOfFollowUpNoneRequired}"/>
                </p:column>

                <p:column styleClass="alignCenter boldFont">
                    <h:outputText value="#{consolidateMetricItem.nbrOfFollowUpTotal}"/>
                </p:column>
            </p:summaryRow>

            <p:columnGroup type="footer"
                           rendered="#{not empty performanceMetricsController.consolidatedPerformanceMetricBeans}">
                <p:row>
                    <p:column styleClass="boldFont" footerText="#{msg_perf['performance.metric.grand.total']}"/>

                    <p:column styleClass="alignCenter boldFont"
                              footerText="#{performanceMetricsController.totalPerformanceMetricBean.nbrOfQuotesIncomplete}"/>

                    <p:column styleClass="alignCenter boldFont"
                              footerText="#{performanceMetricsController.totalPerformanceMetricBean.nbrOfQuotesComplete}"/>

                    <p:column styleClass="alignCenter boldFont"
                              footerText="#{performanceMetricsController.totalPerformanceMetricBean.nbrOfPurchaseRequestIncomplete}"/>

                    <p:column styleClass="alignCenter boldFont"
                              footerText="#{performanceMetricsController.totalPerformanceMetricBean.nbrOfPurchaseRequestComplete}"/>

                    <p:column styleClass="alignCenter boldFont"
                              footerText="#{performanceMetricsController.totalPerformanceMetricBean.nbrOfUploadedQuotesNotProceed}&#10;&#160;"
                              rendered="#{provinceController.companyA}"/>
                    <p:column styleClass="alignCenter boldFont"
                              footerText=""
                              rendered="#{not provinceController.companyA}"/>

                    <p:column styleClass="alignCenter boldFont"
                              footerText="#{performanceMetricsController.totalPerformanceMetricBean.nbrOfUploadedQuotesAccepted}"/>

                    <p:column styleClass="alignCenter boldFont"
                              footerText="#{performanceMetricsController.totalPerformanceMetricBean.nbrOfUploadedQuotesRefused}&#10;&#160;"
                              rendered="#{provinceController.companyA}"/>
                    <p:column styleClass="alignCenter boldFont"
                              footerText=""
                              rendered="#{not provinceController.companyA}"/>

                    <p:column styleClass="alignCenter boldFont"
                              footerText="#{performanceMetricsController.totalPerformanceMetricBean.nbrOfQuotesExpired}"/>

                    <p:column styleClass="alignCenter boldFont"
                              footerText="#{performanceMetricsController.totalPerformanceMetricBean.nbrOfQuotesTotal}"/>

                    <p:column styleClass="alignCenter boldFont"
                              footerText="#{performanceMetricsController.totalPerformanceMetricBean.nbrOfFollowUpNeverContacted}"/>

                    <p:column styleClass="alignCenter boldFont"
                              footerText="#{performanceMetricsController.totalPerformanceMetricBean.nbrOfFollowUpContactNeeded}"/>

                    <p:column styleClass="alignCenter boldFont"
                              footerText="#{performanceMetricsController.totalPerformanceMetricBean.nbrOfFollowUpNoContactNeeded}"/>

                    <p:column styleClass="alignCenter boldFont"
                              footerText="#{performanceMetricsController.totalPerformanceMetricBean.nbrOfFollowUpNoneRequired}"/>

                    <p:column styleClass="alignCenter boldFont"
                              footerText="#{performanceMetricsController.totalPerformanceMetricBean.nbrOfFollowUpTotal}"/>
                </p:row>
            </p:columnGroup>
        </p:dataTable>
        <p:panel styleClass="search-refDate">
            <h:outputText value="#{msg_perf['performance.metric.based.on.quotes.received']}"/>

            <h:outputText value="#{performanceMetricsController.dateBefore}">
                <f:convertDateTime type="date" pattern="yyyy-MM-dd"/>
            </h:outputText>
        </p:panel>

    </h:form>

</ui:composition>
