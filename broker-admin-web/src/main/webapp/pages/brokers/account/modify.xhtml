<ui:composition xmlns="http://www.w3.org/1999/xhtml"
      xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
	  xmlns:p="http://primefaces.org/ui"
      >

    <script type="text/javascript">

			function blockIfNoAccessProfileSelected() {
			  var oneProfileSelected = false;
			  var cboxes = jQuery("#mainTab\\:brokerPanel\\:accountForm\\:accessProfileGrid_content input[type='checkbox']");
			   cboxes.each(function( index ) {
			     if ( jQuery(this).is(':checked') ) {
			      oneProfileSelected = true;
			      return;
			     }
			   });

			  if (oneProfileSelected == false) {
			    var alertLang = document.getElementById('mainTab:brokerPanel:accountForm:lang');
			    var alertMsg = (alertLang.value == 'fr' ? 'Au moins un profil d\'accès doit être sélectionné' : 'At least one access profile must be selected');
			    window.alert(alertMsg);
			    return false;
			  }
			  else {
			    return true;
			  }
			}
       </script>

	<h:form id="accountForm">
		<h:inputHidden id="AntiCSRFToken"
	               value="#{accountsController.tokenCSRF}"/>

		<h:inputHidden id="lang" value="#{accountsController.language}" />
	    <f:loadBundle var="msg_account" basename="com.intact.brokeroffice.controller.accounts.accounts"/>
	    <h:outputText value="#{msg_account[accountsTabController.accountPageTitleKey]}" styleClass="title"/>

		<p:panel styleClass="modify-form user-modify">
			<p:panelGrid columns="3" styleClass="subbroker-form form" cellspacing="0" >

				<h:outputText styleClass="formCol-title" value="#{msg_account['form.name']}"/>
				<h:graphicImage styleClass="img-arrow" url="/image/question_delimiter_arrow.gif"/>
				<p:panelGrid columns="1" styleClass="errorGrid" cellspacing="0" cellpadding="0" >
					<h:outputText value="#{accountsController.accountBean.userName}" />
				</p:panelGrid>

				<h:outputText styleClass="formCol-title" value="#{msg_account['form.full.name']}"/>
				<h:graphicImage styleClass="img-arrow" url="/image/question_delimiter_arrow.gif"/>
				<p:panelGrid columns="1" styleClass="errorGrid" cellspacing="0" cellpadding="0" >
					<h:outputText value="#{accountsController.accountBean.userFullName}" />
				</p:panelGrid>

				<h:outputText styleClass="formCol-title" value="#{msg_account['form.accessprofiles']}" />
				<h:graphicImage styleClass="img-arrow" url="/image/question_delimiter_arrow.gif"/>
				<p:dataGrid id="accessProfileGrid" styleClass="access-list"
							value="#{accountsController.accountBean.accessProfileBeans}" var="accessProfile" columns="1">
	      				<h:selectBooleanCheckbox value="#{accessProfile.selected}" styleClass="checkbox_masterBrokerSelected" />
	      				<h:outputText value="#{msg_account['form.accessprofile.'.concat(accessProfile.profile.code)]}" />
	      		</p:dataGrid>

				<h:outputText styleClass="formCol-title" value="#{msg_account['form.brokers']}" />
				<h:graphicImage styleClass="img-arrow" url="/image/question_delimiter_arrow.gif"/>
				<p:dataGrid styleClass="access-list" value="#{accountsController.accountBean.masterBrokerBeans}"  var="masterBrokers" columns="1">
	      				<h:selectBooleanCheckbox value="#{masterBrokers.selected}" styleClass="checkbox_masterBrokerSelected" />
	      				<h:outputText value="#{masterBrokers.number} #{masterBrokers.name}"/>
	      		</p:dataGrid>
			</p:panelGrid>
		</p:panel>

		<p:panel styleClass="btn-group">
			<!--  jmarois: PM9331: insure that image is always download to avoid buttons disappear temporarily  -->
			<p:panel style="display:none">
				<h:commandLink styleClass="actionBtnRightHover" >
					<h:outputText value="invisible right" />
				</h:commandLink>

				<h:commandLink styleClass="actionBtnLeftHover">
					<h:outputText value="invisible left" />
				</h:commandLink>
			</p:panel>

			<p:commandLink action="#{accountsController.listPage}"
						   styleClass="link_accountCancel actionBtn floatLeft"
						   update="mainTab:brokerPanel">
				<span class="left">
					<h:outputText value="#{global['form.button.cancel']}"/>
				</span>
			</p:commandLink>

			<p:commandLink onstart="return blockIfNoAccessProfileSelected();"
						   action="#{accountsController.modify}"
						   styleClass="link_accountModify actionBtn floatRight"
						   update="mainTab:brokerPanel">
				<span class="right">
					<h:outputText value="#{global['form.button.update']}"/>
		    		<h:graphicImage url="/image/btnRightArrow.png" styleClass="rightButtonArrow" />
		    	</span>
			</p:commandLink>
		</p:panel>
	</h:form>

</ui:composition>
