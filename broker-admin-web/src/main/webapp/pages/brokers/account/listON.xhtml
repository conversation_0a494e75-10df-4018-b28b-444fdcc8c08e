<ui:composition xmlns="http://www.w3.org/1999/xhtml"
      xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui">

    <style>
       .rich-sort-icon {
 			display: none;
		}
	</style>
	<script src="javascript/utils.js" type="text/javascript"/>
    <script type="text/javascript">

		var accountListONPage = 0;

        function setCaretToEnd (e) {
            var control = $((e.target ? e.target : e.srcElement).id);
            if (control.createTextRange) {
                var range = control.createTextRange();
                range.collapse(false);
                range.select();
            }
            else if (control.setSelectionRange) {
                control.focus();
                var length = control.value.length;
                control.setSelectionRange(length, length);
            }
            control.selectionStart = control.selectionEnd = control.value.length;
        }

		function setAccountListONPage(pageNumber) {
			if (pageNumber || pageNumber == 0) {
				this.accountListONPage = pageNumber;
			}
		}
    </script>

	<h:form id="accountListForm" styleClass="list-form">
		<h:inputHidden id="AntiCSRFToken" value="#{accountsController.tokenCSRF}"/>
		<f:loadBundle var="msg_account" basename="com.intact.brokeroffice.controller.accounts.accounts"/>

		<ui:param name="listName" value="accountList"/>
		<ui:param name="scrollerPage" value="#{accountsController.scrollerPage}"/>

		<h:outputText value="#{msg_account[accountsTabController.accountPageTitleKey]}" styleClass="title"/>

	    <h:panelGroup rendered="#{accountsController.accountBean!=null}">
			<h:outputFormat value="#{msg_account['modify.confirmation']}" styleClass="notification" rendered="#{accountsController.accountBean != null}">
				<f:param value="#{accountsController.accountBean.userName}" />
			</h:outputFormat >
 		</h:panelGroup>

		<ui:include src="/pages/common/scrollertop.xhtml">
			<ui:param name="listName" value="accountList"/>
			<ui:param name="scrollerPerPage" value="#{accountsController.scrollerPerPage}"/>
			<ui:param name="scrollerPerPageList" value="#{accountsController.scrollerPerPageList}"/>
		</ui:include>

		<p:dataTable id="#{listName}"
					 value="#{accountsController.accountBeans}"
					 widgetVar="accountsONWidgetVar"
					 var="sbb" rowKeyVar="rowIndex" width="100%"
			         rows="#{accountsController.scrollerPerPage}"
					 columnClasses="accountColumn"
					 filterDelay="700"
					 paginator="true"
					 paginatorPosition="bottom"
					 paginatorTemplate="{PreviousPageLink} {PageLinks} {NextPageLink}"
					 stripedRows="true">

			<p:ajax event="sort" onstart="startSort('accountsONWidgetVar', setAccountListONPage)" oncomplete="completeSort('accountsONWidgetVar', accountListONPage)" />

			<p:column sortBy="#{sbb.userName}"
					  filterBy="#{sbb.userName}"
					  filterValue="#{accountsController.userNameFilter}"
					  filterFunction="#{accountsController.filterUserName}"
					  headerClass="link_sortByUserName firstColumn">
				<f:facet name="header">
					<h:panelGrid>
						<h:panelGroup>
							<h:outputText styleClass="boldFont" value="#{msg_account['table.username']}"/>
							<h:graphicImage value="/image/ascdesc.gif" onclick="triggerSort('accountsWidgetVar',0)"/>
						</h:panelGroup>
						<p:messages id="messagesUserName" styleClass="errorMessage" showDetail="true" for="accountListForm"/>
					</h:panelGrid>
				</f:facet>
				<h:outputText value="#{sbb.userName}" styleClass="firstColumn userName-#{rowIndex}"/>
			</p:column>

			<p:column sortBy="#{sbb.userFullName}"
					  filterBy="#{sbb.userFullName}"
					  filterValue="#{accountsController.fullUserNameFilter}"
					  filterFunction="#{accountsController.filterFullUserName}"
					  headerClass="link_sortByFullUserName">
				<f:facet name="header">
					<h:panelGrid>
						<h:panelGroup>
							<h:outputText styleClass="boldFont" value="#{msg_account['table.full.name']}"/>
							<h:graphicImage value="/image/ascdesc.gif" onclick="triggerSort('accountsWidgetVar',1)"/>
						</h:panelGroup>
						<p:messages id="messagesFullUserName" styleClass="errorMessage" showDetail="true" for="accountListForm"/>
					</h:panelGrid>
				</f:facet>
				<h:outputText escape="false" value="#{sbb.userFullName}" styleClass="userFullName-#{rowIndex}"/>
			</p:column>

		    <p:column>
		        <f:facet name="header">
		        	<h:panelGrid>
		        		<h:panelGroup>
		            	<h:outputText value="#{msg_account['table.masterbrokers']}"/>
		            	</h:panelGroup>
		           	</h:panelGrid>
		        </f:facet>
		        <h:outputText escape="false" value="#{sbb.masterBrokers}" styleClass="masterBrokers-#{rowIndex}"/>
		    </p:column>

		    <p:column styleClass="actionCol">
		        <f:facet name="header">
		        	<h:panelGrid>
		        		<h:panelGroup>
		            		<h:outputText value="#{msg_account['table.action']}"/>
		            	</h:panelGroup>
		            </h:panelGrid>
		        </f:facet>

		        <p:commandLink value="#{msg_account['modify']}" action="#{accountsController.modifyPage}" update="mainTab:brokerPanel" styleClass="link_modifyUser modifyUserLink-#{rowIndex}">
					<f:setPropertyActionListener value="#{sbb.userName}" target="#{accountsController.selectedUser}"/>
					<f:param name="actionParam" value="#{sbb.userName}"/>
				</p:commandLink>
		    </p:column>

		    <ui:include src="/pages/common/scrollerbottom.xhtml" />

		</p:dataTable>

	</h:form>

</ui:composition>
