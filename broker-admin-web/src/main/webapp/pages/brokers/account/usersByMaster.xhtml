<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
		"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">

<html xmlns="http://www.w3.org/1999/xhtml"
	  xmlns:ui="http://java.sun.com/jsf/facelets"
	  xmlns:h="http://java.sun.com/jsf/html"
	  xmlns:f="http://java.sun.com/jsf/core"
	  xmlns:p="http://primefaces.org/ui">

<f:view locale="#{languageController.localeUrl}" />

<h:head>
	<link href="../../../style/richComponent.css" rel="stylesheet" type="text/css" media="all" />
	<link href="../../../style/main.css" rel="stylesheet" type="text/css" media="all" />
	<script src="../../../javascript/richComponent.js" type="text/javascript" />
	<title>#{msg_fsa['window.title']}</title>
</h:head>

<f:loadBundle var="global" basename="com.intact.brokeroffice.resources.global"/>
<f:loadBundle var="msg_account" basename="com.intact.brokeroffice.controller.accounts.accounts"/>

<body>
<p:panel id="usersByMasterPnl" styleClass="main">

	<p:panel styleClass="header">
		<p:spacer height="100px" />
		<h:graphicImage url="#{global['global.image.relative']}/intactLogoLeftLines.png" />
	</p:panel>

	<p:panel styleClass="contents accessDeniedPadding">
		<p:panel styleClass="ui-tabs-panel">
			<h:graphicImage url="/image/contentTopGrey.png" styleClass="breadcrumb"/>

			<h:form id="usersByMasterForm" styleClass="info-accessdenied-in-tab">
				<h:inputHidden id="AntiCSRFToken" value="#{viewUsersController.tokenCSRF}"/>
				<ui:param name="listName" value="usersByOwnerList"/>

				<!--
                    <ui:param name="scrollerPerPage" value="#{viewUsersController.scrollerPerPage}"/>
                    <ui:param name="scrollerPage" value="#{viewUsersController.scrollerPage}"/>
                    <ui:param name="scrollerPerPageList" value="#{viewUsersController.scrollerPerPageList}"/>
                -->

				<h:inputHidden id="selectedOwnerNo" value="#{viewUsersController.selectedOwnerNo}" />

				<h:outputText styleClass="title inline-heading" value="#{msg_account['title.users.assigned.to']}" />

				<h:outputText styleClass="left-margin10 boldFont title-description" value="   #{viewUsersController.master.name} (#{viewUsersController.master.number})"/>

				<p:panel styleClass="leftfullpart"/>

				<p:panel>
					<!--
                    <ui:include src="/pages/common/scrollertop.xhtml" />
                    -->
					<p:panel style="overflow-y: scroll;min-height:320px;">
						<p:dataTable id="#{listName}" rowKeyVar="rowIndex" value="#{viewUsersController.masterUsers}" var="mUser" width="90%"	columnClasses="user-column-1, user-column-2" rowClasses="oddRow, evenRow">

							<p:column headerClass="link_sortByMasterUserName firstColumn" sortBy="#{mUser.userName}">
								<f:facet name="header">
									<h:outputText escape="false" styleClass="boldFont left-margin15" value="#{msg_account['table.username']}"/>
									<h:graphicImage value="/image/ascdesc.gif"/>
								</f:facet>
								<h:outputText value="#{mUser.userName}" styleClass="firstColumn userName-#{rowIndex}"/>
							</p:column>

							<p:column sortBy="#{mUser.userFullName}" headerClass="link_sortByMasterUserFullName">
								<f:facet name="header">
									<h:outputText styleClass="boldFont" value="#{msg_account['table.full.name']}"/>
									<h:graphicImage value="/image/ascdesc.gif"/>
								</f:facet>
								<h:outputText escape="false" value="#{mUser.userFullName}" styleClass="userFullName-#{rowIndex}"/>
							</p:column>
							<!--
                            <ui:include src="/pages/common/scrollerbottom.xhtml" />
                            -->
						</p:dataTable>
					</p:panel>

					<!--  jmarois: PM9331: insure that image is always download to avoid buttons disappear temporarily  -->
					<p:panel style="display:none">
						<h:commandLink styleClass="actionBtnRightHover" >
							<h:outputText value="invisible right" />
						</h:commandLink>

						<h:commandLink styleClass="actionBtnLeftHover">
							<h:outputText value="invisible left" />
						</h:commandLink>
					</p:panel>
					<p:panel class="center-display">
						<p:commandLink onclick="closeAndRefresh()"
									   styleClass="link_closeAndRefresh actionBtn"
									   render="brokerPanel">
										<span class="left">
											<h:graphicImage url="/image/btnLeftArrow.png"/>
											<h:outputText value="#{global['form.button.back']}"/>
										</span>
						</p:commandLink>
					</p:panel>
				</p:panel>

			</h:form>
			<h:graphicImage styleClass="border-position" url="/image/contentBot966.png" />
		</p:panel>
	</p:panel>
</p:panel>
</body>

</html>
