<ui:composition xmlns="http://www.w3.org/1999/xhtml"
				xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui">

    <script src="javascript/utils.js" type="text/javascript"/>

    <f:loadBundle var="global" basename="com.intact.brokeroffice.resources.global"/>

    <p:tabView id="brokerPanel" styleClass="content-block"
               activeIndex="#{subBrokersTabController.activeTabIndex}"
               onTabShow="setDatatablePageNumber(['fsaWidgetVar', 'subBrokerTableVar'], 0);"
               focusOnLastActiveTab="true"
               contentClass="#{subBrokersTabController.isModify || accountsTabController.isModify ? 'rich-tabpanel-content-form':''}">

        <p:ajax event="tabChange" listener="#{subBrokersTabController.onTabChange}"
                update="brokerPanel, @id(tab_POSManagement)"/>
<!--                FSAManagement is hidden as per the client request-->
<!--                update="brokerPanel, @id(tab_POSManagement), @id(tab_FSAManagement)"/>-->

        <p:tab id="tab_POSManagement" title="#{global['common.tab.subbroker']}"
               action="#{subBrokersController.listPage}"
               rendered="#{permissionController.checkManageSubBrokerProfile}"
               styleClass="tab_subBroker">
            <ui:include src="#{subBrokersTabController.pagePath}"/>
        </p:tab>

        <p:tab id="tab_userManagement" title="#{global['common.tab.account']}"
               action="#{accountsController.listPage}"
               rendered="#{permissionController.checkManageAccountAccess}"
               styleClass="tab_account">
            <ui:include src="#{accountsTabController.pagePath}"/>
        </p:tab>

        <p:tab id="tab_FSAManagement" title="#{global['common.tab.fsa']}"
               action="#{fsaController.listPage}"
               rendered="false"
               styleClass="tab_fsa">
<!--            As per the request from the client,-->
<!--            FSA management menu is desactivated and hidden-->
            <!--               rendered="#{permissionController.checkManageFsa}"-->

            <ui:include src="#{fsaTabController.pagePath}"/>
        </p:tab>

    </p:tabView>
</ui:composition>
