<ui:composition xmlns="http://www.w3.org/1999/xhtml"
      xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui">

	<script src="javascript/utils.js" type="text/javascript"/>

	<script type="text/javascript">
		var fsaPage = 0;

		function setFsaPage(pageNumber) {
			if (pageNumber || pageNumber == 0) {
				this.fsaPage = pageNumber;
			}
		}
	</script>

	<h:form id="fsaListFormfsaListForm" styleClass="list-form">
		<h:inputHidden id="AntiCSRFToken" value="#{fsaController.tokenCSRF}"/>
		<f:loadBundle var="msg_fsa" basename="com.intact.brokeroffice.controller.fsa.fsa"/>
		<ui:param name="listName" value="fsaListTable"/>
		<ui:param name="scrollerPage" value="#{fsaController.scrollerPage}"/>

		<h:outputText value="#{msg_fsa[fsaTabController.fsaPageTitleKey]}" styleClass="title"/>
		<p:panel styleClass="fsa-action">
			<p:commandLink value="#{msg_fsa['add.new.table']}" action="#{fsaController.addPage}" update="mainTab:brokerPanel" styleClass="link_fsaAddNewTable" />
			<p:commandLink value="#{msg_fsa['list.fsa.refresh']}"
						   onstart="setDatatablePageNumber(['fsaWidgetVar'], 0)"
						   action="#{fsaController.refreshListPage}" event="click" update="#{listName}" styleClass="link_fsaRefreshList" />
		</p:panel>

		<p:panel styleClass="fsa-graph">
			<p:panel styleClass="leftfullpart"/>
			<h:graphicImage url="/image/ligneVirgule.png" styleClass="rightpart"/>
		</p:panel>

		<ui:include src="/pages/common/scrollertop.xhtml">
			<ui:param name="listName" value="fsaListTable"/>
			<ui:param name="scrollerPerPage" value="#{fsaController.scrollerPerPage}"/>
			<ui:param name="scrollerPerPageList" value="#{fsaController.scrollerPerPageList}"/>
		</ui:include>

		<p:dataTable id="#{listName}" rowKeyVar="rowIndex" value="#{fsaController.fsaTableBeans}" var="fsaTable"
					 widgetVar="fsaWidgetVar"
					 width="100%"
					 columnClasses="fsa-table-col1, fsa-table-col2, fsa-table-col3, fsa-table-col4, fsa-table-col5"
					 rows="#{fsaController.scrollerPerPage}"
					 rowClasses="oddRow, evenRow"
					 styleClass="fsaPanelContent"
					 filterDelay="700"
					 paginator="true"
					 paginatorPosition="bottom"
					 paginatorTemplate="{PreviousPageLink} {PageLinks} {NextPageLink}"
					 stripedRows="true">

			   <p:ajax event="sort" onstart="startSort('fsaWidgetVar', setFsaPage)" oncomplete="completeSort('fsaWidgetVar', fsaPage)" />

			    <p:column sortBy="#{fsaTable.fileName}" headerClass="link_sortByFsaFileName firstColumn">
		        	<f:facet name="header">
		            	<h:outputText value="#{msg_fsa['list.fsa.tableFilename']}"/>
						<h:graphicImage value="/image/ascdesc.gif" onclick="triggerSort('fsaWidgetVar',0)"/>
		        	</f:facet>
		        	<h:commandLink id="tableInfoLink" styleClass="downloadLink-#{rowIndex}" actionListener="#{fsaController.downloadFile}">
		       	 		<h:outputText value="#{fsaTable.fileName}" styleClass="firstColumn filename-#{rowIndex}"/>
		       	 		<f:param name="fileName" value="#{fsaTable.fileName}" />
		       	 		<f:param name="loadId" value="#{fsaController.getSecurityLabel(fsaTable)}" />
		       	 	</h:commandLink>
		    	</p:column>

		    	<p:column sortBy="#{fsaTable.company}" headerClass="link_sortByFsaCompany firstColumn">
		        	<f:facet name="header">
		            	<h:outputText value="#{msg_fsa['list.fsa.company']}"/>
						<h:graphicImage value="/image/ascdesc.gif" onclick="triggerSort('fsaWidgetVar',1)"/>
		        	</f:facet>
		        	<h:outputText value="#{fsaTable.company.code}" styleClass="company-#{rowIndex}" />
		    	</p:column>

		    	<p:column sortBy="#{fsaTable.businessContext}" headerClass="link_sortByFsaBusinessContext firstColumn">
		        	<f:facet name="header">
		            	<h:outputText value="#{msg_fsa['list.fsa.businessContext']}"/>
						<h:graphicImage value="/image/ascdesc.gif" onclick="triggerSort('fsaWidgetVar',2)"/>
		        	</f:facet>
		        	<h:outputText value="#{msg_fsa['businessContext.code.'.concat(fsaTable.businessContext.code)]}" styleClass="businessContext-#{rowIndex}" />
		    	</p:column>

		    	<p:column sortBy="#{fsaTable.startDate}" headerClass="link_sortByFsaStartDate firstColumn">
		        	<f:facet name="header">
		            	<h:outputText value="#{msg_fsa['list.fsa.processingDate']}"/>
						<h:graphicImage value="/image/ascdesc.gif" onclick="triggerSort('fsaWidgetVar',3)"/>
		        	</f:facet>
		        	<h:outputText value="#{fsaTable.startDate}" styleClass="startDate-#{rowIndex}" rendered="#{!provinceController.company3}">
		        		<f:convertDateTime type="date" pattern="yyyy-MM-dd" timeZone="US/Eastern"/>
		        	</h:outputText>
		        	<h:outputText value="#{fsaTable.startDate}" styleClass="startDate-#{rowIndex}" rendered="#{provinceController.company3}">
		        		<f:convertDateTime type="date" pattern="yyyy-MM-dd" timeZone="Canada/Mountain"/>
		        	</h:outputText>
		    	</p:column>

		    	<p:column sortBy="#{fsaTable.statusStr}" headerClass="link_sortByFsaStatusStr firstColumn">
		        	<f:facet name="header">
		            	<h:outputText value="#{msg_fsa['list.fsa.result']}"/>
						<h:graphicImage value="/image/ascdesc.gif" onclick="triggerSort('fsaWidgetVar',4)"/>
		        	</f:facet>
		        	 <h:outputLink value="pages/brokers/fsa/msgReport.jsf" styleClass="statusLink-#{rowIndex}" rendered="#{fsaTable.status.code != 'PROC'}" target="_blank" >
		        		<h:outputText value="#{msg_fsa[fsaTable.statusKey]}" styleClass="status-#{rowIndex}"/>
		        		<f:param name="uploadFileStatus" value="#{fsaTable.status.code}"/>
		        		<f:param name="selectedLoadId" value="#{fsaController.getSecurityLabel(fsaTable)}"/>
		        	</h:outputLink>

		        	<h:outputText value="#{msg_fsa[fsaTable.statusKey]}" styleClass="status-#{rowIndex}" rendered="#{fsaTable.status.code == 'PROC'}"/>

		    	</p:column>

			    <ui:include src="/pages/common/scrollerbottom.xhtml" />
		</p:dataTable>

	</h:form>
</ui:composition>
