<ui:composition xmlns="http://www.w3.org/1999/xhtml"
      xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
	  xmlns:p="http://primefaces.org/ui"
      >

	<h:form id="fsaModifiyForm" style="width:926px;">
			<h:inputHidden id="AntiCSRFToken" value="#{fsaTabController.tokenCSRF}"/>
		<f:loadBundle var="msg" basename="com.intact.brokeroffice.controller.fsa.fsa"/>
			<p:spacer width="20px" />
			<h:outputText value="#{msg[fsaTabController.fsaPageTitleKey]}" styleClass="title"/>
			<p:spacer width="100px" />
		    <p:commandLink value="#{msg['add.new.table']}" action="#{fsaController.addPage}" update="mainTab:brokerPanel" styleClass="link_fsaAddTable"/>
		    <p:spacer width="300px" />
	   		<h:outputText style="text-align: right;" value="#{msg['form.fsa.search']}" />
	   		<p:spacer width="10px" />
		    <h:inputText id="search" value="#{fsaController.searchFsaorPostalCode}" size="6" maxlength="6"
		    				styleClass="input_searchFsaOrPostalCode"/>
		    <p:spacer width="10px" />
		    <h:commandLink action="#{fsaController.searchFsaInfosBeans}" styleClass="link_searchFsaInfosBeans">
				<span> <h:graphicImage style="vertical-align: bottom" url="/image/searchIcon.png" /> </span>
			</h:commandLink>
		    <p:messages styleClass="errorMessage" for="search" />

		<p:panel styleClass="leftfullpart line865"></p:panel>
	    <h:graphicImage url="/image/ligneVirgule.png" styleClass="rightpart"/>

		<ui:param name="listName" value="fsaListInfos"/>
		<ui:param name="scrollerPage" value="#{fsaController.scrollerPage}"/>

		<ui:include src="/pages/common/scrollertop.xhtml">
			<ui:param name="listName" value="fsaListInfos"/>
			<ui:param name="scrollerPerPage" value="#{fsaController.scrollerPerPage}"/>
			<ui:param name="scrollerPerPageList" value="#{fsaController.scrollerPerPageList}"/>
		</ui:include>

		<p:dataTable id="#{listName}" rowKeyVar="rowIndex" value="#{fsaController.fsaInfosBeans}" var="fsa" width="100%"
			rows="#{fsaController.scrollerPerPage}" rowClasses="oddRow, evenRow">

			   <p:column headerClass="firstColumn" styleClass="firstColumn">
			      <f:facet name="header">
		        	<p:panelGrid id="pnlGrid43" columns="1" style="float:left;">
		    			<h:commandButton id="deselectAllBtn" immediate="true" action="#{fsaController.deselectAll}" image="image/iconDeselectAll.png"
		    				onclick="checkAllCheckboxesInTable( this.id, false );"  styleClass="button_deselectAllBtn deselect-#{rowIndex}" />
		    			<h:commandButton id="selectAllBtn" immediate="true" action="#{fsaController.selectAll}" image="image/iconSelectAll.png"
		    				onclick="checkAllCheckboxesInTable( this.id, true );"  styleClass="button_selectAllBtn select-#{rowIndex}"" />
		    				<p:toolTip for="selectAllBtn" value="#{msg['form.select.all']}" direction="bottom-right"  showDelay="100" styleClass="tooltip" layout="block"/>
		   		 			<p:toolTip for="deselectAllBtn" value="#{msg['form.deselect.all']}" direction="bottom-right"  showDelay="100" styleClass="tooltip" layout="block"/>
		    		</p:panelGrid>
		        </f:facet>
   					<h:selectBooleanCheckbox id="selected" immediate="true" value="#{fsa.selected}"
	       				valueChangeListener="#{fsaController.toggleSelection}" styleClass="input_fsaSelected selected-#{rowIndex}">
				      <p:ajax event="click" update="fsaListTable"/>
					</h:selectBooleanCheckbox>
	         </p:column>

			 <p:column sortBy="#{fsa.fsaOrPostalCode}">
		        <f:facet name="header">
		            <h:outputText value="#{msg['list.fsa.fsa']}"/>
		        </f:facet>
				<p:spacer width="10px" />
				<h:outputText value="#{fsa.fsaOrPostalCode}" styleClass="fsa-#{rowIndex}"/>
			</p:column>

		    <p:column sortBy="#{fsa.assignedPos}">
		        <f:facet name="header">
		            <h:outputText value="#{msg['list.fsa.pos']}"/>
		        </f:facet>
		        <h:outputText value="#{fsa.assignedPos}" styleClass="assigned-#{rowIndex}"/>
		    </p:column>

		    <p:column sortBy="#{fsa.startDate}">
		        <f:facet name="header">
		            <h:outputText value="#{msg['list.fsa.starDate']}"/>
		        </f:facet>
		        <h:outputText value="#{fsa.startDate}" styleClass="startDate-#{rowIndex}"/>
		    </p:column>

		     <p:column sortBy="#{fsa.endDate}">
		        <f:facet name="header">
		            <h:outputText value="#{msg['list.fsa.endDate']}"/>
		        </f:facet>
		        <h:outputText value="#{fsa.endDate}" styleClass="endDate-#{rowIndex}"/>
		    </p:column>

			    <ui:include src="/pages/common/scrollerbottom.xhtml" />

		</p:dataTable>

		<p:panel>
		<h:outputText value="#{msg['form.fsa.change']}" styleClass="sectionTitle" />

			<p:panelGrid id="pnlGrid94" columns="4" styleClass="subbroker-form form" cellspacing="0" >

				<h:outputText value="#{msg['form.fsa.fsa']}" />
				<h:panelGroup>
					<p:panelGrid id="pnlGrid98" columns="1" styleClass="errorGrid" cellspacing="0" cellpadding="0" >
						<h:inputText id="fsaPostalCode" value="#{fsaController.fsaInfoBean.fsaOrPostalCode}" styleClass="input_fsaPostalCode"/>
						<p:messages styleClass="errorMessage" for="fsaPostalCode" />
					</p:panelGrid>
				</h:panelGroup>

				<h:outputText value="#{msg['form.fsa.pos']}" />
				<h:panelGroup>
					<p:panelGrid id="pnlGrid106" columns="1" styleClass="errorGrid" cellspacing="0" cellpadding="0" >
						<h:selectOneMenu id="attendingBrokerNameLine1" value="#{fsaController.fsaInfoBean.assignedPos}"
										styleClass="input_attendingBrokerNameLine1">
							<f:selectItems id="tableOwnerList" value="#{fsaController.tableOwnerList}"/>
						</h:selectOneMenu>
						<p:messages styleClass="errorMessage" for="attendingBrokerNameLine1" />
					</p:panelGrid>
				</h:panelGroup>

				<h:outputText value="#{msg['form.fsa.starDate']}" />
				<h:panelGroup>
					<p:panelGrid id="pnlGrid117" columns="1" styleClass="errorGrid" cellspacing="0" cellpadding="0" >
						<p:calendar id="calendarStart" value="#{fsaController.fsaInfoBean.changeStartDate}"
			    				datePattern="yyyy-MM-dd" showWeeksBar="false" enableManualInput="true"
			    				cellWidth="24px" cellHeight="22px" style="width:200px" immediate="true"

			    				styleClass="input_calendarStart" inputClass="input_calendarStartTextfield" buttonClass="input_calendarStartButton" >
		        		</p:calendar>
		        		<p:messages styleClass="errorMessage" for="calendarStart" />
        			</p:panelGrid>
				</h:panelGroup>

				<h:outputText value="#{msg['form.fsa.endDate']}" />
				<h:panelGroup>
					<p:panelGrid id="pnlGrid130" columns="1" styleClass="errorGrid" cellspacing="0" cellpadding="0" >
						<p:calendar id="calendarEnd" value="#{fsaController.fsaInfoBean.changeEndDate}"
			    				datePattern="yyyy-MM-dd" showWeeksBar="false" enableManualInput="true"
			    				cellWidth="24px" cellHeight="22px" style="width:200px" immediate="true"

			    				styleClass="input_calendarEnd" inputClass="input_calendarEndTextField" buttonClass="input_calendarEndButton" >
		        		</p:calendar>
		        		<p:messages styleClass="errorMessage" for="calendarEnd" />
        			</p:panelGrid>
				</h:panelGroup>
		</p:panelGrid>
		</p:panel>

		<p:panel styleClass="leftfullpart line865"></p:panel>
	    <h:graphicImage url="/image/ligneVirgule.png" styleClass="rightpartIn"/>

        <p:panel>
			<h:outputText value="#{msg['form.table.change']}" styleClass="sectionTitle" />
			<p:panelGrid id="pnlGrid148" columns="4" styleClass="subbroker-form form" cellspacing="0" >
				<h:outputText value="#{msg['form.fsa.starDate']}" />
				<h:panelGroup>
					<p:panelGrid id="pnlGrid151" columns="1" styleClass="errorGrid" cellspacing="0" cellpadding="0" >
						<p:calendar id="calendarTableStart" value="#{fsaController.fsaTableBean.startDate}"
			    				datePattern="yyyy-MM-dd" showWeeksBar="false" enableManualInput="true"
			    				cellWidth="24px" cellHeight="22px" style="width:200px" immediate="true"

			    				styleClass="input_calendarTableStart" inputClass="input_calendarTableStartTextField" buttonClass="input_calendarTableStartButton" >
		        		</p:calendar>
		        		<p:messages styleClass="errorMessage" for="calendarTableStart" />
        			</p:panelGrid>
				</h:panelGroup>

				<h:outputText value="#{msg['form.fsa.endDate']}" />
				<h:panelGroup>
					<p:panelGrid id="pnlGrid164" columns="1" styleClass="errorGrid" cellspacing="0" cellpadding="0" >
						<p:calendar id="calendarTableEnd" value="#{fsaController.fsaTableBean.endDate}"
			    				datePattern="yyyy-MM-dd" showWeeksBar="false" enableManualInput="true"
			    				cellWidth="24px" cellHeight="22px" style="width:200px" immediate="true"

			    				styleClass="input_calendarTableEnd" inputClass="input_calendarTableEndTextField" buttonClass="input_calendarTableEndButton" >
		        		</p:calendar>
		        		<p:messages styleClass="errorMessage" for="calendarTableEnd" />
        			</p:panelGrid>
				</h:panelGroup>

       		</p:panelGrid>
		</p:panel>
		<p:spacer height="20px" />

		<p:panel>
			<!--  jmarois: PM9331: insure that image is always download to avoid buttons disappear temporarily  -->
			<p:panel style="display:none">
				<h:commandLink styleClass="actionBtnRightHover" >
					<h:outputText value="invisible right" />
				</h:commandLink>

				<h:commandLink styleClass="actionBtnLeftHover">
					<h:outputText value="invisible left" />
				</h:commandLink>
			</p:panel>

			<h:commandLink action="#{fsaController.listPage}" styleClass="link_fsaCancel actionBtn floatLeft">
				<span class="left">
					<h:outputText value="#{global['form.button.cancel']}"/>
				</span>
			</h:commandLink>

			<h:commandLink action="#{fsaController.addNewFsaOrUpdateTable}" styleClass="link_fsaUpdate actionBtn floatRight">
				<span class="right">
					<h:outputText value="#{global['form.button.update']}"/>
		    		<h:graphicImage url="/image/btnRightArrow.png" styleClass="rightButtonArrow" />
		    	</span>
			</h:commandLink>
		</p:panel>

	</h:form>
</ui:composition>
