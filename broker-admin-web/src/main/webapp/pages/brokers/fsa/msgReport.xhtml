<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">

<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui"
      >

<f:view locale="#{languageController.localeUrl}"/>

<h:head>
    <link href="../../../style/richComponent.css" rel="stylesheet" type="text/css" media="all"/>
    <link href="../../../style/primefacesComponent.css" rel="stylesheet" type="text/css" media="all"/>
    <link href="../../../style/main.css" rel="stylesheet" type="text/css" media="all"/>
    <script src="../../../javascript/richComponent.js" type="text/javascript"/>
    <title>#{msg_fsa['title.report_fsa']}</title>
</h:head>

<f:loadBundle var="global" basename="com.intact.brokeroffice.resources.global"/>
<f:loadBundle var="msg_fsa" basename="com.intact.brokeroffice.controller.fsa.fsa"/>

<body>
<p:panel id="uploadMsgReportPanel" styleClass="main">

    <p:panel styleClass="header">
        <p:spacer height="100px"/>
        <h:graphicImage url="#{global['global.image.relative']}/intactLogoLeftLines.png"/>
    </p:panel>

    <p:panel styleClass="contents accessDeniedPadding">
        <p:panel styleClass="border-mid-content ui-tabs-panel">
            <h:graphicImage url="/image/contentTopGrey.png" styleClass="breadcrumb"/>
            <h:form id="uploadMsgRepForm" styleClass="content-block">
                <h:inputHidden id="AntiCSRFToken" value="#{fsaController.tokenCSRF}"/>
                <ui:param name="listName" value="fsaMsgList"/>
                <ui:param name="scrollerPage" value="#{fsaController.scrollerPage}"/>

                <h:inputHidden id="selectedLoadId" value="#{fsaUploadReportController.selectedLoadId}"/>
                <h:inputHidden id="uploadFileStatus" value="#{fsaUploadReportController.uploadFileStatus}"/>

                <p:panel class="upload-status-wrap">
                    <h:outputText styleClass="title upload-status-title fail" value="#{msg_fsa['title.status.report.failed']}"
                                  rendered="#{!fsaUploadReportController.uploadSucceedInd}"/>
                    <h:outputText styleClass="title upload-status-title success" value="#{msg_fsa['title.status.report.succeed']}"
                                  rendered="#{fsaUploadReportController.uploadSucceedInd}"/>

                    <h:panelGrid columns="2" styleClass="upload-status-detail">
                        <h:outputText value="#{currentDate}" rendered="#{!provinceController.company3}">
                            <f:convertDateTime type="date" pattern="yyyy-MM-dd HH:mm:ss" timeZone="US/Eastern"/>
                        </h:outputText>
                        <h:outputText value="#{currentDate}" rendered="#{provinceController.company3}">
                            <f:convertDateTime type="date" pattern="yyyy-MM-dd HH:mm:ss" timeZone="Canada/Mountain"/>
                        </h:outputText>

                        <h:outputText
                                styleClass="errorSize boldFont"
                                value="#{msg_fsa['fsa.report.total.error']} #{fsaUploadReportController.errorListSize}"
                                rendered="#{!fsaUploadReportController.uploadSucceedInd}"/>
                    </h:panelGrid>

                    <p:panel styleClass="fsa-graph">
                        <p:panel styleClass="leftfullpart line845"/>
                        <h:graphicImage url="/image/ligneVirgule.png" styleClass="rightpart"/>
                    </p:panel>

                    <p:panel rendered="#{!fsaUploadReportController.uploadSucceedInd and fsaUploadReportController.failed}">
                        <ui:param name="listName" value="fsaMsgList"/>
                        <ui:param name="scrollerPage" value="#{fsaUploadReportController.scrollerPage}"/>

                        <ui:include src="/pages/common/scrollertop.xhtml">
                            <ui:param name="listName" value="fsaMsgList"/>
                            <ui:param name="scrollerPerPage" value="#{fsaUploadReportController.scrollerPerPage}"/>
                            <ui:param name="scrollerPerPageList" value="#{fsaUploadReportController.scrollerPerPageList}"/>
                        </ui:include>

                        <p:dataTable id="#{listName}"
                                     value="#{fsaUploadReportController.fsaMessageBeans}"
                                     rows="#{fsaUploadReportController.scrollerPerPage}"
                                     var="fsaMsg" width="100%"
                                     rowClasses="oddRow, oddRow"
                                     columnClasses="fsa-rep-col2, fsa-rep-col3, fsa-rep-col4, fsa-rep-col5, fsa-rep-col6, fsa-rep-col7, fsa-rep-col8, fsa-rep-col9 "
                                     paginator="true"
                                     paginatorPosition="bottom"
                                     paginatorTemplate="{PreviousPageLink} {PageLinks} {NextPageLink}"
                                     stripedRows="true">

                            <p:column  headerClass="firstColumn">
                                <f:facet name="header">
                                    <h:outputText value="#{msg_fsa['list.status.report.pos.nbr']}" escape="false"/>
                                </f:facet>
                                <h:outputText value="#{fsaMsg.rejectSubBrokerNumber}"/>
                            </p:column>

                            <p:column>
                                <f:facet name="header">
                                    <h:outputText value="#{msg_fsa['list.status.report.message']}" escape="false"/>
                                </f:facet>
                                <h:outputText value="#{msg_fsa[fsaMsg.messageTypeCdKey]}" rendered="#{!fsaMsg.displayed}"/>
                                <h:outputText value="#{msg_fsa[fsaMsg.messageTypeCdKey]} (#{fsaMsg.message} records)"
                                              rendered="#{fsaMsg.displayed}"/>
                            </p:column>

                            <p:column>
                                <f:facet name="header">
                                    <h:outputText value="#{msg_fsa['list.status.report.fsa']}" escape="false"/>
                                </f:facet>
                                <h:outputText value="#{fsaMsg.rejectPostalCode}"
                                              rendered="#{not empty fsaMsg.rejectPostalCode}"/>
                                <h:outputText value="#{fsaMsg.rejectFsa}" rendered="#{not empty fsaMsg.rejectFsa}"/>
                            </p:column>

                            <p:column>
                                <f:facet name="header">
                                    <h:outputText value="#{msg_fsa['list.status.report.company']}" escape="false"/>
                                </f:facet>
                                <h:outputText value="#{fsaMsg.rejectCompanyNumber}"/>
                            </p:column>

                            <p:column>
                                <f:facet name="header">
                                    <h:outputText value="#{msg_fsa['list.status.report.effective.date']}" escape="false"/>
                                </f:facet>
                                <h:outputText value="#{fsaMsg.rejectStartDate}" rendered="#{!provinceController.company3}">
                                    <f:convertDateTime type="date" pattern="yyyy-MM-dd" timeZone="US/Eastern"/>
                                </h:outputText>
                                <h:outputText value="#{fsaMsg.rejectStartDate}" rendered="#{provinceController.company3}">
                                    <f:convertDateTime type="date" pattern="yyyy-MM-dd" timeZone="Canada/Mountain"/>
                                </h:outputText>
                            </p:column>

                            <p:column>
                                <f:facet name="header">
                                    <h:outputText value="#{msg_fsa['list.status.report.expiry.date']}" escape="false"/>
                                </f:facet>
                                <h:outputText value="#{fsaMsg.rejectEndDate}" rendered="#{!provinceController.company3}">
                                    <f:convertDateTime type="date" pattern="yyyy-MM-dd" timeZone="US/Eastern"/>
                                </h:outputText>
                                <h:outputText value="#{fsaMsg.rejectEndDate}" rendered="#{provinceController.company3}">
                                    <f:convertDateTime type="date" pattern="yyyy-MM-dd" timeZone="Canada/Mountain"/>
                                </h:outputText>
                            </p:column>

                            <p:column>
                                <f:facet name="header">
                                    <h:outputText value="#{msg_fsa['list.status.report.origin']}" escape="false"/>
                                </f:facet>
                                <h:outputText value="#{fsaMsg.rejectExternalSystemOriginCd}"/>
                            </p:column>

                            <p:column>
                                <f:facet name="header">
                                    <h:outputText value="#{msg_fsa['list.status.report.language']}" escape="false"/>
                                </f:facet>
                                <h:outputText value="#{fsaMsg.language}"/>
                            </p:column>

                            <ui:include src="/pages/common/scrollerbottom.xhtml"/>
                        </p:dataTable>
                    </p:panel>

                    <p:panel styleClass="upload-details-tableList" rendered="#{!fsaUploadReportController.uploadSucceedInd and fsaUploadReportController.cancelled}">
                        <p:panelGrid styleClass="upload-error-intro" columns="1">
                            <h:outputText value="#{msg_fsa['fsa.report.forced.cancel.msg1']}"/>
                        </p:panelGrid>
                        <p:panelGrid styleClass="upload-cancle-table" columns="2">
                            <h:outputText value="#{msg_fsa['fsa.report.forced.cancel.load.id']}"/>
                            <h:outputText value="#{fsaUploadReportController.fsaLoadBean.loadId}"/>

                            <h:outputText value="#{msg_fsa['fsa.report.forced.cancel.file.name']}"/>
                            <h:outputText value="#{fsaUploadReportController.fsaLoadBean.fileName}"/>

                            <h:outputText value="#{msg_fsa['fsa.report.forced.cancel.process.date']}"/>
                            <h:outputText value="#{fsaUploadReportController.fsaLoadBean.startDate}"
                                          rendered="#{!provinceController.company3}">
                                <f:convertDateTime type="date" pattern="yyyy-MM-dd" timeZone="US/Eastern"/>
                            </h:outputText>
                            <h:outputText value="#{fsaUploadReportController.fsaLoadBean.startDate}"
                                          rendered="#{provinceController.company3}">
                                <f:convertDateTime type="date" pattern="yyyy-MM-dd" timeZone="Canada/Mountain"/>
                            </h:outputText>
                        </p:panelGrid>

                        <p:panelGrid columns="1">
                            <h:outputText value="#{msg_fsa['fsa.report.forced.cancel.msg2']}"/>
                        </p:panelGrid>
                    </p:panel>

                    <p:panel rendered="#{fsaUploadReportController.uploadSucceedInd}">
                        <p:panelGrid styleClass="upload-details-tableList" columns="1">
                            <h:outputText
                                    value="#{fsaUploadReportController.fsaMessageBean.fsaProceedNbr} #{msg_fsa['fsa.nbr.fsa.processed']}"/>
                        </p:panelGrid>
                        <p:panelGrid styleClass="upload-details-tableList" columns="1">
                            <h:outputText
                                    value="#{msg_fsa['fsa.total.upload.time']} #{fsaUploadReportController.fsaMessageBean.proceedTime}"/>
                        </p:panelGrid>
                    </p:panel>

                    <!--  jmarois: PM9331: insure that image is always download to avoid buttons disappear temporarily  -->
                    <p:panel style="display:none">
                        <h:commandLink styleClass="actionBtnRightHover">
                            <h:outputText value="invisible right"/>
                        </h:commandLink>

                        <h:commandLink styleClass="actionBtnLeftHover">
                            <h:outputText value="invisible left"/>
                        </h:commandLink>
                    </p:panel>
                    <p:panel class="btn-group center-flex">
                        <h:commandLink onclick="closeAndRefresh()" styleClass="link_closeMsgReport actionBtn floatCenter"
                                   update="mainTab:brokerPanel">
                                <span class="left">
                                    <h:graphicImage url="/image/btnLeftArrow.png"/>
                                    <h:outputText value="#{global['form.button.back']}"/>
                                </span>
                        </h:commandLink>
                    </p:panel>
                </p:panel>
            </h:form>
            <h:graphicImage styleClass="border-position" url="/image/contentBot966.png"/>
        </p:panel>
    </p:panel>
</p:panel>
</body>
</html>
