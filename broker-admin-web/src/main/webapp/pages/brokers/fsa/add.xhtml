<ui:composition xmlns="http://www.w3.org/1999/xhtml"
      xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui"
      >

    <f:view locale="#{languageController.localeUrl}" />

	<h:form id="fsaAddForm" enctype="multipart/form-data">
		<h:inputHidden id="AntiCSRFToken"
	               value="#{fsaTabController.tokenCSRF}"/>

		<f:loadBundle var="msg_fsa" basename="com.intact.brokeroffice.controller.fsa.fsa"/>
		<ui:param name="provincePrefix" value="fsa.province."/>
		<h:outputText value="#{msg_fsa[fsaTabController.fsaPageTitleKey]}" styleClass="title"/>
		<p:commandLink value="#{msg_fsa['add.table.return']}" action="#{fsaController.listPage}" update="mainTab:brokerPanel" styleClass="margin-vertical15"/>

		<p:panel>
			<p:panelGrid columns="3" styleClass="modify-sale-block add-table subbroker-form form" cellspacing="0" rendered="#{fsaController.fsaUploadBean!=null}" >

				<h:outputText value="#{msg_fsa['add.table.file']}" />
				<h:graphicImage url="/image/question_delimiter_arrow.gif"/>
				<h:panelGroup>
					<p:panelGrid columns="1" styleClass="errorGrid" cellspacing="0" cellpadding="0" >
						<p:fileUpload accept=".dat,.csv,.xls,.xlsx,.txt" id="fsaFileUpload" size="60"
									  value="#{fsaController.fsaUploadBean.file}" styleClass="input_fsaFileUpload"
						              mode="simple"/>
						<p:messages styleClass="errorMessage"
									showSummary="false"
									showDetail="true"
									for="fsaAddForm:fsaFileUpload" />
					</p:panelGrid>
				</h:panelGroup>

				<h:outputText value="#{msg_fsa['add.table.businessContext']}" />
				<h:graphicImage url="/image/question_delimiter_arrow.gif"/>
				<h:panelGroup>
					<p:panelGrid columns="1" styleClass="errorGrid" cellspacing="0" cellpadding="0" >
						<h:selectOneMenu id="businessContext" value="#{fsaController.fsaUploadBean.businessContext}" styleClass="input_fsaLineOfBusiness">
						 	<f:selectItem itemValue="#{null}" itemLabel="-- #{msg_fsa['add.table.select']} --" />
							<f:selectItems value="#{fsaController.fsaUploadBean.businessContextItems}" var="businessContextItem" itemValue="#{businessContextItem.code}"  itemLabel="#{msg_fsa['businessContext.code.'.concat(businessContextItem.code)]}"/>
						</h:selectOneMenu>
						<p:messages styleClass="errorMessage" for="businessContext" />
					</p:panelGrid>
				</h:panelGroup>
		 	</p:panelGrid>

		</p:panel>

		<p:panel styleClass="btn-group">
			<!--  jmarois: PM9331: insure that image is always download to avoid buttons disappear temporarily  -->
			<p:panel style="display:none">
				<h:commandLink styleClass="actionBtnRightHover" >
					<h:outputText value="invisible right" />
				</h:commandLink>

				<h:commandLink styleClass="actionBtnLeftHover">
					<h:outputText value="invisible left" />
				</h:commandLink>
			</p:panel>

			<p:commandLink action="#{fsaController.listPage}" styleClass="link_fsaCancel actionBtn" update="mainTab:brokerPanel">
					<h:outputText value="#{global['form.button.cancel']}"/>
			</p:commandLink>

			<p:commandLink action="#{fsaController.fileUploaded}" styleClass="link_fsaUpdate actionBtn"
						   update="mainTab:brokerPanel:tab_FSAManagement">
				<span class="right">
					<h:outputText value="#{global['form.button.update']}"/>
		    		<h:graphicImage url="/image/btnRightArrow.png" styleClass="rightButtonArrow" />
		    	</span>
			</p:commandLink>

		</p:panel>
	</h:form>
</ui:composition>
