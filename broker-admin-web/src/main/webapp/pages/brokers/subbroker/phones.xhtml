<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                >
        <p:panelGrid  styleClass="modify-sale-block" columns="3" cellspacing="0" columnClasses="td1,td2,td3">

        <h:outputText value="#{msg_sbb['form.phone.pos.'.concat(currentPhone)]}" />
        <h:graphicImage url="/image/question_delimiter_arrow.gif"/>
        <h:panelGroup>
            <p:panelGrid columns="1" styleClass="errorGrid" cellspacing="0" cellpadding="0" >
                <h:panelGroup styleClass="group-blocks">
                    <span class="phone-detail">
                        <h:outputText styleClass="org-name" value="#{msg_sbb['WEBBK']}" />
                        <h:inputText id="phoneNumberPOS" value="#{subBrokersController.subBrokerBean.linesOfBusiness[currentLob].applications[currentApp].phones[currentPhone].brokerPhone}" styleClass="input_phoneNumberPointOfSale masked" />
                        <h:selectBooleanCheckbox id="phoneNumberPointOfSaleTollFreeInd" value="#{subBrokersController.subBrokerBean.linesOfBusiness[currentLob].applications[currentApp].phones[currentPhone].brokerTollFree}" styleClass="input_brokerSource" />
                        <h:outputText value="#{msg_sbb['toll.free']}" />
                    </span>
                    <span class="phone-detail">
                        <h:outputText styleClass="org-name" value="#{msg_sbb['INT']}" />
                        <h:inputText id="phoneNumber" value="#{subBrokersController.subBrokerBean.linesOfBusiness[currentLob].applications[currentApp].phones[currentPhone].intactPhone}" styleClass="input_phoneNumberPhoneNumber masked"/>
                        <h:selectBooleanCheckbox id="phoneNumberTollFreeInd" value="#{subBrokersController.subBrokerBean.linesOfBusiness[currentLob].applications[currentApp].phones[currentPhone].intactTollFree}" styleClass="input_brokerSource" />
                        <h:outputText value="#{msg_sbb['toll.free']}" />
                    </span>
                    <h:outputText styleClass="errorMessage" escape="false" value="#{subBrokersController.showError('phone_'.concat(currentLob).concat('_').concat(currentApp).concat('_').concat(currentPhone).concat('_').concat('I'))}" />
                    <h:outputText styleClass="errorMessage" escape="false" value="#{subBrokersController.showError('phone_'.concat(currentLob).concat('_').concat(currentApp).concat('_').concat(currentPhone).concat('_').concat('B'))}" />
                </h:panelGroup>
            </p:panelGrid>
        </h:panelGroup>
        <h:panelGroup></h:panelGroup>
    </p:panelGrid>
</ui:composition>
