<ui:composition xmlns="http://www.w3.org/1999/xhtml"
				xmlns:ui="http://java.sun.com/jsf/facelets"
				xmlns:h="http://java.sun.com/jsf/html"
        xmlns:p="http://primefaces.org/ui"
				>

    <h:outputText styleClass="sub-title" value="#{msg_sbb['form.sales.'.concat(currentLob)]}"/>

    <p:panelGrid styleClass="line-identity" columns="3" cellspacing="0" columnClasses="td1,td2,td3">
        <h:panelGroup>
            <h:outputText value="#{msg_sbb['form.email']}"/>
        </h:panelGroup>

        <h:graphicImage url="/image/question_delimiter_arrow.gif"/>
        <h:panelGroup>
            <p:panelGrid columns="1" styleClass="errorGrid" cellspacing="0" cellpadding="0">
                <h:inputText
                        value="#{subBrokersController.subBrokerBean.linesOfBusiness[currentLob].contact.emailAddress}"
                        size="40" maxlength="73" styleClass="input_subBrokerEmail"/>
                <h:outputText styleClass="errorMessage" escape="false"
                              value="#{subBrokersController.showError('email'.concat(currentLob))}"/>
            </p:panelGrid>
        </h:panelGroup>
        <h:panelGroup>
            <h:outputText value="#{msg_sbb['form.chat']}"/>
        </h:panelGroup>
        <h:graphicImage url="/image/question_delimiter_arrow.gif"/>

        <p:panelGrid columns="1" styleClass="errorGrid" cellspacing="0" cellpadding="0">
            <h:inputText value="#{subBrokersController.subBrokerBean.linesOfBusiness[currentLob].contact.chatId}"
                         size="40" maxlength="50" styleClass="input_subBrokerChatId"/>
            <h:outputText styleClass="errorMessage" escape="false"
                          value="#{subBrokersController.showError('chatId'.concat(currentLob))}"/>
        </p:panelGrid>
        <h:panelGroup></h:panelGroup>
    </p:panelGrid>

    <ui:repeat var="app"
               value="#{subBrokersController.subBrokerBean.linesOfBusiness[lob.lineOfBusiness].applications.values().toArray()}">
        <h:panelGroup>
            <h:panelGroup>
                <ui:include src="/pages/brokers/subbroker/application.xhtml">
                    <ui:param name="currentApp" value="#{app.id}"></ui:param>
                    <ui:param name="currentLob" value="#{lob.lineOfBusiness}"></ui:param>
                </ui:include>
            </h:panelGroup>
        </h:panelGroup>
        <h:panelGroup>
            <h:panelGroup rendered="#{app.id == 'WQ'}">
                <ui:include src="/pages/brokers/subbroker/bundle/application.xhtml">
                    <ui:param name="currentApp" value="#{app.id}"></ui:param>
                    <ui:param name="currentLob" value="#{lob.lineOfBusiness}"></ui:param>
                </ui:include>
            </h:panelGroup>
        </h:panelGroup>
    </ui:repeat>

</ui:composition>
