<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui">
    <style>
        .rich-sort-icon {
            display: none;
        }
    </style>
    <script src="javascript/utils.js" type="text/javascript"/>
    <script type="text/javascript">

        var subBrokerPage = 0;

        function setCaretToEnd(e) {
            var control = $((e.target ? e.target : e.srcElement).id);
            if (control.createTextRange) {
                var range = control.createTextRange();
                range.collapse(false);
                range.select();
            } else if (control.setSelectionRange) {
                control.focus();
                var length = control.value.length;
                control.setSelectionRange(length, length);
            }
            control.selectionStart = control.selectionEnd = control.value.length;
        }

        function setSubBrokerPage(pageNumber) {
            if (pageNumber || pageNumber == 0) {
                this.subBrokerPage = pageNumber;
            }
        }
    </script>

    <h:form id="subBrokerListForm" styleClass="list-form">
        <h:inputHidden id="AntiCSRFToken" value="#{subBrokersController.tokenCSRF}"/>
        <f:loadBundle var="msg_subbrokers" basename="com.intact.brokeroffice.controller.subbrokers.subbrokers"/>

        <ui:param name="listName" value="brokerList"/>
        <ui:param name="scrollerPage" value="#{subBrokersController.scrollerPage}"/>

        <h:outputText value="#{msg_subbrokers[subBrokersTabController.subBrokerPageTitleKey]}" styleClass="title"/>

        <h:panelGroup rendered="#{subBrokersController.subBrokerBean != null}">
            <h:outputFormat value="#{msg_subbrokers['modify.confirmation']}" styleClass="notification">
                <f:param value="#{subBrokersController.subBrokerBean.subBrokerNo}"/>
            </h:outputFormat>
        </h:panelGroup>

        <ui:include src="/pages/common/scrollertop.xhtml">
            <ui:param name="listName" value="brokerList"/>
            <ui:param name="scrollerPerPage" value="#{subBrokersController.scrollerPerPage}"/>
            <ui:param name="scrollerPerPageList" value="#{subBrokersController.scrollerPerPageList}"/>
        </ui:include>

        <p:dataTable id="#{listName}" rowKeyVar="rowIndex" value="#{subBrokersController.subBrokerBeans}"
                     widgetVar="subBrokerTableVar"
                     update="#{listName}dsFooterIndex" var="sbb" width="100%"
                     rows="#{subBrokersController.scrollerPerPage}" rowClasses="oddRow, evenRow"
                     filterDelay="700"
                     paginator="true"
                     paginatorPosition="bottom"
                     paginatorTemplate="{PreviousPageLink} {PageLinks} {NextPageLink}"
                     stripedRows="true">

            <p:ajax event="sort" onstart="startSort('subBrokerTableVar', setSubBrokerPage)" oncomplete="completeSort('subBrokerTableVar', subBrokerPage)" />

            <p:column sortBy="#{sbb.masterOwnerNo}"
                      filterBy="#{sbb.masterOwnerNo}"
                      filterFunction="#{subBrokersController.filterOwnerNo}"
                      headerClass="link_sortByMasterOwnerNo"
                      styleClass="numCol">
                <f:facet name="header">
                    <h:panelGrid>
                        <h:panelGroup>
                            <h:outputText styleClass="boldFont" value="#{msg_subbrokers['table.owner']}"/>
                            <h:graphicImage value="/image/ascdesc.gif" onclick="triggerSort('subBrokerTableVar',0)"/>
                        </h:panelGroup>
                    </h:panelGrid>
                </f:facet>

                <h:outputLink value="pages/brokers/account/usersByMaster.jsf?selectedOwnerNo=#{sbb.masterOwnerNo}"
                              target="_blank" styleClass="masterBrokerLink-#{rowIndex}">
                    <h:outputText value="#{sbb.masterOwnerNo}"
                                  styleClass="firstColumn boldFont masterOwnerNo-#{rowIndex}"
                                  rendered="#{sbb.masterOwnerNo eq sbb.subBrokerNo}"/>
                    <h:outputText value="#{sbb.masterOwnerNo}" styleClass="firstColumn masterOwnerNo-#{rowIndex}"
                                  rendered="#{sbb.masterOwnerNo ne sbb.subBrokerNo}"/>
                </h:outputLink>
            </p:column>

            <p:column sortBy="#{sbb.subBrokerNo}"
                      filterBy="#{sbb.subBrokerNo}"
                      filterFunction="#{subBrokersController.filterPosNo}"
                      headerClass="link_sortBySubBrokerNo"
                      styleClass="numCol">
                <f:facet name="header">
                    <h:panelGrid>
                        <h:panelGroup>
                            <h:outputText styleClass="boldFont" value="#{msg_subbrokers['table.subBrokerNo']}"/>
                            <h:graphicImage url="/image/ascdesc.gif"  onclick="triggerSort('subBrokerTableVar', 1)"/>
                        </h:panelGroup>
                    </h:panelGrid>
                </f:facet>
                <h:outputText value="#{sbb.subBrokerNo}" styleClass="subBrokerNo-#{rowIndex}"
                              rendered="#{sbb.masterOwnerNo ne sbb.subBrokerNo}"/>
                <h:outputText value="#{sbb.subBrokerNo}" styleClass="boldFont subBrokerNo-#{rowIndex}"
                              rendered="#{sbb.masterOwnerNo eq sbb.subBrokerNo}"/>
            </p:column>

            <p:column sortBy="#{sbb.name}"
                      filterBy="#{sbb.name}"
                      filterFunction="#{subBrokersController.filterName}"
                      headerClass="link_sortByName"
                      styleClass="nameCol">
                <f:facet name="header">
                    <h:panelGrid>
                        <h:panelGroup>
                            <h:outputText styleClass="boldFont" value="#{msg_subbrokers['table.name']}"/>
                            <h:graphicImage url="/image/ascdesc.gif"  onclick="triggerSort('subBrokerTableVar', 2)"/>
                        </h:panelGroup>
                    </h:panelGrid>
                </f:facet>
                <h:outputText value="#{sbb.name}" styleClass="name-#{rowIndex}"
                              rendered="#{sbb.masterOwnerNo ne sbb.subBrokerNo}"/>
                <h:outputText value="#{sbb.name}" styleClass="boldFont name-#{rowIndex}"
                              rendered="#{sbb.masterOwnerNo eq sbb.subBrokerNo}"/>
            </p:column>

            <p:column sortBy="#{subBrokersController.getStatus(sbb)}"
                      headerClass="link_sortByWebAttendingBroker">
                <f:facet name="header">
                    <h:panelGrid>
                        <h:panelGroup>
                            <h:outputText styleClass="boldFont" value="#{msg_subbrokers['table.enable.attending']}"
                                          escape="false"/>
                            <h:graphicImage url="/image/ascdesc.gif"  onclick="triggerSort('subBrokerTableVar', 3)"/>
                        </h:panelGroup>
                    </h:panelGrid>
                </f:facet>
                <h:outputText styleClass="consent-#{rowIndex}"
                                   value="#{subBrokersController.getStatus(sbb)}"/>
            </p:column>

            <p:column sortBy="#{sbb.city}"
                      filterBy="#{sbb.city};#{sbb.province}"
                      filterFunction="#{subBrokersController.filterCityProvince}"
                      headerClass="link_sortByCityProvince"
                      styleClass="locationCol">
                <f:facet name="header">
                    <h:panelGrid>
                        <h:panelGroup>
                            <h:outputText styleClass="boldFont" value="#{msg_subbrokers['table.city']}"/>
                            <h:graphicImage url="/image/ascdesc.gif" onclick="triggerSort('subBrokerTableVar', 4)"/>
                        </h:panelGroup>
                    </h:panelGrid>
                </f:facet>
                <h:outputText value="#{sbb.city}, #{sbb.province}" styleClass="city-province-#{rowIndex}"
                              rendered="#{sbb.masterOwnerNo ne sbb.subBrokerNo}"/>
                <h:outputText value="#{sbb.city}, #{sbb.province}" styleClass="city-boldFont province-#{rowIndex}"
                              rendered="#{sbb.masterOwnerNo eq sbb.subBrokerNo}"/>
            </p:column>

            <p:column>
                <f:facet name="header">
                    <h:panelGrid>
                        <h:panelGroup>
                            <h:outputText styleClass="boldFont" value="#{msg_subbrokers['table.action']}"/>
                        </h:panelGroup>
                    </h:panelGrid>
                </f:facet>

                <p:commandLink id="modifySubBroker" value="#{msg_subbrokers['modify']}"
                               action="#{subBrokersController.modifyPage}" update="mainTab:brokerPanel"
                               styleClass="link_modifySubBroker#{sbb.id} modifySubBroker-#{rowIndex}">
                    <f:setPropertyActionListener target="#{subBrokersController.selectedId}" value="#{sbb.id}"/>
                    <f:param name="actionParam" value="#{sbb.id}"/>
                </p:commandLink>
            </p:column>

            <ui:include src="/pages/common/scrollerbottom.xhtml"/>

        </p:dataTable>

    </h:form>

</ui:composition>

