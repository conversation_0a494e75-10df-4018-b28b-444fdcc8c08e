<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                >

    <p:panel styleClass="modify-sale-block sub-block-line">
        <h:outputText styleClass="formRow-title" value="#{msg_sbb['application.'.concat(currentLob).concat('.').concat(currentApp)]}"/>
    <p:panelGrid columns="3" cellspacing="0" columnClasses="td1,td2,td3">
        <h:panelGroup>
            <p:panelGrid columns="2" styleClass="errorGrid" cellspacing="0" cellpadding="0">
                <h:outputText value="#{msg_sbb['form.program.access']}"/>
            </p:panelGrid>
        </h:panelGroup>
        <h:graphicImage url="/image/question_delimiter_arrow.gif"/>
        <h:panelGroup>
            <h:panelGroup>
                <p:panelGrid columns="2" styleClass="errorGrid" cellspacing="0" cellpadding="0">
                    <h:selectOneRadio
                            value="#{subBrokersController.subBrokerBean.linesOfBusiness[currentLob].applications[currentApp].accessType}"
                            styleClass="input_webAccessType">
                        <f:selectItems value="#{subBrokersController.webAccessTypeList}" var="accessType"
                                       itemValue="#{accessType.value}" itemLabel="#{msg_sbb[accessType.value]}"/>
                    </h:selectOneRadio>
                    <h:outputText styleClass="errorMessage" escape="false"
                                  value="#{subBrokersController.showError('accessType'.concat(currentApp).concat(currentLob))}"/>
                </p:panelGrid>
            </h:panelGroup>
        </h:panelGroup>

        <h:panelGroup>
            <p:panelGrid columns="2" styleClass="errorGrid" cellspacing="0" cellpadding="0">
                <h:outputText value=" "/>
                <h:outputText value="#{msg_sbb['form.assignable']}"/>
            </p:panelGrid>
        </h:panelGroup>
        <h:graphicImage url="/image/question_delimiter_arrow.gif"/>
        <h:panelGroup>
            <p:panelGrid columns="4" cellspacing="2" cellpadding="2">
                <h:selectOneRadio
                        value="#{subBrokersController.subBrokerBean.linesOfBusiness[currentLob].applications[currentApp].assignable}"
                        styleClass="radioBtn-table">
                    <f:selectItem itemLabel="#{msg_sbb['client.centre.yes']}" itemValue="true"/>
                    <f:selectItem itemLabel="#{msg_sbb['client.centre.no']}" itemValue="false"/>
                </h:selectOneRadio>
            </p:panelGrid>
        </h:panelGroup>
        <h:panelGroup></h:panelGroup>
    </p:panelGrid>
    </p:panel>
    <ui:repeat var="phone"
               value="#{subBrokersController.subBrokerBean.linesOfBusiness[currentLob].applications[currentApp].phones.values().toArray()}">
        <ui:include src="/pages/brokers/subbroker/phones.xhtml">
            <ui:param name="currentLob" value="#{currentLob}"></ui:param>
            <ui:param name="currentApp" value="#{currentApp}"></ui:param>
            <ui:param name="currentPhone" value="#{phone.usage}"></ui:param>
        </ui:include>
    </ui:repeat>

</ui:composition>
