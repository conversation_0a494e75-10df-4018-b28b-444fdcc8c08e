<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                >

    <p:panelGrid styleClass="modify-sale-block" columns="3" cellspacing="0" columnClasses="td1,td2,td3">

        <h:outputText value="#{msg_sbb['form.phone.pos.'.concat(currentPhone)]}"/>

        <h:graphicImage url="/image/question_delimiter_arrow.gif"/>
        <h:panelGroup styleClass="inputBundleGroup">
            <p:panelGrid columns="1" styleClass="errorGrid" cellspacing="0" cellpadding="0">
                <h:panelGroup>
                    <h:inputText id="phoneNumber"
                                 value="#{subBrokersController.subBrokerBean.linesOfBusiness[currentLob].applications[currentApp].phones[currentPhone].phone}"
                                 size="14" maxlength="14"
                                 styleClass="input_phoneNumberPhoneNumber masked"/>

                    <p:spacer width="5px"/>
                    <h:selectBooleanCheckbox id="phoneNumberTollFreeInd"
                                             value="#{subBrokersController.subBrokerBean.linesOfBusiness[currentLob].applications[currentApp].phones[currentPhone].tollFree}"
                                             styleClass="input_brokerSource"/><h:outputText
                        value="#{msg_sbb['toll.free']}"/>
                    <h:outputText styleClass="errorMessage" escape="false"
                                  value="#{subBrokersController.showError('phone_P_WQ_'.concat(currentPhone))}"/>
                </h:panelGroup>
            </p:panelGrid>
        </h:panelGroup>
        <h:panelGroup></h:panelGroup>
    </p:panelGrid>

</ui:composition>
