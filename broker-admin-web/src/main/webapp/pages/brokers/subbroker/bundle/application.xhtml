<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                >

    <p:panel styleClass="modify-sale-block sub-block-line">
       <h:outputText styleClass="formRow-title" value="#{msg_sbb['application.'.concat('P').concat('.').concat('BQ')]}"/>
        <p:panelGrid  columns="3" cellspacing="0" columnClasses="td1,td2,td3">
            <h:panelGroup>
                <p:panelGrid columns="2" styleClass="section-hide" cellspacing="0" cellpadding="0">
                    <h:outputText value="#{msg_sbb['form.program.access']}"/>
                </p:panelGrid>
            </h:panelGroup>
            <h:graphicImage url="/image/question_delimiter_arrow.gif"/>
            <h:panelGroup>
                <h:panelGroup>
                    <p:panelGrid columns="2" styleClass="section-hide" cellspacing="0" cellpadding="0">
                        <h:selectOneRadio
                                value="#{subBrokersController.subBrokerBean.linesOfBusiness[currentLob].applications[currentApp].accessType}"
                                styleClass="input_webAccessType" disabled="true">
                            <f:selectItems value="#{subBrokersController.webAccessTypeList}"
                                           var="accessType" itemValue="#{accessType.value}"
                                           itemLabel="#{msg_sbb[accessType.value]}"/>
                        </h:selectOneRadio>
                        <h:outputText styleClass="errorMessage" escape="false"
                                      value="#{subBrokersController.showError('accessType'.concat(currentApp).concat(currentLob))}"/>
                    </p:panelGrid>
                </h:panelGroup>
            </h:panelGroup>
        </p:panelGrid>
    </p:panel>

    <p:panel styleClass="phone-bundle">
        <ui:repeat var="phone"
                   value="#{subBrokersController.subBrokerBean.linesOfBusiness['P'].applications['WQ'].phones.values().toArray()}">
            <ui:include src="/pages/brokers/subbroker/bundle/phones.xhtml">
                <ui:param name="currentLob" value="#{'P'}"></ui:param>
                <ui:param name="currentApp" value="#{'WQ'}"></ui:param>
                <ui:param name="currentPhone" value="#{phone.usage}"></ui:param>
            </ui:include>
        </ui:repeat>
    </p:panel>

</ui:composition>
