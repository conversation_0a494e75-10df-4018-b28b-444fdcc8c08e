<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                >

    <script src="javascript/submitSubBroker.js"/>
    <script src="javascript/mask.js"/>

    <h:form id="subBrokerForm" enctype="multipart/form-data">
        <h:inputHidden id="AntiCSRFToken" value="#{subBrokersTabController.tokenCSRF}"/>
        <h:inputHidden id="currentlocale" value="#{subBrokersController.language}"/>
        <h:inputHidden id="updateMode" value="#{subBrokersController.updateMode}"/>
        <f:loadBundle var="msg_sbb" basename="com.intact.brokeroffice.controller.subbrokers.subbrokers"/>
        <f:loadBundle var="global" basename="com.intact.brokeroffice.resources.global"/>

        <p:commandLink value="#{msg_sbb['title.return.link.subbrokers']}"
                       action="#{subBrokersController.listPage}"
                       update="mainTab:brokerPanel"
                       styleClass="link_returnToPointOfSaleList"/>

        <p:panel>
            <p:panelGrid id="pnlGrid24" columns="1" styleClass="subbroker-form form" cellspacing="0" cellpadding="0">

                <h:panelGroup>
                    <h:outputText styleClass="sub-title" value="#{msg_sbb['title.modify_subbroker']}"/>
                    <p:panelGrid id="pnlGrid28" styleClass="modify-sale-block top-padding15 point-of-sale-wrap" columns="3" cellspacing="0">
                        <h:outputText value="#{msg_sbb['form.subbrokerno']}"/>
                        <h:graphicImage url="/image/question_delimiter_arrow.gif"/>
                        <p:panelGrid id="pnlGrid31" styleClass="left-margin10" columns="1" cellspacing="0" cellpadding="0" >
                            <h:outputText value="#{subBrokersController.subBrokerBean.subBrokerNo}" styleClass="label_subBrokerNo"/>
                        </p:panelGrid>

                        <h:outputText value="#{msg_sbb['form.name']}" />
                        <h:graphicImage url="/image/question_delimiter_arrow.gif"/>
                        <h:panelGroup styleClass="left-margin10 displayBlock">
                            <p:panelGrid id="pnlGrid38" columns="1" cellspacing="0" cellpadding="0" >
                                <h:outputText value="#{subBrokersController.subBrokerBean.name}" styleClass="label_subBrokerName"/>
                            </p:panelGrid>
                        </h:panelGroup>

                        <h:outputText value="#{msg_sbb['form.online.name']}" />
                        <h:graphicImage url="/image/question_delimiter_arrow.gif"/>
                        <h:panelGroup styleClass="left-margin10 displayBlock">
                            <p:panelGrid id="pnlGrid46" columns="1" cellspacing="0" cellpadding="0" >
                                <h:inputText id="onlineName" value="#{subBrokersController.subBrokerBean.onlineName}" size="50" styleClass="input_onlineName"/>
                                <p:messages styleClass="errorMessage"
                                            showDetail="true" for="subBrokerForm:onlineName" />
                            </p:panelGrid>
                        </h:panelGroup>

                        <h:outputText value="#{msg_sbb['form.brokerage.image.french']}" />
                        <h:graphicImage url="/image/question_delimiter_arrow.gif"/>
                        <p:panelGrid id="pnlGrid55" columns="1" styleClass="point-modify-group" cellspacing="0" cellpadding="0" >
                            <h:panelGroup id="panel_LOGO-FR-UI" styleClass="nowrap">
                                <p:fileUpload id="image_LOGO-FR--UI"
                                              value="#{subBrokersController.subBrokerBean.images['LOGO-FR--UI'].image}"
                                              mode="simple"
                                              styleClass="input_frenchBrokerageImage"/>

                                <p:graphicImage style="width:226px; height:46px;"
                                                value="#{subBrokersController.subBrokerBean.images['LOGO-FR&#45;&#45;UI'].imagePreviewAsStream}"
                                                cache="false"
                                                rendered="#{subBrokersController.isDisplayable(subBrokersController.subBrokerBean.images['LOGO-FR&#45;&#45;UI'])}"/>

                                <p:commandButton id="deleteFrenchBrokerageImage" value="#{global['button.delete']}" styleClass="deleteBtn"
                                                 action="#{subBrokersController.clear('LOGO-FR&#45;&#45;UI')}"
                                                 update="panel_LOGO-FR-UI"
                                                 rendered="#{subBrokersController.isDisplayable(subBrokersController.subBrokerBean.images['LOGO-FR&#45;&#45;UI'])}" />
                            </h:panelGroup>
                            <p:messages styleClass="errorMessage"
                                        showDetail="true"
                                        for="subBrokerForm:image_LOGO-FR--UI">
                                <p:autoUpdate />
                            </p:messages>
                        </p:panelGrid>

                        <h:outputText value="#{msg_sbb['form.brokerage.image.english']}" />
                        <h:graphicImage url="/image/question_delimiter_arrow.gif"/>
                        <p:panelGrid id="pnlGrid81" columns="1" styleClass="point-modify-group" cellspacing="0" cellpadding="0" >
                            <h:panelGroup id="panel_LOGO-EN-UI" styleClass="nowrap">
                                <p:fileUpload id="image_LOGO-EN--UI"
                                              value="#{subBrokersController.subBrokerBean.images['LOGO-EN--UI'].image}"
                                              mode="simple"
                                              styleClass="input_englishBrokerageImage" />

                                <p:graphicImage style="width:226px; height:46px;"
                                                value="#{subBrokersController.subBrokerBean.images['LOGO-EN&#45;&#45;UI'].imagePreviewAsStream}"
                                                cache="false"
                                                rendered="#{subBrokersController.isDisplayable(subBrokersController.subBrokerBean.images['LOGO-EN&#45;&#45;UI'])}"/>

                                <p:commandButton id="deleteEnglishBrokerageImage" value="#{global['button.delete']}" styleClass="deleteBtn"
                                                 action="#{subBrokersController.clear('LOGO-EN--UI')}"
                                                 update="panel_LOGO-EN-UI"
                                                 rendered="#{subBrokersController.isDisplayable(subBrokersController.subBrokerBean.images['LOGO-EN--UI'])}" />
                            </h:panelGroup>
                            <p:messages styleClass="errorMessage"
                                        showDetail="true"
                                        for="subBrokerForm:image_LOGO-EN--UI">
                                <p:autoUpdate />
                            </p:messages>
                        </p:panelGrid>

                        <h:outputText value="#{msg_sbb['form.brokerage.image.svg.french']}" />
                        <h:graphicImage url="/image/question_delimiter_arrow.gif"/>
                        <p:panelGrid id="pnlGrid107" columns="1" styleClass="point-modify-group" cellspacing="0" cellpadding="0" >
                            <h:panelGroup  id="panel_LOGO-FR-SVG-UI" styleClass="nowrap">
                                <p:fileUpload id="image_LOGO-FR-SVG-UI"
                                              value="#{subBrokersController.subBrokerBean.images['LOGO-FR-SVG-UI'].image}"
                                              mode="simple"
                                              styleClass="input_frenchBrokerageImage" />

                                <p:graphicImage style="width:226px; height:46px;"
                                                value="#{subBrokersController.subBrokerBean.images['LOGO-FR-SVG-UI'].imagePreviewAsStream}"
                                                cache="false"
                                                rendered="#{subBrokersController.isDisplayable(subBrokersController.subBrokerBean.images['LOGO-FR-SVG-UI'])}"/>

                                <p:commandButton id="deleteFrenchBrokerageImageSVG" value="#{global['button.delete']}" styleClass="deleteBtn"
                                                 action="#{subBrokersController.clear('LOGO-FR-SVG-UI')}"
                                                 update="panel_LOGO-FR-SVG-UI"
                                                 rendered="#{subBrokersController.isDisplayable(subBrokersController.subBrokerBean.images['LOGO-FR-SVG-UI'])}" />
                            </h:panelGroup>
                            <p:messages styleClass="errorMessage"
                                        showDetail="true"
                                        for="subBrokerForm:image_LOGO-FR-SVG-UI">
                                <p:autoUpdate />
                            </p:messages>
                        </p:panelGrid>

                        <h:outputText value="#{msg_sbb['form.brokerage.image.svg.english']}" />
                        <h:graphicImage url="/image/question_delimiter_arrow.gif"/>
                        <p:panelGrid id="pnlGrid133" columns="1" styleClass="point-modify-group" cellspacing="0" cellpadding="0" >
                            <h:panelGroup  id="panel_LOGO-EN-SVG-UI" styleClass="nowrap">
                                <p:fileUpload id="image_LOGO-EN-SVG-UI"
                                              value="#{subBrokersController.subBrokerBean.images['LOGO-EN-SVG-UI'].image}"
                                              mode="simple"
                                              styleClass="input_englishBrokerageImage" />

                                <p:graphicImage style="width:226px; height:46px;"
                                                value="#{subBrokersController.subBrokerBean.images['LOGO-EN-SVG-UI'].imagePreviewAsStream}"
                                                cache="false"
                                                rendered="#{subBrokersController.isDisplayable(subBrokersController.subBrokerBean.images['LOGO-EN-SVG-UI'])}"/>

                                <p:commandButton id="deleteEnglishBrokerageImageSVG" value="#{global['button.delete']}" styleClass="deleteBtn"
                                                 action="#{subBrokersController.clear('LOGO-EN-SVG-UI')}"
                                                 update="panel_LOGO-EN-SVG-UI"
                                                 rendered="#{subBrokersController.isDisplayable(subBrokersController.subBrokerBean.images['LOGO-EN-SVG-UI'])}" />
                            </h:panelGroup>
                            <p:messages styleClass="errorMessage"
                                        showDetail="true"
                                        for="subBrokerForm:image_LOGO-EN-SVG-UI">
                                <p:autoUpdate />
                            </p:messages>
                        </p:panelGrid>

                        <h:outputText value="#{msg_sbb['form.brokerage.image.printing.french']}"   />
                        <h:graphicImage url="/image/question_delimiter_arrow.gif"  />
                        <p:panelGrid id="pnlGrid159" columns="1" styleClass="point-modify-group" cellspacing="0" cellpadding="0"  >
                            <h:panelGroup id="panel_LOGO-FR-PR" styleClass="nowrap">
                                <p:fileUpload id="image_LOGO-FR--PR"
                                              value="#{subBrokersController.subBrokerBean.images['LOGO-FR--PR'].image}"
                                              mode="simple"
                                              styleClass="input_frenchBrokerageImage" />

                                <p:graphicImage style="width:226px; height:46px;"
                                                value="#{subBrokersController.subBrokerBean.images['LOGO-FR&#45;&#45;PR'].imagePreviewAsStream}"
                                                cache="false"
                                                rendered="#{subBrokersController.isDisplayable(subBrokersController.subBrokerBean.images['LOGO-FR&#45;&#45;PR'])}"/>
                                <p:commandButton id="deleteFrenchPrintingImage" value="#{global['button.delete']}" styleClass="deleteBtn"
                                                 action="#{subBrokersController.clear('LOGO-FR--PR')}"
                                                 update="panel_LOGO-FR-PR"
                                                 rendered="#{subBrokersController.isDisplayable(subBrokersController.subBrokerBean.images['LOGO-FR--PR'])}" />
                            </h:panelGroup>
                            <p:messages styleClass="errorMessage"
                                        showDetail="true"
                                        for="subBrokerForm:image_LOGO-FR--PR">
                                <p:autoUpdate />
                            </p:messages>
                        </p:panelGrid>

                        <h:outputText value="#{msg_sbb['form.brokerage.image.printing.english']}" />
                        <h:graphicImage url="/image/question_delimiter_arrow.gif"  />
                        <p:panelGrid id="pnlGrid184" columns="1" styleClass="point-modify-group" cellspacing="0" cellpadding="0"  >
                            <h:panelGroup id="panel_LOGO-EN-PR" styleClass="nowrap">
                                <p:fileUpload id="image_LOGO-EN--PR"
                                              value="#{subBrokersController.subBrokerBean.images['LOGO-EN--PR'].image}"
                                              mode="simple"
                                              styleClass="input_englishBrokerageImage" />

                                <p:graphicImage style="width:226px; height:46px;"
                                                value="#{subBrokersController.subBrokerBean.images['LOGO-EN&#45;&#45;PR'].imagePreviewAsStream}"
                                                cache="false"
                                                rendered="#{subBrokersController.isDisplayable(subBrokersController.subBrokerBean.images['LOGO-EN&#45;&#45;PR'])}"/>

                                <p:commandButton id="deleteEnglishPrintingImage" value="#{global['button.delete']}" styleClass="deleteBtn"
                                                 action="#{subBrokersController.clear('LOGO-EN--PR')}"
                                                 update="panel_LOGO-EN-PR"
                                                 rendered="#{subBrokersController.isDisplayable(subBrokersController.subBrokerBean.images['LOGO-EN--PR'])}" />
                            </h:panelGroup>
                            <p:messages styleClass="errorMessage"
                                        showDetail="true"
                                        for="subBrokerForm:image_LOGO-EN--PR">
                                <p:autoUpdate />
                            </p:messages>
                        </p:panelGrid>

                        <h:outputText value="#{msg_sbb['form.address']}" />
                        <h:graphicImage url="/image/question_delimiter_arrow.gif"/>
                        <h:panelGroup>
                            <p:panelGrid id="pnlGrid211" columns="1" styleClass="point-modify-group" cellspacing="0" cellpadding="0" >
                                <h:outputText value="#{subBrokersController.subBrokerBean.address1}" styleClass="label_subBrokerAdress1"/>
                                <h:outputText value="#{subBrokersController.subBrokerBean.address2}" styleClass="label_subBrokerAdress2"/>
                            </p:panelGrid>
                        </h:panelGroup>

                        <h:outputText value="#{msg_sbb['form.suppress.address']}" />
                        <h:graphicImage url="/image/question_delimiter_arrow.gif"/>
                        <h:panelGroup styleClass="point-modify-group displayBlock">
                            <h:selectBooleanCheckbox id="brokerSource" value="#{subBrokersController.subBrokerBean.displayBrokerAddress}" styleClass="input_brokerSource" />
                            <p:spacer width="5px" /><h:outputText value="#{msg_sbb['WEBBK']}" /><p:spacer width="70px" />
                            <h:selectBooleanCheckbox id="intactSource" value="#{subBrokersController.subBrokerBean.displayIntactAddress}" styleClass="input_quoteIntactSource" />
                            <p:spacer width="5px" /><h:outputText value="#{msg_sbb['INT']}" /><p:spacer width="70px" />
                        </h:panelGroup>

                        <h:outputText value="#{msg_sbb['form.url']}" />
                        <h:graphicImage url="/image/question_delimiter_arrow.gif"/>
                        <h:panelGroup>
                            <p:panelGrid id="pnlGrid229" columns="1" styleClass="point-modify-group" cellspacing="0" cellpadding="0" >
                                <h:outputText id="url" value="#{subBrokersController.subBrokerBean.url}" size="40" maxlength="60" styleClass="input_subBrokerUrl"/>
                                <p:messages styleClass="errorMessage"
                                            showDetail="true" for="subBrokerForm:url" />
                            </p:panelGrid>
                        </h:panelGroup>

                        <h:outputText value="#{msg_sbb['form.phone.number.broker.file']}" />
                        <h:graphicImage url="/image/question_delimiter_arrow.gif"/>
                        <h:panelGroup>
                            <p:panelGrid id="pnlGrid239" columns="1" styleClass="point-modify-group" cellspacing="0" cellpadding="0" >
                                <h:outputText value="#{subBrokersController.subBrokerBean.phoneNumber}" size="14"
                                              maxlength="14" styleClass="input_subBrokerPhoneNumberFile"/>
                                <p:messages styleClass="errorMessage" for="subBrokerForm:phoneNumber"/>
                            </p:panelGrid>
                        </h:panelGroup>

                        <h:outputText value="#{msg_sbb['form.client.scheduled.callback']}" />
                        <h:graphicImage url="/image/question_delimiter_arrow.gif"/>
                        <h:panelGroup>
                            <p:panelGrid id="pnlGrid249" columns="1" styleClass="point-modify-group" cellspacing="0" cellpadding="0" >
                                <h:selectOneRadio id="clientScheduledCallbackInd" value="#{subBrokersController.subBrokerBean.allowCallback}" styleClass="radioBtn-table">
                                    <f:selectItem id="clientScheduledCallbackInd_true" itemLabel="#{msg_sbb['client.scheduled.callback.yes']}" itemValue="true" />
                                    <f:selectItem id="clientScheduledCallbackInd_false" itemLabel="#{msg_sbb['client.scheduled.callback.no']}" itemValue="false" />
                                </h:selectOneRadio>
                                <p:messages styleClass="errorMessage"
                                            showDetail="true" for="subBrokerForm:clientScheduledCallbackInd" />
                            </p:panelGrid>
                        </h:panelGroup>
                    </p:panelGrid>
                    <ui:include src="/pages/brokers/subbroker/businessHours.xhtml" />
                </h:panelGroup>
                <h:inputHidden value="#{subBrokersController.subBrokerBean.id}" />
                <h:inputHidden value="#{subBrokersController.subBrokerBean.subBrokerNo}" />
                <ui:repeat var="lob" value="#{subBrokersController.subBrokerBean.linesOfBusiness.values().toArray()}" >
                    <ui:include src="/pages/brokers/subbroker/line.xhtml"><ui:param name="currentLob" value="#{lob.lineOfBusiness}"></ui:param></ui:include>
                </ui:repeat>

                <h:panelGroup>
                    <h:outputText styleClass="sub-title" value="#{msg_sbb['service']}"/>

                    <ui:repeat var="service" value="#{subBrokersController.subBrokerBean.services.values().toArray()}" >

                        <ui:include src="/pages/brokers/subbroker/service.xhtml">
                            <ui:param name="currentService" value="#{service.application.id}"></ui:param>
                        </ui:include>

                    </ui:repeat>

                </h:panelGroup>
            </p:panelGrid>

        </p:panel>

        <p:panel styleClass="btn-group modify-sale">
            <!--  PM9331: insure that image is always download to avoid buttons disappear temporarily  -->
            <p:panel style="display:none">
                <h:commandLink styleClass="actionBtnRightHover" >
                    <h:outputText value="invisible right" />
                </h:commandLink>

                <h:commandLink styleClass="actionBtnLeftHover">
                    <h:outputText value="invisible left" />
                </h:commandLink>
            </p:panel>

            <p:commandLink action="#{subBrokersController.listPage}"
                           styleClass="actionBtn"
                           update="mainTab:brokerPanel">
				<span class="left">
					<h:outputText value="#{global['form.button.cancel']}"/>
				</span>
            </p:commandLink>

            <p:panel styleClass="updateBtn-group">
                <!--  PM14367: ajax commandlink doesn't work properly with t:inputFileUpload, so I just remove the waiting behavior until a better solution is found. -->
                <p:commandButton id="linkAll" styleClass="actionBtn btnArrow"
                                 value="#{global['form.button.updateAll']}"
                                 ajax="false"
                                 onclick="return confirmUpdateAll()"
                                 action="#{subBrokersController.modifySubBrokersInMultiUpdateMode()}"
                                 update="mainTab:brokerPanel"/>

                <p:commandButton id="link"  styleClass="buttonUpdate actionBtn btnArrow"
                                 value="#{global['form.button.update']}"
                                 action="#{subBrokersController.modifySubBroker}"
                                 ajax="false"
                                 onclick="return handleUpdate(document.getElementById('mainTab:brokerPanel:subBrokerForm:currentlocale'))"
                                 update="mainTab:brokerPanel"/>
            </p:panel>
        </p:panel>

        <h:outputText escape="false">
            <script>//<![CDATA[

            function confirmUpdateAll(){
                return handleAllPointsofSales(document.getElementById('mainTab:brokerPanel:subBrokerForm:currentlocale'), '#{subBrokersController.changes}');
            }

            function doReady(updateMode) {
                try {
                    jQuery('.masked').mask('(*************');
                } catch (err) {
                    console.log(err);
                }

                try {
                    if ('MULTI' === updateMode) {
                        if (handleAllPointsofSales(document.getElementById('mainTab:brokerPanel:subBrokerForm:currentlocale'), '#{subBrokersController.changes}')) {
                            document.getElementById('mainTab:brokerPanel:subBrokerForm:btnSaveAll').click();
                        }
                    }
                } catch (err) {
                    console.log(err);
                }
            };

            //]]>
            </script>
        </h:outputText>

    </h:form>

</ui:composition>

