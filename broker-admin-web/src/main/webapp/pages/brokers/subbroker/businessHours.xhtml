<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                >
    <p:panelGrid id="pnlGridBH9" columns="12" styleClass="subbroker-form modify-sale-block hourTable" cellspacing="0" width="100%">
        <h:panelGroup>
            <h:outputText styleClass="ui-panelgrid" value="#{msg_sbb['form.business.hour']}"/>
            <h:outputText styleClass="ui-panelgrid" value="#{msg_sbb['form.sunday']}"/>
            <h:outputText styleClass="ui-panelgrid" value="#{msg_sbb['form.monday']}"/>
            <h:outputText styleClass="ui-panelgrid" value="#{msg_sbb['form.tuesday']}"/>
            <h:outputText styleClass="ui-panelgrid" value="#{msg_sbb['form.wednesday']}"/>
            <h:outputText styleClass="ui-panelgrid" value="#{msg_sbb['form.thursday']}"/>
            <h:outputText styleClass="ui-panelgrid" value="#{msg_sbb['form.friday']}"/>
            <h:outputText styleClass="ui-panelgrid" value="#{msg_sbb['form.saturday']}"/>
        </h:panelGroup>

        <h:panelGroup>
            <h:outputText styleClass="ui-panelgrid" value="#{msg_sbb['form.start.hour']}"/>
            <p:panelGrid columns="1" styleClass="errorGrid" cellspacing="0" cellpadding="0">
                <h:selectOneMenu styleClass="input_hoursSundayStart errorGrid" id="sundayStart"
                                 value="#{subBrokersController.subBrokerBean.businessHours['7'].openHour}">
                    <f:selectItems value="#{subBrokersController.businessHoursChoice}"/>
                </h:selectOneMenu>
            </p:panelGrid>
            <p:panelGrid columns="1" styleClass="input_hoursMondayStart errorGrid" cellspacing="0" cellpadding="0">
                <h:selectOneMenu styleClass="errorGrid" id="mondayStart"
                                 value="#{subBrokersController.subBrokerBean.businessHours['1'].openHour}">
                    <f:selectItems value="#{subBrokersController.businessHoursChoice}"/>
                </h:selectOneMenu>
            </p:panelGrid>
            <p:panelGrid columns="1" styleClass="input_hoursTuesdayStart errorGrid" cellspacing="0" cellpadding="0">
                <h:selectOneMenu styleClass="errorGrid" id="tuesdayStart"
                                 value="#{subBrokersController.subBrokerBean.businessHours['2'].openHour}">
                    <f:selectItems value="#{subBrokersController.businessHoursChoice}"/>
                </h:selectOneMenu>
            </p:panelGrid>
            <p:panelGrid columns="1" styleClass="input_hoursWednesdayStart errorGrid" cellspacing="0" cellpadding="0">
                <h:selectOneMenu styleClass="errorGrid" id="wednesdayStart"
                                 value="#{subBrokersController.subBrokerBean.businessHours['3'].openHour}">
                    <f:selectItems value="#{subBrokersController.businessHoursChoice}"/>
                </h:selectOneMenu>
            </p:panelGrid>
            <p:panelGrid columns="1" styleClass="input_hoursThursdayStart errorGrid" cellspacing="0" cellpadding="0">
                <h:selectOneMenu styleClass="errorGrid" id="thursdayStart"
                                 value="#{subBrokersController.subBrokerBean.businessHours['4'].openHour}">
                    <f:selectItems value="#{subBrokersController.businessHoursChoice}"/>
                </h:selectOneMenu>
            </p:panelGrid>
            <p:panelGrid columns="1" styleClass="input_hoursFridayStart errorGrid" cellspacing="0" cellpadding="0">
                <h:selectOneMenu styleClass="errorGrid" id="fridayStart"
                                 value="#{subBrokersController.subBrokerBean.businessHours['5'].openHour}">
                    <f:selectItems value="#{subBrokersController.businessHoursChoice}"/>
                </h:selectOneMenu>
            </p:panelGrid>
            <p:panelGrid columns="1" styleClass="input_hoursSaturdayStart errorGrid" cellspacing="0" cellpadding="0">
                <h:selectOneMenu styleClass="errorGrid" id="saturdayStart"
                                 value="#{subBrokersController.subBrokerBean.businessHours['6'].openHour}">
                    <f:selectItems value="#{subBrokersController.businessHoursChoice}"/>
                </h:selectOneMenu>
            </p:panelGrid>
        </h:panelGroup>

        <h:panelGroup>
            <h:outputText styleClass="ui-panelgrid" value="#{msg_sbb['form.end.hour']}"/>
            <p:panelGrid columns="1" styleClass="errorGrid" cellspacing="0" cellpadding="0">
                <h:selectOneMenu styleClass="input_hoursSundayEnd errorGrid" id="sundayEnd"
                                 value="#{subBrokersController.subBrokerBean.businessHours['7'].closeHour}">
                    <f:selectItems value="#{subBrokersController.businessHoursChoice}"/>
                </h:selectOneMenu>
            </p:panelGrid>
            <p:panelGrid columns="1" styleClass="errorGrid" cellspacing="0" cellpadding="0">
                <h:selectOneMenu styleClass="input_hoursMondayEnd errorGrid" id="mondayEnd"
                                 value="#{subBrokersController.subBrokerBean.businessHours['1'].closeHour}">
                    <f:selectItems value="#{subBrokersController.businessHoursChoice}"/>
                </h:selectOneMenu>
            </p:panelGrid>
            <p:panelGrid columns="1" styleClass="errorGrid" cellspacing="0" cellpadding="0">
                <h:selectOneMenu styleClass="input_hoursTuesdayEnd errorGrid" id="tuesdayEnd"
                                 value="#{subBrokersController.subBrokerBean.businessHours['2'].closeHour}">
                    <f:selectItems value="#{subBrokersController.businessHoursChoice}"/>
                </h:selectOneMenu>
            </p:panelGrid>
            <p:panelGrid columns="1" styleClass="errorGrid" cellspacing="0" cellpadding="0">
                <h:selectOneMenu styleClass="input_hoursWednesdayEnd errorGrid" id="wednesdayEnd"
                                 value="#{subBrokersController.subBrokerBean.businessHours['3'].closeHour}">
                    <f:selectItems value="#{subBrokersController.businessHoursChoice}"/>
                </h:selectOneMenu>
            </p:panelGrid>
            <p:panelGrid columns="1" styleClass="errorGrid" cellspacing="0" cellpadding="0">
                <h:selectOneMenu styleClass="input_hoursThursdayEnd errorGrid" id="thursdayEnd"
                                 value="#{subBrokersController.subBrokerBean.businessHours['4'].closeHour}">
                    <f:selectItems value="#{subBrokersController.businessHoursChoice}"/>
                </h:selectOneMenu>
            </p:panelGrid>
            <p:panelGrid columns="1" styleClass="errorGrid" cellspacing="0" cellpadding="0">
                <h:selectOneMenu styleClass="input_hoursFridayEnd errorGrid" id="fridayEnd"
                                 value="#{subBrokersController.subBrokerBean.businessHours['5'].closeHour}">
                    <f:selectItems value="#{subBrokersController.businessHoursChoice}"/>
                </h:selectOneMenu>
            </p:panelGrid>
            <p:panelGrid columns="1" styleClass="input_hoursSaturdayEnd errorGrid" cellspacing="0" cellpadding="0">
                <h:selectOneMenu styleClass="errorGrid" id="saturdayEnd"
                                 value="#{subBrokersController.subBrokerBean.businessHours['6'].closeHour}">
                    <f:selectItems value="#{subBrokersController.businessHoursChoice}"/>
                </h:selectOneMenu>
            </p:panelGrid>
        </h:panelGroup>
    </p:panelGrid>
</ui:composition>
