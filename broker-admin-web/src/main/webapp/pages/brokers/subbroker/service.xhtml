<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                >


   <p:panel styleClass="modify-sale-block sub-block-line">
       <h:outputText styleClass="formRow-title" value="#{msg_sbb['service.'.concat(currentService)]}"/>
    <p:panelGrid columns="2" cellspacing="0" cellpadding="0">
        <h:panelGroup styleClass="service-block">
            <p:panelGrid columns="2" cellspacing="0" cellpadding="0">

                <h:panelGroup>
                    <h:outputText value="#{msg_sbb['client.centre.access']}"/>
                </h:panelGroup>
                <h:panelGroup>
                    <h:selectOneRadio id="clientCentreWebAccessType"
                                      onchange="document.getElementById('subBrokerForm:allowClientNotificationInd').disabled = this.[0].checked; "
                                      value="#{subBrokersController.subBrokerBean.services[currentService].application.accessType}"
                                      styleClass="input_clientCentreAccessInd">
                        <f:selectItem id="clientCentreWebAccessType_true"
                                      itemLabel="#{msg_sbb['client.centre.yes']}" itemValue="true"/>
                        <f:selectItem id="clientCentreWebAccessType_false"
                                      itemLabel="#{msg_sbb['client.centre.no']}" itemValue="false"/>
                        <p:ajax event="click" render="mainTab:brokerPanel:subBrokerForm"/>
                    </h:selectOneRadio>
                    <p:messages styleClass="errorMessage" for="clientCentreWebAccessType"/>
                </h:panelGroup>

                <h:panelGroup>
                    <h:outputText
                            value="#{msg_sbb['form.business.allow.client.notification.ind']}"/>
                </h:panelGroup>
                <h:panelGroup>
                    <h:selectOneRadio disabledClass="disabledAllowClientNotificationInd"
                                      disabled="#{subBrokersController.subBrokerBean.services[currentService].application.accessType == false}"
                                      id="allowClientNotificationInd"
                                      value="#{subBrokersController.subBrokerBean.services[currentService].application.notifiable}"
                                      styleClass="input_allowClientNotificationInd">
                        <f:selectItem id="allowClientNotificationInd_true"
                                      itemLabel="#{msg_sbb['client.centre.yes']}" itemValue="true"/>
                        <f:selectItem id="allowClientNotificationInd_false"
                                      itemLabel="#{msg_sbb['client.centre.no']}" itemValue="false"/>
                    </h:selectOneRadio>
                    <p:messages styleClass="errorMessage" for="allowClientNotificationInd"/>
                </h:panelGroup>

                <h:panelGroup>
                    <h:outputText value="#{msg_sbb['form.business.online.name.display.ind']}"/>
                </h:panelGroup>
                <h:panelGroup>
                    <h:selectOneRadio id="brokerNameClientCentreDisplayInd"
                                      value="#{subBrokersController.subBrokerBean.services[currentService].application.assignable}"
                                      styleClass="input_brokerNameClientCentreDisplayInd">
                        <f:selectItem id="brokerNameClientCentreDisplayInd_true"
                                      itemLabel="#{msg_sbb['client.centre.yes']}" itemValue="true"/>
                        <f:selectItem id="brokerNameClientCentreDisplayInd_false"
                                      itemLabel="#{msg_sbb['client.centre.no']}" itemValue="false"/>
                    </h:selectOneRadio>
                    <p:messages styleClass="errorMessage" for="businessHoursClientCentreDisplayInd"/>
                </h:panelGroup>
            </p:panelGrid>
        </h:panelGroup>

        <h:panelGroup styleClass="service-block">
            <p:panelGrid columns="2" cellspacing="0" cellpadding="0">
                <h:panelGroup>
                    <h:outputText styleClass="word-wrap" value="#{msg_sbb['form.email']}"/>
                </h:panelGroup>
                <h:panelGroup styleClass="service-input-block">
                    <h:inputText id="ccEmail"
                                 value="#{subBrokersController.subBrokerBean.services[currentService].contact.emailAddress}"
                                 size="40" maxlength="73" styleClass="input_ccSubBrokerEmail"/>
                    <h:outputText styleClass="errorMessage" escape="false"
                                  value="#{subBrokersController.showError('service.email.'.concat(currentService))}"/>
                </h:panelGroup>

                <h:panelGroup>
                    <h:outputText styleClass="word-wrap" value="#{msg_sbb['form.chat']}"/>
                </h:panelGroup>
                <h:panelGroup styleClass="service-input-block">
                    <h:inputText id="ccChatId"
                                 value="#{subBrokersController.subBrokerBean.services[currentService].contact.chatId}"
                                 size="40" maxlength="50" styleClass="input_ccSubBrokerChatId"/>
                    <p:messages styleClass="errorMessage" for="ccChatId"/>
                </h:panelGroup>

                <h:panelGroup >
                    <h:outputText styleClass="word-wrap" value="#{msg_sbb['client.centre.phone.number']}"/>
                </h:panelGroup>
                <h:panelGroup styleClass="service-input-block">
                    <h:inputText id="ccPhoneNumber"
                                 value="#{subBrokersController.subBrokerBean.services[currentService].application.phones[''].intactPhone}"
                                 size="14" maxlength="14"
                                 styleClass="input_ccPhoneNumberId masked"/>
                    <h:selectBooleanCheckbox id="ccPhoneNumberTollFreeInd"
                                             value="#{subBrokersController.subBrokerBean.services[currentService].application.phones[''].intactTollFree}"
                                             styleClass="input_brokerSource"/>
                    <h:outputText value="#{msg_sbb['toll.free']}" />
                    <h:outputText styleClass="errorMessage" escape="false"
                                  value="#{subBrokersController.showError('service.phone.'.concat(currentService))}"/>
                </h:panelGroup>

            </p:panelGrid>
        </h:panelGroup>

    </p:panelGrid>
   </p:panel>
</ui:composition>


