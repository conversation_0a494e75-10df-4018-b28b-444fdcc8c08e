FROM docker-group.iad.ca.inet:8473/intact/ocp-tomcat11-jdk17-base:1@sha256:7a951bcb4cf3025a25f90722182edfe813271edeff39a776c6eb98b4827e3754

ARG LABEL_VERSION='1.0.0'
LABEL version=$LABEL_VERSION

ARG APPLICATION='broker-admin-app'
ENV APPLICATION=$APPLICATION \
    APP_BASE="/opt/${APPLICATION}" \
    JAVA_OPTS="-Xms256m -Xmx512m -Duser.timezone=America/Montreal" \
    # Environment variables declared in base image
    # See https://githubifc.iad.ca.inet/lab-se/ocp-tomcat9-jdk11-base/blob/main/Dockerfile
    CATALINA_WEBAPPS="/usr/local/tomcat/webapps" \
    CATALINA_BIN="/usr/local/tomcat/bin" \
    CATALINA_CONF="/usr/local/tomcat/conf"

#https://confluence.tooling.intactfc.cloud/pages/viewpage.action?spaceKey=ISM&title=Image+traceability
#https://confluence.tooling.intactfc.cloud/display/AIL/Acquisition+components+Ownership
LABEL OWNER_TEAM="SE_INTACT_LAB" \
      TRIBE_TEAM="acquisition" \
      SQUAD_TEAM="AEGIS"

# Switch to user "root" to be able to chmod files
USER root

# Expose porrt 8080 of this container
EXPOSE 8080

# Copy war to tomcat webapps folder
COPY broker-admin-web/target/BrokerAdm.war ${CATALINA_WEBAPPS}/

# Copy scripts into the container
# It overrides base image's entrypoint which is defined as "start-tomcat.sh" by default
COPY docker/bin/*.sh ${CATALINA_BIN}/

# Make sure all scripts are executable
RUN chmod a+x "${CATALINA_BIN}"/*.sh &&\
    mkdir -p "${CATALINA_BASE}"/extra-classpath

# Copy Tomcat specific config files into the container. All placeholders in CATALINA_CONF will be resolved using Vault
# secrets. See https://githubifc.iad.ca.inet/lab-se/ocp-tomcat9-jdk11-base/blob/main/bin/vault_replace.sh for more
# details.
COPY docker/conf/tomcat/* ${CATALINA_CONF}/

# Make sure all config files are read-writable
RUN chmod a+rw "${CATALINA_BASE}"/* "${CATALINA_CONF}"/*

# Use intermediate script to be able to resolve parameters and keep using ENTRYPOINT instruction in EXEC form
# See https://github.com/hadolint/hadolint/wiki/DL3025
RUN printf "#!/bin/bash \n exec %s/start-tomcat.sh \n" "${CATALINA_BIN}" > ./docker-entrypoint.sh \
    && chmod +x ./docker-entrypoint.sh

# Switch to any non-root user otherwise Prismacloud will file this as a compliance issue.
# When running the container locally, it will not work because "nobody" cannot deploy a WAR in tomcat. However, it will
# work on Openshift because there's an internal logic to ignore this user and run the container with a dedicated one
# created at deploy time.
USER nobody

ENTRYPOINT ["./docker-entrypoint.sh"]
