<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>intact.web.brokeroffice</groupId>
    <artifactId>broker-admin-app</artifactId>
    <version>3.0.4-SNAPSHOT</version>
  </parent>

  <artifactId>broker-admin-business</artifactId>
  <packaging>jar</packaging>

  <properties>
    <!-- Override parent config which is set to UTF-8 -->
    <project.build.sourceEncoding>ISO-8859-1</project.build.sourceEncoding>
  </properties>


  <dependencies>

    <!--                      -->
    <!--        SPRING        -->
    <!--                      -->

    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-core</artifactId>
    </dependency>

    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-context</artifactId>
    </dependency>

    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-beans</artifactId>
    </dependency>

    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-tx</artifactId>
    </dependency>

    <!--                      -->
    <!--      HIBERNATE       -->
    <!--                      -->

    <dependency>
      <groupId>org.hibernate</groupId>
      <artifactId>hibernate-core-jakarta</artifactId>
      <exclusions>
        <exclusion>
          <groupId>net.bytebuddy</groupId>
          <artifactId>byte-buddy</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>com.atomikos</groupId>
      <artifactId>transactions-jta</artifactId>
      <classifier>jakarta</classifier>
    </dependency>
    <dependency>
      <groupId>com.atomikos</groupId>
      <artifactId>transactions-jdbc</artifactId>
      <classifier>jakarta</classifier>
    </dependency>

    <dependency>
      <groupId>net.bytebuddy</groupId>
      <artifactId>byte-buddy</artifactId>
      <version>1.16.1</version>
    </dependency>

    <dependency>
      <groupId>com.oracle.ojdbc</groupId>
      <artifactId>ojdbc8</artifactId>
    </dependency>

    <!--                      -->
    <!--      LOGGING         -->
    <!--                      -->

    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>jul-to-slf4j</artifactId>
    </dependency>

    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-api</artifactId>
    </dependency>

    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-simple</artifactId>
    </dependency>

    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>log4j-over-slf4j</artifactId>
    </dependency>

    <dependency>
      <groupId>org.apache.logging.log4j</groupId>
      <artifactId>log4j-api</artifactId>
    </dependency>

    <dependency>
      <groupId>org.apache.logging.log4j</groupId>
      <artifactId>log4j-core</artifactId>
    </dependency>

    <!--                      -->
    <!--   JAVA DEPENDENCIES  -->
    <!--                      -->

    <dependency>
      <groupId>jakarta.inject</groupId>
      <artifactId>jakarta.inject-api</artifactId>
    </dependency>

    <dependency>
      <groupId>jakarta.annotation</groupId>
      <artifactId>jakarta.annotation-api</artifactId>
      <version>1.3.5</version>
    </dependency>


    <!--                       -->
    <!--  INTACT DEPENDENCIES  -->
    <!--                       -->

    <dependency>
      <groupId>intact.commons.service.cif</groupId>
      <artifactId>cif-service-api</artifactId>
      <exclusions>
        <exclusion>
          <groupId>log4j</groupId>
          <artifactId>log4j</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <!-- to have access to cif-services-beans.xml -->
      <groupId>intact.commons.service.cif</groupId>
      <artifactId>cif-services</artifactId>
      <exclusions>
        <exclusion>
          <groupId>log4j</groupId>
          <artifactId>log4j</artifactId>
        </exclusion>
        <exclusion>
          <artifactId>commons-collections</artifactId>
          <groupId>commons-collections</groupId>
        </exclusion>
        <exclusion>
          <artifactId>xercesImpl</artifactId>
          <groupId>xerces</groupId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>intact.commons.service.plp</groupId>
      <artifactId>plp-api</artifactId>
    </dependency>

    <dependency>
      <groupId>intact.commons.service.brm</groupId>
      <artifactId>brm-api</artifactId>
    </dependency>

    <dependency>
      <groupId>intact.commons.service.brm</groupId>
      <artifactId>brm-service</artifactId>
      <exclusions>
        <exclusion>
          <groupId>org.hibernate</groupId>
          <artifactId>hibernate-entitymanager</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.hibernate</groupId>
          <artifactId>hibernate-core</artifactId>
        </exclusion>
        <exclusion>
          <groupId>dom4j</groupId>
          <artifactId>dom4j</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>intact.tools</groupId>
      <artifactId>tools-api-ldap</artifactId>
      <exclusions>
        <exclusion>
          <artifactId>commons-collections</artifactId>
          <groupId>commons-collections</groupId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>intact.tools</groupId>
      <artifactId>tools-comparison-api</artifactId>
    </dependency>

    <dependency>
      <groupId>org.easymock</groupId>
      <artifactId>easymock</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>org.easymock</groupId>
      <artifactId>easymockclassextension</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-test</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-core</artifactId>
      <version>5.15.2</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.tomee</groupId>
      <artifactId>javaee-api</artifactId>
    </dependency>

    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-collections4</artifactId>
    </dependency>

  </dependencies>

  <build>
    <plugins>
      <plugin>
        <artifactId>maven-jar-plugin</artifactId>
        <configuration>
          <archive>
            <manifest>
              <addClasspath>false</addClasspath>
              <addDefaultImplementationEntries>true</addDefaultImplementationEntries>
            </manifest>
            <manifestEntries>
              <Build-Time>${maven.build.timestamp}</Build-Time>
            </manifestEntries>
          </archive>
        </configuration>
      </plugin>
    </plugins>
  </build>

</project>
