package com.intact.brokeroffice.business.fsa.impl;

import static org.easymock.EasyMock.createMock;
import junit.framework.JUnit4TestAdapter;

import org.junit.After;
import org.junit.Before;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit38.AbstractJUnit38SpringContextTests;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.transaction.annotation.Transactional;

import com.ing.canada.cif.service.ISubBrokersService;
import com.intact.brokeroffice.business.fsa.IFsaBusinessProcess;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = { "/applicationContextTest.xml" })
@Transactional
public class FsaBusinessProcessTest extends AbstractJUnit38SpringContextTests {

	@Autowired
	private IFsaBusinessProcess fsaBusinessProcess;
	private ISubBrokersService subBrokersService;

	@Override
	@Before
	public void setUp() {
		this.subBrokersService = createMock(ISubBrokersService.class);
		ReflectionTestUtils.setField(this.fsaBusinessProcess, "subBrokersService", this.subBrokersService,
				ISubBrokersService.class);
	}
	
	@Override
	@After
	public void tearDown() throws Exception {
		this.subBrokersService = null;
	}
	@Test
	public void testLoadFsaTableTxt() throws Exception {

		/*String filePathTxt = "../BrokerOfficeBusiness-test/intgSrc/com/intact/brokeroffice/business/fsa/impl/fsaAssignmentTemplate.txt";
		SubBrokers sub = new SubBrokers();
		sub.setSubBrokerNumber("3386");
		sub.setNameLine1("Intact");
		sub.setCompanyNumber("A");
		expect(this.subBrokersService.getSubBroker("3386", "A")).andReturn(sub).anyTimes();
		replay(this.subBrokersService);
		
		List<IBrokerLocators> result = this.fsaBusinessProcess.loadFsaTable(new FileInputStream(filePathTxt),
				getFileExtension(filePathTxt), "A");
		assertEquals(getFileExtension(filePathTxt), "txt");
		assertNotNull(result);
		assertEquals(18, result.size());
		
		*/
	}
	
	@Test
	public void testLoadFsaTableXls() throws Exception {

		/*String filePathExcel = "../BrokerOfficeBusiness-test/intgSrc/com/intact/brokeroffice/business/fsa/impl/fsa assignment template_2.xls";
		SubBrokers sub = new SubBrokers();
		sub.setSubBrokerNumber("2932");
		sub.setNameLine1("Intact");
		sub.setCompanyNumber("A");
		expect(this.subBrokersService.getSubBroker("2932", "A")).andReturn(sub).anyTimes();
		replay(this.subBrokersService);

		List<IBrokerLocators> result = this.fsaBusinessProcess.loadFsaTable(new FileInputStream(
				filePathExcel), getFileExtension(filePathExcel), "A");
		
		assertEquals(getFileExtension(filePathExcel), "xls");
		assertNotNull(result);
		assertEquals(21, result.size());
		*/
	}
	
	@Test
	public void testLoadFsaTableCsv() throws Exception {

		/*String filePathCsv = "../BrokerOfficeBusiness-test/intgSrc/com/intact/brokeroffice/business/fsa/impl/fsa assignment template_3.csv";
		SubBrokers sub = new SubBrokers();
		sub.setSubBrokerNumber("1653");
		sub.setNameLine1("Intact");
		sub.setCompanyNumber("A");
		expect(this.subBrokersService.getSubBroker("1653", "A")).andReturn(sub).anyTimes();
		replay(this.subBrokersService);

		List<IBrokerLocators> result = this.fsaBusinessProcess.loadFsaTable(new FileInputStream(
				filePathCsv), getFileExtension(filePathCsv), "A");

		assertEquals(getFileExtension(filePathCsv), "csv");
		assertNotNull(result);
		assertEquals(26, result.size());
		*/
	}
	
	private String getFileExtension(String aFileName) {
		int extDashIndex = aFileName.lastIndexOf(".");
		String ext = aFileName.substring(extDashIndex + 1, aFileName.length());
		return ext;
	}
	/**
	 * Utility method need to run this test case in Maven 1.
	 * 
	 * @return Test suite
	 */
	public static junit.framework.Test suite() {
		return new JUnit4TestAdapter(FsaBusinessProcessTest.class);
	}
	
}
