package com.intact.brokeroffice.business.report.subbrokerassignments.impl;

import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.List;

import junit.framework.JUnit4TestAdapter;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.UncategorizedSQLException;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit38.AbstractJUnit38SpringContextTests;
import com.ing.canada.plp.report.SubBrokerAssignmentInfo;
import com.intact.brokeroffice.business.report.subbrokerassignment.ISubBrokerAssignmentBusinessProcess;


@ContextConfiguration(locations = { "/applicationContextTestSrc.xml" })
public class SubBrokerAssignmentBusinessTest extends AbstractJUnit38SpringContextTests{
	
	@Autowired
	private ISubBrokerAssignmentBusinessProcess subBrokerAssignmentBusinessProcess;

	
	@Test
	public void testDatesAreMandatory()
	{
		/* --------------------------------------------------
		 * 				**** TEST LIMIT ***** 
		 * UpdateFrom and  UpdateTo are NULL (MANDATORY INPUT PARAM)
			
			t_last_update_from          VARCHAR2  	-->	null
			t_last_update_to            VARCHAR2  	-->	null			
		
		-----------------------------------------------------*/
	
		boolean thrown = true;
		
		try
		{
			this.subBrokerAssignmentBusinessProcess.getAllSubBrokerAssignmentsSP(null, null, null, null, null);			
		}
		
		catch(UncategorizedSQLException exp){
			thrown= false;
		}
		assertTrue(thrown);		
	}
	
	
	@Test
	public void testBrokerChangeReport()
	{
		/* --------------------------------------------------
		 * 				 
		 * UpdateFrom and UpdateTo not NULL
			
			t_last_update_from          VARCHAR2  	-->	today - 30 days
			t_last_update_to            VARCHAR2  	-->	today			
		
		-----------------------------------------------------*/
				
		GregorianCalendar gCalendar =  new GregorianCalendar();
		gCalendar.setTime(new Date());
		gCalendar.add(Calendar.MONTH, -1);				
			
		List<SubBrokerAssignmentInfo> infos = this.subBrokerAssignmentBusinessProcess.getAllSubBrokerAssignmentsSP(
				gCalendar.getTime(), new Date(), null, null, null);			
		assertNotNull(infos);		
	}
	
	public static junit.framework.Test suite() {
		return new JUnit4TestAdapter(SubBrokerAssignmentBusinessTest.class);
	}
}