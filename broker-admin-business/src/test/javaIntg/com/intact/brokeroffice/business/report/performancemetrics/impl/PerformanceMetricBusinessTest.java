package com.intact.brokeroffice.business.report.performancemetrics.impl;



import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.List;

import junit.framework.JUnit4TestAdapter;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.UncategorizedSQLException;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit38.AbstractJUnit38SpringContextTests;

import com.ing.canada.cif.domain.enums.CifCompanyEnum;
import com.ing.canada.plp.domain.enums.ManufacturerCompanyCodeEnum;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.ing.canada.plp.report.performancemetric.PerformanceMetricInfo;
import com.intact.brokeroffice.business.report.performancemetric.IPerformanceMetricBusinessProcess;

@ContextConfiguration(locations = { "/applicationContextTestSrc.xml" })
public class PerformanceMetricBusinessTest extends AbstractJUnit38SpringContextTests{
	
	@Autowired	
	private IPerformanceMetricBusinessProcess performanceMetricBusinessProcess;
	
	
	@Test
	public void testPerformanceMetric()
	{
		/* --------------------------------------------------
		 *  
		 * Recherche avec dateFrom & dateTo			
			date_from          VARCHAR2  	-->	2010/01/01
			date_to            VARCHAR2  	-->	current date
		
		-----------------------------------------------------*/
		
		Date aDateTo = new Date();
		
		GregorianCalendar calendarFrom = new GregorianCalendar();
		calendarFrom.set(Calendar.YEAR, 2010);
		calendarFrom.set(Calendar.MONTH, 0);
		calendarFrom.set(Calendar.DATE, 1);	
		
		List<PerformanceMetricInfo> items = this.performanceMetricBusinessProcess.getPerformanceMetricListSP(calendarFrom.getTime(), aDateTo, null,ProvinceCodeEnum.QUEBEC, CifCompanyEnum.INTACT_QC.getSubBrokerCompanyNumber(), ManufacturerCompanyCodeEnum.INTACT_QUEBEC_REGION);
		assertNotNull(items);	
	}
	
	@Test
	public void testMandatoryPeriodStart()
	{
		/* --------------------------------------------------
		 *  
		 * Recherche avec dateFrom & dateTo			
			date_from          VARCHAR2  	-->	null
			date_to            VARCHAR2  	-->	current date
		
		-----------------------------------------------------*/
				
		boolean thrown = false;
			
		try
		{
			this.performanceMetricBusinessProcess.getPerformanceMetricListSP(null, new Date(), null, ProvinceCodeEnum.QUEBEC, CifCompanyEnum.INTACT_QC.getSubBrokerCompanyNumber(), ManufacturerCompanyCodeEnum.INTACT_QUEBEC_REGION);						
		}
		
		catch(UncategorizedSQLException exp){
			thrown= true;
		}
		assertTrue(thrown);	
	}	
	
	@Test
	public void testMandatoryPeriodEnd()
	{
		/* --------------------------------------------------
		 *  
		 * Recherche avec dateFrom & dateTo			
			date_from          VARCHAR2  	-->	2010/01/01
			date_to            VARCHAR2  	-->	null
		
		-----------------------------------------------------*/
						
		GregorianCalendar calendarFrom = new GregorianCalendar();
		calendarFrom.set(Calendar.YEAR, 2010);
		calendarFrom.set(Calendar.MONTH, 0);
		calendarFrom.set(Calendar.DATE, 1);	
		
		boolean thrown = false;	
		
		try
		{
			this.performanceMetricBusinessProcess.getPerformanceMetricListSP(calendarFrom.getTime(), null,null, ProvinceCodeEnum.QUEBEC, CifCompanyEnum.INTACT_QC.getSubBrokerCompanyNumber(), ManufacturerCompanyCodeEnum.INTACT_QUEBEC_REGION);						
		}
		
		catch(UncategorizedSQLException exp){
			thrown= true;
		}
		assertTrue(thrown);	
	}	
	
	public static junit.framework.Test suite() {
		return new JUnit4TestAdapter(PerformanceMetricBusinessTest.class);
	}
}
