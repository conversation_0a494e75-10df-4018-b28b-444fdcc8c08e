package com.intact.brokeroffice.business.accounts.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.ArrayList;
import java.util.Enumeration;
import java.util.List;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.ldap.core.AttributesMapper;
import org.springframework.test.util.ReflectionTestUtils;

import com.ing.canada.cif.service.IBrokerService;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.intact.brokeroffice.business.accounts.impl.AccountsBusinessProcess.UserNameAttributeMapper;
import com.intact.brokeroffice.business.exception.BrokerServiceException;
import com.intact.canada.brm.domain.user.UserAccount;
import com.intact.canada.brm.exception.BRMServiceException;
import com.intact.canada.brm.service.IUserAccountService;
import com.intact.tools.ldap.util.LdapTemplate;

/**
 * Test for {@link AccountsBusinessProcess}
 * <AUTHOR>
 *
 */
public class AccountsBusinessProcessTest {
	
	private AccountsBusinessProcess accountBusinessProcess;
	
	private IUserAccountService userAccountService;
	
	private IBrokerService brokerService;
	
	private LdapTemplate ldapTemplate;
	
	private UserAccount userAccount;
	
	private Enumeration<String> memberListEnumeration;
	
	private Enumeration<String> getUserNamesByCNEnum;
	
	private List<Enumeration<String>> ldapMemberList;
	
	private List<Enumeration<String>> getUserNamesByCNList;
	
	private String ldapGroupBroker;

	private String ldapGroupBrokerReassign;

	private String ldapBaseDN;
	
	@SuppressWarnings("unchecked")
	@BeforeEach
	public void setUp() throws Exception {
		// Class under test
		accountBusinessProcess = new AccountsBusinessProcess();
		
		// Mocks
		userAccountService = mock(IUserAccountService.class);
		brokerService = mock(IBrokerService.class);
		ldapTemplate = mock(LdapTemplate.class);
		memberListEnumeration = mock(Enumeration.class);
		getUserNamesByCNEnum = mock(Enumeration.class);
		
		// UserAccount
		userAccount = new UserAccount();

		// Enumeration<String> List
		ldapMemberList = new ArrayList<Enumeration<String>>();
		getUserNamesByCNList = new ArrayList<Enumeration<String>>();
		
		ldapGroupBroker = "ldapGroupBroker";
		ldapGroupBrokerReassign = "ldapGroupBrokerReassign";
		ldapBaseDN = "ldapBaseDN";
		
		// SetFields
		ReflectionTestUtils.setField(accountBusinessProcess, "userAccountService", userAccountService);
		ReflectionTestUtils.setField(accountBusinessProcess, "brokerService", brokerService);
		ReflectionTestUtils.setField(accountBusinessProcess, "ldapTemplate", ldapTemplate);
		ReflectionTestUtils.setField(accountBusinessProcess, "ldapGroupBroker", ldapGroupBroker);
		ReflectionTestUtils.setField(accountBusinessProcess, "ldapGroupBrokerReassign", ldapGroupBrokerReassign);
		ReflectionTestUtils.setField(accountBusinessProcess, "ldapBaseDN", ldapBaseDN);
	}

	@AfterEach
	public void tearDown() throws Exception {
		accountBusinessProcess = null;
		userAccountService = null;
		brokerService = null;
		userAccount = null;
		ldapGroupBroker = null;
		ldapGroupBrokerReassign = null;
		ldapBaseDN = null;
		memberListEnumeration = null;
		getUserNamesByCNEnum = null;
		ldapMemberList = null;
		getUserNamesByCNList = null;
	}
	
	/**
	 * Test for the method {@link AccountsBusinessProcess#findByUId(String)}
	 * Validating the the userAccountService is returning the UserAccount
	 * 
	 * @throws BrokerServiceException Can be thrown when using accountBusinessProcess findByUId
	 * @throws BRMServiceException Can be thrown when using userAccountService findByUId
	 */
	@Test
	public void testFindByUId_HappyPath() throws BrokerServiceException, BRMServiceException {
		// Given
		final String uId = "1234";
		userAccount.setId(1234L);
		
		when(userAccountService.findByUId(uId)).thenReturn(userAccount);

		// When
		final UserAccount findByUIdResult = accountBusinessProcess.findByUId(uId);

		// Then
		assertEquals(userAccount.getUid(), findByUIdResult.getUid(), "The UserAccount Uid");
	}
	
	/**
	 * Test for the method {@link AccountsBusinessProcess#getUsers(String, ProvinceCodeEnum)}
	 * The ldapMemberList and the ldapReassignMemberList are empty so the returned List should be empty
	 */
	@Test
	public void testgetUsers_Empty() throws BrokerServiceException {
		// Given
		final List<Enumeration<String>> ldapMemberList = new ArrayList<Enumeration<String>>();
		final String aCurrentUid = "1234-TEST"; 
		final ProvinceCodeEnum aProvinceCode = ProvinceCodeEnum.QUEBEC;
		
		when(ldapTemplate.searchUser(anyString(), anyString(), any(AttributesMapper.class))).thenReturn(ldapMemberList);
		
		// When
		List<UserAccount> getUsersResult = accountBusinessProcess.getUsers(aCurrentUid, aProvinceCode);
		
		// Then
		assertTrue(getUsersResult.isEmpty(), "The Users List should be empty (True)");
	}
	
	/**
	 * Test for the method {@link AccountsBusinessProcess#getUsers(String, ProvinceCodeEnum)}
	 * The ldapMemberList and the ldapReassignMemberList have values in, so the returned List should be filled with those
	 * 
	 * @throws BRMServiceException When calling UserAccountService saveUser
	 */
	@Test
	public void testgetUsers_HappyPath() throws BrokerServiceException, BRMServiceException {
		// Given
		final String aCurrentUid = "1234-TEST"; 
		final ProvinceCodeEnum aProvinceCode = ProvinceCodeEnum.QUEBEC;
		final String userAccountName = "Jay Unit";
		
		ldapMemberList.add(memberListEnumeration);
		getUserNamesByCNList.add(getUserNamesByCNEnum);

		when(ldapTemplate.searchUser(anyString(), anyString(), any(AttributesMapper.class))).thenReturn(ldapMemberList);
		when(ldapTemplate.search(anyString(), anyString(), any(UserNameAttributeMapper.class))).thenReturn(getUserNamesByCNList);
		
		// The enum is going to return only one value
		when(memberListEnumeration.hasMoreElements()).thenReturn(Boolean.TRUE).thenReturn(Boolean.FALSE);
		when(memberListEnumeration.nextElement()).thenReturn("uid=1234-TEST,TEST");
		
		// The enum is going to return only one value
		when(getUserNamesByCNEnum.hasMoreElements()).thenReturn(Boolean.TRUE).thenReturn(Boolean.FALSE);
		when(getUserNamesByCNEnum.nextElement()).thenReturn(userAccountName);
		
		doNothing().when(userAccountService).saveUser(userAccount);
		
		// When
		List<UserAccount> getUsersResult = accountBusinessProcess.getUsers(aCurrentUid, aProvinceCode);
		
		// Then
		assertTrue(!getUsersResult.isEmpty(), "The Users List should NOT be empty (True)");
		assertEquals(userAccountName, getUsersResult.get(0).getName(), "The UserAccount account name");
		assertEquals(aCurrentUid, getUsersResult.get(0).getUid(), "The UserAccount Uid");
				
		verify(userAccountService).saveUser(getUsersResult.get(0));
	}
	
	/**
	 * Test for the method {@link AccountsBusinessProcess#checkForUsersToRemove(List)}
	 * @throws BrokerServiceException 
	 * @throws BRMServiceException 
	 *
	 */
	@Test
	public void testCheckForUsersToRemove() throws BrokerServiceException, BRMServiceException {
		// Given
		final String uId = "1234";
		userAccount.setId(1234L);
		userAccount.setUid(uId);
		
		final List<UserAccount> usersWithAccess = new ArrayList<UserAccount>();
		usersWithAccess.add(userAccount);
		
		when(userAccountService.findWithBrokerWebOfficeAccess()).thenReturn(usersWithAccess);

		// When
		accountBusinessProcess.checkForUsersToRemove(usersWithAccess);

		// Then
		verify(userAccountService).findWithBrokerWebOfficeAccess();
	}

}
