package com.intact.brokeroffice.business.report.performancemetric.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

import org.easymock.classextension.EasyMock;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.ing.canada.cif.domain.enums.CifCompanyEnum;
import com.ing.canada.plp.domain.enums.ManufacturerCompanyCodeEnum;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.ing.canada.plp.report.performancemetric.PerformanceMetricInfo;
import com.ing.canada.plp.report.service.IPerformanceMetricInfoService;

//import junit.framework.JUnit4TestAdapter;

public class PerformanceMetricBusinessTest {

	private PerformanceMetricBusinessProcess kpiBusinessProcess;

	private IPerformanceMetricInfoService kpiService;

	@BeforeEach
	public void setup() {
		this.kpiBusinessProcess = new PerformanceMetricBusinessProcess();
		this.kpiService = EasyMock.createMock(IPerformanceMetricInfoService.class);
	}

	@AfterEach
	public void teardown() {
		this.kpiBusinessProcess = null;
		this.kpiService = null;
	}

	@Test
	public void testServices() {

		// Setting up the expected value of the method call calc

		Calendar startingDate = Calendar.getInstance();
		startingDate.set(2012, 1, 1);

		Calendar endingDate = Calendar.getInstance();
		endingDate.set(2012, 1, 2);

		List<PerformanceMetricInfo> performanceMetricInfos = new ArrayList<PerformanceMetricInfo>();

		PerformanceMetricInfo aPerformanceMetricInfo = new PerformanceMetricInfo();
		aPerformanceMetricInfo.setPurchaseRequestComplete(BigDecimal.ZERO);
		aPerformanceMetricInfo.setPurchaseRequestIncomplete(BigDecimal.ZERO);
		aPerformanceMetricInfo.setQuotesComplete(BigDecimal.ZERO);
		aPerformanceMetricInfo.setQuotesIncomplete(BigDecimal.ONE);
		aPerformanceMetricInfo.setQuotesTotal(BigDecimal.ONE);
		aPerformanceMetricInfo.setUploadedQuotesAccepted(BigDecimal.ONE);
		aPerformanceMetricInfo.setUploadedQuotesNotProceed(BigDecimal.ONE);
		aPerformanceMetricInfo.setUploadedQuotesRefused(BigDecimal.ONE);

		aPerformanceMetricInfo.setFollowUpContactNeeded(BigDecimal.ONE);
		aPerformanceMetricInfo.setFollowUpNeverContacted(BigDecimal.ZERO);
		aPerformanceMetricInfo.setFollowUpNoContactNeeded(BigDecimal.ZERO);
		aPerformanceMetricInfo.setFollowUpTotal(BigDecimal.ONE);
		aPerformanceMetricInfo.setMasterBrokerName("Intact Assurance");
		aPerformanceMetricInfo.setSubBrokerName("Intact Assurance D");
		aPerformanceMetricInfo.setQuotesExpired(BigDecimal.ONE);
		aPerformanceMetricInfo.setSubBrokerNbr("0028");
		aPerformanceMetricInfo.setMasterBrokerNbr("0030");

		performanceMetricInfos.add(aPerformanceMetricInfo);
		org.easymock.EasyMock.expect(
				this.kpiService.getPerformanceMetricListSP(startingDate.getTime(), endingDate.getTime(), null,
						ProvinceCodeEnum.QUEBEC, CifCompanyEnum.INTACT_QC.getSubBrokerCompanyNumber(),
						ManufacturerCompanyCodeEnum.INTACT_QUEBEC_REGION)).andReturn(performanceMetricInfos);

		Calendar startingDate_2 = Calendar.getInstance();
		startingDate_2.set(2012, 1, 3);
		Calendar endingDate_2 = Calendar.getInstance();
		endingDate_2.set(2012, 1, 4);

		org.easymock.EasyMock.expect(
				this.kpiService.getPerformanceMetricListSP(startingDate_2.getTime(), endingDate_2.getTime(), null,
						ProvinceCodeEnum.QUEBEC, CifCompanyEnum.INTACT_QC.getSubBrokerCompanyNumber(),
						ManufacturerCompanyCodeEnum.INTACT_QUEBEC_REGION)).andReturn(null);

		// Setup is finished to activate the mock
		EasyMock.replay(this.kpiService);

		this.kpiBusinessProcess.initPerformanceMetricInfoService(this.kpiService);

		assertEquals(performanceMetricInfos, this.kpiBusinessProcess.getPerformanceMetricListSP(startingDate.getTime(),
				endingDate.getTime(), null, ProvinceCodeEnum.QUEBEC, CifCompanyEnum.INTACT_QC
						.getSubBrokerCompanyNumber(), ManufacturerCompanyCodeEnum.INTACT_QUEBEC_REGION));
		assertEquals(null, this.kpiBusinessProcess.getPerformanceMetricListSP(startingDate_2.getTime(), endingDate_2
				.getTime(), null, ProvinceCodeEnum.QUEBEC, CifCompanyEnum.INTACT_QC.getSubBrokerCompanyNumber(),
				ManufacturerCompanyCodeEnum.INTACT_QUEBEC_REGION));

	}

//	public static junit.framework.Test suite() {
//		return new JUnit4TestAdapter(PerformanceMetricBusinessTest.class);
//	}
}
