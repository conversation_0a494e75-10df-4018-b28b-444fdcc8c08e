package com.intact.brokeroffice.business.report.subbrokerassignment.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

import org.easymock.classextension.EasyMock;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.ing.canada.cif.domain.enums.CifCompanyEnum;
import com.ing.canada.plp.domain.enums.ManufacturerCompanyCodeEnum;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.ing.canada.plp.report.SubBrokerAssignmentInfo;
import com.ing.canada.plp.service.ISubBrokerAssignmentInfoService;

//import junit.framework.JUnit4TestAdapter;

public class SubBrokerAssignmentBusinessTest {

	private SubBrokerAssignmentBusinessProcess subBrokerAssignmentBusinessProcess;

	private ISubBrokerAssignmentInfoService subBrokerAssignmentInfoService;

	@BeforeEach
	public void setup() {
		this.subBrokerAssignmentBusinessProcess = new SubBrokerAssignmentBusinessProcess();
		this.subBrokerAssignmentInfoService = EasyMock.createMock(ISubBrokerAssignmentInfoService.class);
	}

	@AfterEach
	public void teardown() {
		this.subBrokerAssignmentBusinessProcess = null;
		this.subBrokerAssignmentInfoService = null;
	}

	@Test
	public void testServices() {

		// Setting up the expected value of the method call

		Calendar startingDate = Calendar.getInstance();
		startingDate.set(2012, 1, 1);

		Calendar endingDate = Calendar.getInstance();
		endingDate.set(2012, 1, 2);

		List<SubBrokerAssignmentInfo> subBrokerAssignmentInfos = new ArrayList<SubBrokerAssignmentInfo>();

		SubBrokerAssignmentInfo aSubBrokerAssignmentInfo = new SubBrokerAssignmentInfo();
		aSubBrokerAssignmentInfo.setNewBrokerCity("Montreal");
		aSubBrokerAssignmentInfo.setOldBrokerCity("Laval");
		aSubBrokerAssignmentInfo.setNewBrokerCity("0028");
		aSubBrokerAssignmentInfo.setOldBrokerName("Intact Assurance D");
		aSubBrokerAssignmentInfo.setNewBrokerName("Intact Assurance A");
		subBrokerAssignmentInfos.add(aSubBrokerAssignmentInfo);

		org.easymock.EasyMock.expect(
				this.subBrokerAssignmentInfoService.getAllSubBrokerAssignmentsSP(startingDate.getTime(), endingDate
						.getTime(), ProvinceCodeEnum.QUEBEC, CifCompanyEnum.INTACT_QC.getSubBrokerCompanyNumber(),
						ManufacturerCompanyCodeEnum.INTACT_QUEBEC_REGION)).andReturn(subBrokerAssignmentInfos).times(2);

		Calendar startingDate_2 = Calendar.getInstance();
		startingDate_2.set(2012, 1, 3);
		Calendar endingDate_2 = Calendar.getInstance();
		endingDate_2.set(2012, 1, 4);
		org.easymock.EasyMock.expect(
				this.subBrokerAssignmentInfoService.getAllSubBrokerAssignmentsSP(startingDate_2.getTime(), endingDate_2
						.getTime(), ProvinceCodeEnum.QUEBEC, CifCompanyEnum.INTACT_QC.getSubBrokerCompanyNumber(),
						ManufacturerCompanyCodeEnum.INTACT_QUEBEC_REGION)).andReturn(null);

		// Setup is finished to activate the mock
		EasyMock.replay(this.subBrokerAssignmentInfoService);

		this.subBrokerAssignmentBusinessProcess.initSubBrokerAssignmentInfoService(this.subBrokerAssignmentInfoService);

		assertEquals(subBrokerAssignmentInfos, this.subBrokerAssignmentBusinessProcess.getAllSubBrokerAssignmentsSP(
				startingDate.getTime(), endingDate.getTime(), ProvinceCodeEnum.QUEBEC, CifCompanyEnum.INTACT_QC
						.getSubBrokerCompanyNumber(), ManufacturerCompanyCodeEnum.INTACT_QUEBEC_REGION));

		for (SubBrokerAssignmentInfo anInfo : this.subBrokerAssignmentBusinessProcess.getAllSubBrokerAssignmentsSP(
				startingDate.getTime(), endingDate.getTime(), ProvinceCodeEnum.QUEBEC, CifCompanyEnum.INTACT_QC
						.getSubBrokerCompanyNumber(), ManufacturerCompanyCodeEnum.INTACT_QUEBEC_REGION)) {
			assertEquals(anInfo.getNewBrokerCity(), aSubBrokerAssignmentInfo.getNewBrokerCity());
			assertEquals(anInfo.getOldBrokerCity(), aSubBrokerAssignmentInfo.getOldBrokerCity());
		}

		assertEquals(null, this.subBrokerAssignmentBusinessProcess.getAllSubBrokerAssignmentsSP(startingDate_2
				.getTime(), endingDate_2.getTime(), ProvinceCodeEnum.QUEBEC, CifCompanyEnum.INTACT_QC
				.getSubBrokerCompanyNumber(), ManufacturerCompanyCodeEnum.INTACT_QUEBEC_REGION));

	}

//	public static junit.framework.Test suite() {
//		return new JUnit4TestAdapter(SubBrokerAssignmentBusinessTest.class);
//	}
}
