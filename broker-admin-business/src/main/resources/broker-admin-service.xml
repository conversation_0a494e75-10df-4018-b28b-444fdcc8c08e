<?xml version="1.0" encoding="UTF-8"?>

<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:jee="http://www.springframework.org/schema/jee"
	xmlns:context="http://www.springframework.org/schema/context"
	xsi:schemaLocation="http://www.springframework.org/schema/beans
						http://www.springframework.org/schema/beans/spring-beans.xsd
						http://www.springframework.org/schema/context
						http://www.springframework.org/schema/context/spring-context.xsd
						http://www.springframework.org/schema/jee
						http://www.springframework.org/schema/jee/spring-jee.xsd">
						
	<import resource="classpath:broker-admin-aop.xml" />
	
	<context:annotation-config />
	<context:component-scan base-package="com.intact.brokeroffice.business" />
	<context:component-scan base-package="com.intact.uploadtosavers" />
	<context:component-scan base-package="com.intact.canada.brm.service" />
	<context:component-scan base-package="com.intact.canada.brm.service.impl" />
	<context:component-scan base-package="com.ing.canada.cif.service.impl" />

	<bean id="cifBrokersService" class="com.ing.canada.cif.service.impl.BrokerService"/>
	<bean id="userAccountService" class="com.intact.canada.brm.service.impl.UserAccountService"/>
	<bean id="accessProfileService" class="com.intact.canada.brm.service.impl.AccessProfileService"/>

	<bean id="accountsBusinessProcess" class="com.intact.brokeroffice.business.accounts.impl.AccountsBusinessProcess">
		<property name="ldapTemplate" ref="ldapTemplate" />
	</bean>

	<bean id="ldapPlaceholderConfig" class="org.springframework.context.support.PropertySourcesPlaceholderConfigurer">
		<property name="location" value="classpath:ldap.properties" />
		<property name="order" value="0" />
	</bean>

	<bean id="ldap-base-dn" class="java.lang.String" depends-on="ldapPlaceholderConfig">
		<constructor-arg value="${LDAP_BASE_DN}" />
	</bean>
	
	<bean id="contextSource" class="org.springframework.ldap.core.support.LdapContextSource" depends-on="ldapPlaceholderConfig">
		<property name="url" value="${LDAP_URL}:${LDAP_PORT}" />
		<property name="userDn" value="${LDAP_USER_DN}" />
		<property name="password" value="${LDAP_PASSWORD}" />
	</bean>
	
	<bean id="ldapTemplate" class="com.intact.tools.ldap.util.LdapTemplate">
		<constructor-arg ref="contextSource" />
	</bean>
	
	<!-- JdbcTemplate for DAOs requiring JDBC access -->
	<bean id="sad-jdbcTemplate" class="org.springframework.jdbc.core.JdbcTemplate">
		<property name="dataSource" ref="sadDataSource" />
	</bean>

<!--	<jee:jndi-lookup id="sadDataSource" jndi-name="${sad-dataSource.jndi-name}" resource-ref="false" />-->
	<jee:jndi-lookup id="sadDataSource" jndi-name="java:comp/env/jdbc/SAD" resource-ref="true" />

</beans>
