package com.intact.brokeroffice.clientservice.dao;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Types;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

@Repository
public class ClientServiceProfilMasterSPDao implements ClientServiceProfilMasterDao {
	
	@Autowired
	@Qualifier("sad-jdbcTemplate")
	private JdbcTemplate jdbcTemplate;
	
	@Override
	public String updateProfilMaster(String subBrokerId, Date startDate, String profilMaster) {
		String message = "FAILED";
		Connection connection = null;
		CallableStatement cStmt = null;
		try {
			connection = jdbcTemplate.getDataSource().getConnection();
			cStmt = connection.prepareCall("{call SADADMIN.SAD_USER_POOL.MERGE_BROKER(?,?,?,?,?,?,?,?,?)}");
			cStmt.setString("P_CALL_TYPE", null);
			cStmt.setInt("P_SUB_BROKER_ID", Integer.parseInt(subBrokerId));
			cStmt.setDate("P_START_DATE", new java.sql.Date(startDate.getTime()));
			cStmt.setDate("P_END_DATE", null);
			cStmt.setString("P_LIST_MASTER_PROFL", "'" + profilMaster + "'");
			cStmt.setString("P_LIST_ADD_FUNC_PROFL", null);
			cStmt.registerOutParameter("P_SYS_ACCSR_ID", Types.NUMERIC);
			cStmt.registerOutParameter("P_STATUS_MESSAGE", Types.VARCHAR);
			cStmt.registerOutParameter("P_STATUS_TRANSACTION", Types.VARCHAR);
			cStmt.execute();
			message = cStmt.getString("P_STATUS_MESSAGE");
		} catch (DataAccessException e) {
			throw new RuntimeException(e);
		} catch (SQLException e) {
			throw new RuntimeException(e);
		} finally {
			try {
				if (cStmt != null) {
					cStmt.close();
				}
				
				if (connection != null) {
					connection.close();
				} 
				
			} catch (SQLException e) {}
		}		
		
		return message;
	}
	
	public List<ClientServiceProfilMaster> getProfilMasterInfo(String subBrokerId) {
		List<ClientServiceProfilMaster> result = null;
		Connection connection = null;
		
		try {			
			connection = jdbcTemplate.getDataSource().getConnection();
			Statement stmt =  connection.createStatement();
			String sql = "SELECT * FROM TABLE(sadadmin.get_recon_values_sub_broker('D',%s,NULL))".formatted(subBrokerId);
			ResultSet rs = stmt.executeQuery(sql);
			
			while (rs.next()) {
				result = new ArrayList<ClientServiceProfilMaster>();
				ClientServiceProfilMaster clientServiceProfilMaster = new ClientServiceProfilMaster();
				clientServiceProfilMaster.setSubBrokerNumber(rs.getString("SUB_BROKER"));
				clientServiceProfilMaster.setProfilesMaster(rs.getString("PROFILES_MASTER"));
				clientServiceProfilMaster.setSystemAccessor(rs.getString("SYSTEM_ACCESSOR"));
				result.add(clientServiceProfilMaster);
			}
			stmt.close();
		} catch (DataAccessException e) {
			throw new RuntimeException(e);
		} catch (SQLException e) {
			throw new RuntimeException(e);
		} finally {
			try {
				if (connection != null)					
					connection.close();
			} catch (SQLException e) {}			
		}
		
		return result;
	}
}
