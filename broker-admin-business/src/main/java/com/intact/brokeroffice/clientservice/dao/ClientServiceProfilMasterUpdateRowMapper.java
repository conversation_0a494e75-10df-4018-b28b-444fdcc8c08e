package com.intact.brokeroffice.clientservice.dao;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

public class ClientServiceProfilMasterUpdateRowMapper implements RowMapper<ClientServiceProfilMaster> {
	
	@Override
	public ClientServiceProfilMaster mapRow(ResultSet rs, int rowNum) throws SQLException {
		ClientServiceProfilMaster clientServiceInfo = new ClientServiceProfilMaster();
		clientServiceInfo.setSubBrokerNumber(rs.getString(ClientServiceDaoConstants.P_SUB_BROKER_ID));
		clientServiceInfo.setSystemAccessor(rs.getString(ClientServiceDaoConstants.P_SYS_ACCSR_ID));
		clientServiceInfo.setProfilesMaster(rs.getString(ClientServiceDaoConstants.P_LIST_MASTER_PROFL));
		clientServiceInfo.setProfilesAdd(rs.getString(ClientServiceDaoConstants.P_LIST_ADD_FUNC_PROFL));
		return clientServiceInfo;
	}

}
