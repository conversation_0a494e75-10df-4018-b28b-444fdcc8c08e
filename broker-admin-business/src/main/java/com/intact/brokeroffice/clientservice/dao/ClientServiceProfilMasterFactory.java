package com.intact.brokeroffice.clientservice.dao;

import javax.annotation.PostConstruct;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

@Component
public class ClientServiceProfilMasterFactory {

	// default value of stored procedure name
	private static String MERGE_BROKER = "SADADMIN.SAD_USER_POOL.MERGE_BROKER";
	
	@Autowired
	@Qualifier("cif-jdbcTemplate")
	private JdbcTemplate jdbcTemplate;

	@Autowired(required = false)
	@Qualifier("clientServiceProfilMasterUpdateStoredProcedure")
	private String clientServiceProfilMasterUpdateStoredProcedure;
	
	private ClientServiceProfilMasterUpdateStoredProcedure clientServiceProfilMasterUpdate;
	
	/**
	 * Get client search stored procedure.
	 * 
	 * @return {@link ClientSearchStoredProcedure}
	 */
	public ClientServiceProfilMasterUpdateStoredProcedure getClientServiceProfilMasterUpdateStoredProcedure() {
		// instanciate if null
		if (this.clientServiceProfilMasterUpdate == null) {
			initClientServiceProfilMasterUpdateStoredProcedure();
			this.clientServiceProfilMasterUpdate = new ClientServiceProfilMasterUpdateStoredProcedure(this.jdbcTemplate, this.clientServiceProfilMasterUpdateStoredProcedure);
		}
		return this.clientServiceProfilMasterUpdate;
	}
	
	/**
	 * PostContruct to initialize Store Procedure.
	 */
	@PostConstruct
	public void postConstruct() {
		
		initClientServiceProfilMasterUpdateStoredProcedure();
		this.clientServiceProfilMasterUpdate = new ClientServiceProfilMasterUpdateStoredProcedure(this.jdbcTemplate, this.clientServiceProfilMasterUpdateStoredProcedure);
	}
	
	public String getStoredProcedureName() {
		return this.clientServiceProfilMasterUpdateStoredProcedure;
	}
	
	private void initClientServiceProfilMasterUpdateStoredProcedure() {
		if (StringUtils.isEmpty(this.clientServiceProfilMasterUpdateStoredProcedure)) {
			this.clientServiceProfilMasterUpdateStoredProcedure = MERGE_BROKER;
		}
	}
	
}
