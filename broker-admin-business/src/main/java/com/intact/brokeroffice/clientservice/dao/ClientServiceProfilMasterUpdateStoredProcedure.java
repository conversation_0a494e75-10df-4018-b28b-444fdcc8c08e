package com.intact.brokeroffice.clientservice.dao;

import java.sql.Types;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.SqlOutParameter;
import org.springframework.jdbc.core.SqlParameter;
import org.springframework.jdbc.object.StoredProcedure;

public class ClientServiceProfilMasterUpdateStoredProcedure extends StoredProcedure {
	
	public ClientServiceProfilMasterUpdateStoredProcedure(JdbcTemplate jdbcTemplate, String storedProcedure) {
		super(jdbcTemplate, storedProcedure);
		ClientServiceProfilMasterUpdateRowMapper rowMapper = new ClientServiceProfilMasterUpdateRowMapper();
		declareParameter(new SqlParameter(ClientServiceDaoConstants.P_CALL_TYPE, Types.VARCHAR));
		declareParameter(new SqlParameter(ClientServiceDaoConstants.P_SUB_BROKER_ID, Types.NUMERIC));
		declareParameter(new SqlParameter(ClientServiceDaoConstants.P_START_DATE, Types.DATE));
		declareParameter(new SqlParameter(ClientServiceDaoConstants.P_END_DATE, Types.DATE));
		declareParameter(new SqlParameter(ClientServiceDaoConstants.P_LIST_MASTER_PROFL, Types.VARCHAR));
		declareParameter(new SqlParameter(ClientServiceDaoConstants.P_LIST_ADD_FUNC_PROFL, Types.VARCHAR));
		declareParameter(new SqlOutParameter(ClientServiceDaoConstants.P_SYS_ACCSR_ID,  oracle.jdbc.OracleTypes.CURSOR, rowMapper));
		declareParameter(new SqlOutParameter(ClientServiceDaoConstants.P_STATUS_MESSAGE,  oracle.jdbc.OracleTypes.CURSOR, rowMapper));
		declareParameter(new SqlParameter(ClientServiceDaoConstants.P_STATUS_TRANSACTION, Types.VARCHAR));
		compile();
	}
	
	public Map<String, Object> updateClientServiceInfo(String callType, String subBrokerNumber, Date startDate, Date endDate, String profilMaster, String profilAdd) {
		Map<String, Object> inParams = new HashMap<String, Object>();
		//inParams.put(ClientServiceDaoConstants.P_SYS_ACCSR_ID, null);
		inParams.put(ClientServiceDaoConstants.P_SUB_BROKER_ID, subBrokerNumber);
		inParams.put(ClientServiceDaoConstants.P_CALL_TYPE, callType);
		inParams.put(ClientServiceDaoConstants.P_START_DATE, startDate);
		inParams.put(ClientServiceDaoConstants.P_END_DATE, endDate);
		inParams.put(ClientServiceDaoConstants.P_LIST_MASTER_PROFL, profilMaster);
		inParams.put(ClientServiceDaoConstants.P_LIST_ADD_FUNC_PROFL, profilAdd);
		//inParams.put(ClientServiceDaoConstants.P_STATUS_MESSAGE, null);
		inParams.put(ClientServiceDaoConstants.P_STATUS_TRANSACTION, null);
		// execute
		Map<String, Object> result = execute(inParams); // call
		return result;
	}

}
