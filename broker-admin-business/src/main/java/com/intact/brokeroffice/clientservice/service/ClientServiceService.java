package com.intact.brokeroffice.clientservice.service;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.intact.brokeroffice.clientservice.dao.ClientServiceProfilMaster;
import com.intact.brokeroffice.clientservice.dao.ClientServiceProfilMasterDao;


@Service
public class ClientServiceService implements IClientServiceService {
	
	@Autowired
	private ClientServiceProfilMasterDao clientServiceProfilMasterSPDao;

	@Override
	public String updateProfilMaster(String subBrokerId, Date startDate, String profilMaster) {
		return clientServiceProfilMasterSPDao.updateProfilMaster(subBrokerId, startDate, profilMaster);
	}

	@Override
	public List<ClientServiceProfilMaster> getProfilMasterInfo(String subBrokerId) {
		return clientServiceProfilMasterSPDao.getProfilMasterInfo(subBrokerId);
	}
}
