package com.intact.brokeroffice.clientservice.dao;

import java.sql.Date;

/**
 * This class contains the the status severals fields within the section Client Service of WebZone. 
 *
 */
public class ClientServiceProfilMaster {
	
	private String subBrokerNumber;
	private String systemAccessor;
	private String profilesMaster;
	private String profilesAdd;
	private Date startDate;
	
	public String getSubBrokerNumber() {
		return subBrokerNumber;
	}
	public void setSubBrokerNumber(String subBrokerNumber) {
		this.subBrokerNumber = subBrokerNumber;
	}
	public String getSystemAccessor() {
		return systemAccessor;
	}
	public void setSystemAccessor(String systemAccessor) {
		this.systemAccessor = systemAccessor;
	}
	public String getProfilesMaster() {
		return profilesMaster;
	}
	public void setProfilesMaster(String profilesMaster) {
		this.profilesMaster = profilesMaster;
	}
	public String getProfilesAdd() {
		return profilesAdd;
	}
	public void setProfilesAdd(String profilesAdd) {
		this.profilesAdd = profilesAdd;
	}
	public Date getStartDate() {
		return startDate;
	}
	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}
	
	
	
	

	
}
