package com.intact.brokeroffice.business.dto;

import java.util.Map;

import com.intact.tools.comparison.domain.Ignored;

public class LineOfBusinessDTO {

	private String lineOfBusiness = null;
	private ElectronicContactDTO contact = null;
	private Map<String, ApplicationDTO> applications = null;

	public LineOfBusinessDTO() {
	}

	@Ignored
	public String getLineOfBusiness() {
		return this.lineOfBusiness;
	}

	public void setLineOfBusiness(String lineOfBusiness) {
		this.lineOfBusiness = lineOfBusiness;
	}

	public ElectronicContactDTO getContact() {
		return this.contact;
	}

	public void setContact(ElectronicContactDTO contact) {
		this.contact = contact;
	}

	public Map<String, ApplicationDTO> getApplications() {
		return this.applications;
	}

	public void setApplications(Map<String, ApplicationDTO> applications) {
		this.applications = applications;
	}

	@Override
	public String toString() {
		return "[line of business=" + this.getLineOfBusiness() + ", contact=" + this.getContact() + "]";
	}

}
