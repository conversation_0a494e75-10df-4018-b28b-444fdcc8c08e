package com.intact.brokeroffice.business.exception;

import java.io.Serial;

import com.ing.canada.commons.service.ApplicationService;
import com.ing.canada.commons.service.exception.ApplicationServiceException;

public class BrokerServiceException extends ApplicationServiceException {

	@Serial
	private static final long serialVersionUID = 1L;

	public BrokerServiceException(ApplicationService applicationService,
			Throwable exception) {
		super(applicationService, exception);
	}

	public BrokerServiceException(String applicationService, Throwable exception) {
		super(applicationService, exception);
	}

}
