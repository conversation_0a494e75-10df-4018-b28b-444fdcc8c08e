package com.intact.brokeroffice.business.report.performancemetric.impl;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.ing.canada.plp.domain.enums.ManufacturerCompanyCodeEnum;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.ing.canada.plp.report.performancemetric.PerformanceMetricInfo;
import com.ing.canada.plp.report.service.IPerformanceMetricInfoService;
import com.intact.brokeroffice.business.report.performancemetric.IPerformanceMetricBusinessProcess;

@Component
public class PerformanceMetricBusinessProcess implements
		IPerformanceMetricBusinessProcess {
	
	@Autowired
	private IPerformanceMetricInfoService performanceMetricInfoService;

	/** 
	 * 
	 * @see IPerformanceMetricBusinessProcess#getPerformanceMetricListSP(Date, Date)
	 */
	public List<PerformanceMetricInfo> getPerformanceMetricListSP(Date aDateFrom, Date aDateTo, String aQuoteSource,ProvinceCodeEnum aProvinceCode, String aSubBrokerCompanyNumber, ManufacturerCompanyCodeEnum aManufacturerCompanyCode) {		
		return this.performanceMetricInfoService.getPerformanceMetricListSP(aDateFrom, aDateTo, aQuoteSource, aProvinceCode, aSubBrokerCompanyNumber, aManufacturerCompanyCode);
	}
	
	public void initPerformanceMetricInfoService(
			IPerformanceMetricInfoService aPerformanceMetricInfoService) {
		this.performanceMetricInfoService = aPerformanceMetricInfoService;
	}

	
}
