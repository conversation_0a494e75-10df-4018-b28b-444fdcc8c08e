package com.intact.brokeroffice.business.dto;

import java.util.Map;

import com.intact.tools.comparison.domain.Ignored;

public class ApplicationDTO {

	private String applicationId = null;
	private String accessType = null;
	private Boolean assignable = null;
	private Boolean notifiable = null;
	private Map<String, PhoneDTO> phones = null;

	public ApplicationDTO() {
	}

	public ApplicationDTO(String applicationId) {
		this.setId(applicationId);
	}

	@Ignored
	public String getId() {
		return this.applicationId;
	}

	public void setId(String applicationId) {
		this.applicationId = applicationId;
	}

	public String getAccessType() {
		return this.accessType;
	}

	public void setAccessType(String accessType) {
		this.accessType = accessType;
	}

	public Boolean getAssignable() {
		return this.assignable;
	}

	public void setAssignable(Boolean assignable) {
		this.assignable = assignable;
	}

	public Map<String, PhoneDTO> getPhones() {
		return this.phones;
	}

	public void setPhones(Map<String, PhoneDTO> phones) {
		this.phones = phones;
	}
	
	public Boolean getNotifiable() {
		return notifiable;
	}

	public void setNotifiable(Boolean notifiable) {
		this.notifiable = notifiable;
	}

	@Override
	public String toString() {
		return "application id=" + this.getId() + ", access type=" + this.getAccessType() + ", assignable="
				+ this.getAssignable() + ", phones=" + this.getPhones() + ", notifiable=" + this.getNotifiable() + "]";
	}
}
