package com.intact.brokeroffice.business.common.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.ing.canada.plp.domain.enums.BusinessTransactionActivityCodeEnum;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.ing.canada.plp.service.IPolicyVersionService;
import com.intact.brokeroffice.business.common.ICommandBusinessProcess;

/**
 * Everything common that is useful on every page
 */
@Component
public class CommandBusinessProcess implements ICommandBusinessProcess {
	

	/** The policy version service. */
	@Autowired
	private IPolicyVersionService policyVersionService;

	
	/* (non-Javadoc)
	 * @see com.intact.brokeroffice.business.common.ICommandBusinessProcess#findLatestPolicyVersion(java.lang.String)
	 */
	public PolicyVersion findLatestPolicyVersion(String agreementNumber) {
		return this.policyVersionService.findLatestQuoteByTransactionActivityCodes(agreementNumber,
				BusinessTransactionActivityCodeEnum.INITIAL_QUOTE_OR_POLICY_CREATION,
				BusinessTransactionActivityCodeEnum.VERSION_RETRIEVED);
	}
	
}
