package com.intact.brokeroffice.business.dto;

import com.intact.tools.comparison.domain.Ignored;

public class ImageDTO {
	
	private String context = null;
	private byte[] previousImage = null;
	private String language = null;
	private String usage = null;
	private String type = null;
	
	public ImageDTO() {
	}
	
	@Ignored
	public String getContext() {
		return this.context;
	}
	public void setContext(String context) {
		this.context = context;
	}
	
	@Ignored
	public byte[] getPreviousImage() {
		return this.previousImage;
	}
	public void setPreviousImage(byte[] previousImage) {
		this.previousImage = previousImage;
	}

	@Ignored
	public String getType() {
		return this.type;
	}

	public void setType(String type) {
		this.type = type;
	}
	
	@Ignored
	public String getUsage() {
		return this.usage;
	}

	public void setUsage(String usage) {
		this.usage = usage;
	}

	@Ignored
	public String getLanguage() {
		return this.language;
	}

	public void setLanguage(String language) {
		this.language = language;
	}

	public String toString() {
		return "[context=" + this.getContext() + "]";
	}
}
