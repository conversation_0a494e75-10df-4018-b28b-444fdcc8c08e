package com.intact.brokeroffice.business.report.subbrokerassignment.impl;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.ing.canada.plp.domain.enums.ManufacturerCompanyCodeEnum;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.ing.canada.plp.report.SubBrokerAssignmentInfo;
import com.ing.canada.plp.service.ISubBrokerAssignmentInfoService;
import com.intact.brokeroffice.business.report.subbrokerassignment.ISubBrokerAssignmentBusinessProcess;

@Component
public class SubBrokerAssignmentBusinessProcess implements
		ISubBrokerAssignmentBusinessProcess {

	@Autowired
	private ISubBrokerAssignmentInfoService subBrokerAssignmentInfoService;
	

	public List<SubBrokerAssignmentInfo> getAllSubBrokerAssignmentsSP(Date aDateFrom, Date aDateTo,
			 ProvinceCodeEnum aProvinceCode, String aSubBrokerCompanyNumber,
			ManufacturerCompanyCodeEnum aManufacturerCompanyCode) {
		
		return this.subBrokerAssignmentInfoService.getAllSubBrokerAssignmentsSP(aDateFrom, aDateTo, aProvinceCode,
				aSubBrokerCompanyNumber, aManufacturerCompanyCode);
	}
	
	public void initSubBrokerAssignmentInfoService(ISubBrokerAssignmentInfoService aSubBrokerAssignmentInfoService){
		this.subBrokerAssignmentInfoService = aSubBrokerAssignmentInfoService;
	}

}
