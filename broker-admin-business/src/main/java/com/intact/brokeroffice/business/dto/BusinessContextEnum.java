package com.intact.brokeroffice.business.dto;

import org.apache.commons.lang.StringUtils;

import com.ing.canada.cif.domain.enums.LineOfBusinessEnum;
import com.intact.brokeroffice.business.common.LineOfBusinessUtils;


public enum BusinessContextEnum {
	COMMERCIAL_LINES_AUTO("CL", LineOfBusinessEnum.COMMERCIAL_LINE, null), 
	PERSONAL_LINES_AUTO_RES("PL", LineOfBusinessEnum.PERSONAL_LINE,  null), 
	COMMERCIAL_LINES_PC("CL_CCQQ", LineOfBusinessEnum.COMMERCIAL_LINE, "CCQQ");
	
	private LineOfBusinessEnum lineOfBusiness;
	
	private String applicationId;
	
	/**
	 * Instantiates a new line of business code enum.
	 * 
	 * @param aCode the a code
	 */
	private BusinessContextEnum(String aCode, LineOfBusinessEnum lineOfBusiness, String applicationId) {
		this.code = aCode;
		this.lineOfBusiness = lineOfBusiness;
		this.applicationId = applicationId;
	}

	/** The code. */
	private String code = null;

	/**
	 * Gets the code.
	 * 
	 * @return the code
	 */
	public String getCode() {
		return this.code;
	}
	
	public String getApplicationId() {
		return applicationId;
	}
	
	public LineOfBusinessEnum getLineOfBusiness() {
		return lineOfBusiness;
	}


	/**
	 * Value of code.
	 * 
	 * @param value the value
	 * 
	 * @return the line of business code enum
	 */
	public static BusinessContextEnum valueOfCode(String value) {

		if (StringUtils.isEmpty(value)) {
			return null;
		}

		for (BusinessContextEnum v : values()) {
			if (v.code.equals(value)) {
				return v;
			}

		}

		throw new IllegalArgumentException("no enum value found for code: " + value);

	}	
	
	/**
	 * Value of code.
	 * 
	 * @param value the value
	 * 
	 * @return the line of business code enum
	 */
	public static BusinessContextEnum valuefromString(String lineOfBusiness, String applicationId) {

		if (applicationId == null)
			return valueOfCode(LineOfBusinessUtils.toStringValue(lineOfBusiness));
		
		return valueOfCode("%s_%s".formatted(LineOfBusinessUtils.toStringValue(lineOfBusiness), applicationId.toUpperCase()));
	}
}
