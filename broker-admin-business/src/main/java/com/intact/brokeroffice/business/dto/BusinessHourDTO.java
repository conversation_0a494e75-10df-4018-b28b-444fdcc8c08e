package com.intact.brokeroffice.business.dto;

import com.intact.tools.comparison.domain.Ignored;

public class BusinessHourDTO {

	private int dayOfWeek = 0;
	private String openHour = null;
	private String closeHour = null;
	
	public BusinessHourDTO() {
	}
	
	@Ignored
	public int getDayOfWeek() {
		return this.dayOfWeek;
	}
	public void setDayOfWeek(int dayOfWeek) {
		this.dayOfWeek = dayOfWeek;
	}
	public String getOpenHour() {
		return this.openHour;
	}
	public void setOpenHour(String openHour) {
		this.openHour = openHour;
	}
	public String getCloseHour() {
		return this.closeHour;
	}
	public void setCloseHour(String closeHour) {
		this.closeHour = closeHour;
	}
	
	public String toString() {
		return "[day of week=" + this.getDayOfWeek() + ", open hour=" + this.getOpenHour() + ", close hour" + this.getCloseHour() + "]";
	}
}
