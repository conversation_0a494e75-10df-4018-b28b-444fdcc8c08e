/**
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance 
 * Copyright (c) 2010, Intact Insurance, All rights reserved.<br>
 */
package com.intact.brokeroffice.business.subbrokers;

import java.util.List;

import com.ing.canada.cif.domain.ISubBrokers;
import com.ing.canada.cif.domain.enums.ApplicationIdEnum;
import com.ing.canada.cif.domain.enums.LineOfBusinessEnum;

public interface ISubBrokersBusinessProcess {
	
	/**
	 * Save a aub broker in CIF database.
	 * 
	 * @param aSubBrokers the a sub brokers
	 */
	void save(ISubBrokers aSubBrokers);
	
	/**
	 * Update a sub broker in CIF database.
	 * 
	 * @param aSubBrokers the a sub brokers
	 */
	void update(ISubBrokers aSubBrokers);
	
	/**
	 * Gets the list of web sub brokers.
	 * 
	 * BR5828 - Items Displayed per Regional Access : 
 	 * The items displayed in the various lists and search results (i.e.: List of Quotes, 
 	 * List of Users, Point of Sale List, etc.) will be limited to the items available 
 	 * for the current region of the user. 
	 * 
	 * @param companyNumber   : The company number(A, 3, etc)
	 * @param aProvinceCode   : The province (ON, QC, etc)
	 * @param apps   : The list of application IDs for which to get the subbrokers
	 * @param lob   : The list of lines of business for which to get the subbrokers
	 * @return the web sub brokers
	 */
	List<ISubBrokers> getWebSubBrokers(String companyNumber, List<ApplicationIdEnum> apps, List<LineOfBusinessEnum> lobs);		
	
	/**
	 * Gets the list of web sub brokers without image.
	 * 
	 * BR5828 - Items Displayed per Regional Access : 
 	 * The items displayed in the various lists and search results (i.e.: List of Quotes, 
 	 * List of Users, Point of Sale List, etc.) will be limited to the items available 
 	 * for the current region of the user. 
	 * 
	 * @param availableMasters : The list of master brokers 
	 * @param companyNumber   : The company number(A, 3, etc)
	 * @param aProvinceCode   : The province (ON, QC, etc)
	 * @param apps   : The list of application IDs for which to get the subbrokers
	 * @param lob   : The list of Lines of business for which to get the subbrokers
	 * @return the web sub brokers
	 */
	List<ISubBrokers> getWebSubBrokers(List<String> availableMasters, String companyNumber, List<ApplicationIdEnum> apps, List<LineOfBusinessEnum> lobs);	
		
	List<ISubBrokers> getAllWebSubBrokers(String companyNumber, String aBrokerCompanyNbr);
	
	/**
	 * Gets the list of web sub brokers.
	 * 
	 * BR5828 - Items Displayed per Regional Access : 
 	 * The items displayed in the various lists and search results (i.e.: List of Quotes, 
 	 * List of Users, Point of Sale List, etc.) will be limited to the items available 
 	 * for the current region of the user.
	 * BR5774  Display Point of Sale in List
	 * 
	 * @param companyNumber     : The company number(A, 3, etc)
	 * @param brokerCompanyNumber : GC, HAL, WU
	 * @param masterBrokerNbr	: the master broker number (99999)
	 * @param aProvinceCode 	: The province (ON, QC, etc)
	 * @param adminRole
	 * @param apps   : The list of application IDs for which to get the subbrokers
	 * @param lob   : The list of Lines of business for which to get the subbrokers
	 * @return the web sub brokers
	 */
	List<ISubBrokers> getWebSubBrokers(String companyNumber, String brokerCompanyNumber, String masterBrokerNbr,  boolean adminRole, 
			List<ApplicationIdEnum> apps, List<LineOfBusinessEnum> lobs);
	
	
	/**
	 * Gets a specific sub broker from CIF database.
	 * 
	 * @param brokerId the broker id
	 * 
	 * @return the sub broker by id
	 */
	ISubBrokers getSubBrokerByID(Long brokerId);
	
	/**
	 * checks if this point of sale (subbroker) is not mapped to at least one FSA 
	 * @param subBrokerId The Id of the subBroker
	 * @param applicationId The application Id for which to do the verification
	 * @param lineOfBusiness The line of business for which to do the verification
	 * @return true if subbroker is not mapped else return false	
	 */
	boolean verifyPosUnassignedToFsa(long subBrokerId, ApplicationIdEnum applicationId, LineOfBusinessEnum lineOfBusiness);
	
	/**
	 * checks if this point of sale (subbroker) is not assigned to at least one Quote 
	 *
	 * @param aSubbrokerId The Id of the subBroker
	 * @param applicationId The application Id for which to do the verification
	 * @param lineOfBusiness The line of business for which to do the verification
	 * @return true if subBroker is not mapped else return false
	 */
	boolean verifyPosUnassignedToQuote(long aSubbrokerId, ApplicationIdEnum applicationId, LineOfBusinessEnum lineOfBusiness);
	
}
