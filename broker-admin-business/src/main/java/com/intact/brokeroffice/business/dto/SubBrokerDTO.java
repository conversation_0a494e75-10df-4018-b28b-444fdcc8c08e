/**
 * Important notice: This software is the soldisplayedEnglishBrokerageImagee property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance 
 * Copyright (c) 2010, Intact Insurance, All rights reserved.<br>
 */
package com.intact.brokeroffice.business.dto;

import java.util.HashMap;
import java.util.Map;

import com.intact.tools.comparison.domain.Ignored;

/**
 * The Class SubBrokerBean.
 */
public class SubBrokerDTO {

	private long subBrokerId = 0;
	private String name = null;
	private String onlineName = null;
	private String subBrokerNo = null;
	private String city = null;
	private String address1 = null;
	private String address2 = null;
	private String phoneNumber = null;
	private String emailAddress = null;
	private String url = null;
	private String province = null;
	private Boolean allowCallback = null;
	private String masterOwnerNo = null;
	private Boolean displayBrokerAddress = null;
	private Boolean displayIntactAddress = null;
	private Map<String,ServiceDTO> services = null;
	private Map<String,LineOfBusinessDTO> linesOfBusiness = null;
	private Map<String, ImageDTO> images = null;
	private Map<String, BusinessHourDTO> businessHours = null;

	public SubBrokerDTO() {
		this.setImages(new HashMap<String, ImageDTO>());
		this.setBusinessHours(new HashMap<String, BusinessHourDTO>());
	}

	public SubBrokerDTO(String subBrokerNumber, long subBrokerId, String name, String city, String province) {
		this();
		this.setSubBrokerNo(subBrokerNumber);
		this.setId(subBrokerId);
		this.setName(name);
		this.setCity(city);
		this.setProvince(province);
	}

	@Ignored
	public long getId() {
		return this.subBrokerId;
	}

	public void setId(long subBrokerId) {
		this.subBrokerId = subBrokerId;
	}

	@Ignored
	public String getName() {
		return this.name;
	}

	public void setName(String aName) {
		this.name = aName;
	}

	public String getOnlineName() {
		return this.onlineName;
	}

	public void setOnlineName(String onlineName) {
		this.onlineName = onlineName;
	}

	@Ignored
	public String getSubBrokerNo() {
		return this.subBrokerNo;
	}

	public void setSubBrokerNo(String aSubBrokerNo) {
		this.subBrokerNo = aSubBrokerNo;
	}

	public String getEmailAddress() {
		return emailAddress;
	}

	public void setEmailAddress(String emailAddress) {
		this.emailAddress = emailAddress;
	}

	public Boolean getAllowCallback() {
		return allowCallback;
	}

	public void setAllowCallback(Boolean allowCallback) {
		this.allowCallback = allowCallback;
	}

	@Ignored
	public String getCity() {
		return this.city;
	}

	public void setCity(String aCity) {
		this.city = aCity;
	}

	@Ignored
	public String getAddress1() {
		return this.address1;
	}

	public void setAddress1(String aAddress1) {
		this.address1 = aAddress1;
	}

	@Ignored
	public String getAddress2() {
		return this.address2;
	}

	public void setAddress2(String aAddress2) {
		this.address2 = aAddress2;
	}

	public Boolean getDisplayBrokerAddress() {
		return this.displayBrokerAddress;
	}

	public void setDisplayBrokerAddress(Boolean displayBrokerAddress) {
		this.displayBrokerAddress = displayBrokerAddress;
	}

	public Boolean getDisplayIntactAddress() {
		return this.displayIntactAddress;
	}

	public void setDisplayIntactAddress(Boolean displayIntactAddress) {
		this.displayIntactAddress = displayIntactAddress;
	}

	public String getUrl() {
		return this.url;
	}

	public void setUrl(String aUrl) {
		this.url = aUrl;
	}

	public String getPhoneNumber() {
		return this.phoneNumber;
	}

	public void setPhoneNumber(String aPhoneNumber) {
		this.phoneNumber = aPhoneNumber;
	}

	@Ignored
	public String getProvince() {
		return this.province;
	}

	public void setProvince(String aProvince) {
		this.province = aProvince;
	}

	public Map<String,LineOfBusinessDTO> getLinesOfBusiness() {
		return this.linesOfBusiness;
	}

	public void setLinesOfBusiness(Map<String,LineOfBusinessDTO> linesOfBusiness) {
		this.linesOfBusiness = linesOfBusiness;
	}

	public Map<String, ServiceDTO> getServices() {
		return this.services;
	}

	public void setServices(Map<String, ServiceDTO> services) {
		this.services = services;
	}

	public Map<String, ImageDTO> getImages() {
		return this.images;
	}

	public void setImages(Map<String, ImageDTO> images) {
		this.images = images;
	}

	public Map<String, BusinessHourDTO> getBusinessHours() {
		return businessHours;
	}

	public void setBusinessHours(Map<String, BusinessHourDTO> businessHours) {
		this.businessHours = businessHours;
	}

	@Ignored
	public String getMasterOwnerNo() {
		return this.masterOwnerNo;
	}

	public void setMasterOwnerNo(String masterOwnerNo) {
		this.masterOwnerNo = masterOwnerNo;
	}
	
	public String toString() {
		return "[id=" + this.getId() + ", sub broker no=" + this.getSubBrokerNo() + ", name=" + this.getName() + "]";
	}
}