package com.intact.brokeroffice.business.fsa;

import java.io.InputStream;
import java.util.Date;

import com.ing.canada.plp.domain.enums.LineOfBusinessCodeEnum;

public class FsaUploadDTO {

	/** the file to upload */
	private InputStream inputStream;

	/** The file path */
	private String filePath;

	/** The file name */
	private String fileName;

	/** The province to which the file is attributed*/ 
	private String province;

	/** The {@link LineOfBusinessCodeEnum} to which the file is attributed*/
	private String lineOfBusiness;

	/** Current date (at object instantiation) */
	private Date date;

	/** Time limit to abort the upload process. (30 minutes, default value)*/
	private Long timeLimit;
	
	private String companyCode;
	
	private String applicationId;
	
	/**
	 * Constructor (default)
	 */
	public FsaUploadDTO() {
		date = new Date();
		timeLimit = 30L;
	}

	/**
	 * @return the inputStream
	 */
	public InputStream getInputStream() {
		return inputStream;
	}

	/**
	 * @param inputStream the inputStream to set
	 */
	public void setInputStream(InputStream inputStream) {
		this.inputStream = inputStream;
	}

	/**
	 * @return the filePath
	 */
	public String getFilePath() {
		return filePath;
	}

	/**
	 * @param filePath the filePath to set
	 */
	public void setFilePath(String filePath) {
		this.filePath = filePath;
	}

	/**
	 * @return the fileName
	 */
	public String getFileName() {
		return fileName;
	}

	/**
	 * @param fileName the fileName to set
	 */
	public void setFileName(String fileName) {
		this.fileName = fileName;
	}

	/**
	 * @return the province
	 */
	public String getProvince() {
		return province;
	}

	/**
	 * @param province the province to set
	 */
	public void setProvince(String province) {
		this.province = province;
	}

	/**
	 * @return the date
	 */
	public Date getDate() {
		return date;
	}

	/**
	 * @param date the date to set
	 */
	public void setDate(Date date) {
		this.date = date;
	}

	/**
	 * @return the lineOfBusiness
	 */
	public String getLineOfBusiness() {
		return lineOfBusiness;
	}

	/**
	 * @param lineOfBusiness the lineOfBusiness to set
	 */
	public void setLineOfBusiness(String lineOfBusiness) {
		this.lineOfBusiness = lineOfBusiness;
	}

	/**
	 * @return the timeLimit
	 */
	public Long getTimeLimit() {
		return timeLimit;
	}

	/**
	 * @param timeLimit the timeLimit to set
	 */
	public void setTimeLimit(Long timeLimit) {
		this.timeLimit = timeLimit;
	}
	
	/**
	 * @return the company code number
	 */
	public String getCompanyCode() {
		return this.companyCode;
	}

	/**
	 * @param company code number to set
	 */
	public void setCompanyCode(String companyCode) {
		this.companyCode = companyCode;
	}
	
	/**
	 * @return the application id
	 */
	public String getApplicationId() {
		return applicationId;
	}

	/**
	 * @param applicationId the application ID to set
	 */
	public void setApplicationId(String applicationId) {
		this.applicationId = applicationId;
	}
}
