package com.intact.brokeroffice.business.dto;

import com.intact.tools.comparison.domain.Ignored;

public class PhoneDTO {

	private String usage = null;
	private String brokerPhone = null;
	private String intactPhone = null;
  private String phone = null;
	private Boolean intactTollFree = false;
	private Boolean brokerTollFree = false;
  private Boolean tollFree = false;

	public PhoneDTO() {
	}

	@Ignored
	public String getUsage() {
		return usage;
	}

	public void setUsage(String usage) {
		this.usage = usage;
	}

	public String getBrokerPhone() {
		return brokerPhone;
	}

	public void setBrokerPhone(String brokerPhone) {
		this.brokerPhone = brokerPhone;
	}

	public String getIntactPhone() {
		return intactPhone;
	}

	public void setIntactPhone(String intactPhone) {
		this.intactPhone = intactPhone;
	}

  public String getPhone() {
    return phone;
  }

  public void setPhone(String phone) {
    this.phone = phone;
  }

	public Boolean getIntactTollFree() {
		return this.intactTollFree;
	}
	
	public void setIntactTollFree(Boolean intactTollFree) {
		this.intactTollFree = intactTollFree;
	}

	public Boolean getBrokerTollFree() {
		return this.brokerTollFree;
	}

	public void setBrokerTollFree(Boolean brokerTollFree) {
		this.brokerTollFree = brokerTollFree;
	}

  public Boolean getTollFree() {
    return this.tollFree;
  }

  public void setTollFree(Boolean tollFree) {
    this.tollFree = tollFree;
  }

  public String toString() {
		return "[usage=" + this.getUsage() + ", broker phone=" + this.getBrokerPhone() + ", broker toll free=" + this.getBrokerTollFree() + ", intact phone=" + this.getIntactPhone() + ", intact toll free=" + this.getIntactTollFree() + "]";
	}
}
