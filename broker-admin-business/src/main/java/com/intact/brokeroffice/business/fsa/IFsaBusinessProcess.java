package com.intact.brokeroffice.business.fsa;

import java.io.IOException;
import java.util.List;

import com.ing.canada.cif.domain.IFsaLoad;
import com.ing.canada.cif.domain.IFsaMessage;
import com.ing.canada.cif.domain.impl.FsaParameters;

public interface IFsaBusinessProcess {
	
	/**
	 * Pre-load the fsa file
	 * @param fsaUploadDTO {@link FsaUploadDTO} encapsulating all parameters regarding a particular fsa file to be uploaded
	 * @return the generated id
	 * @throws IOException In case of I/O error(s)
	 */
	Long preLoadingFsaProcess(FsaUploadDTO fsaUploadDTO) throws IOException;

	/**
	 * Loading the fsa
	 * @param fsaParameters {@link FsaParameters} encapsulating all parameters regarding a particular fsa file
	 */
	void loadingFsaProcess(final FsaParameters fsaParameters);
   	
	/**
	 * Get all fsa messages
	 * @param fsaParameters {@link FsaParameters} encapsulating all parameters regarding a particular list of {@link IFsaMessage fsa messages}
	 * @return All {@link IFsaMessage fsa messages}
	 */
	List<IFsaMessage> getFsaMessagesList(final FsaParameters fsaParameters);

	/**
	 * @param fsaParameters {@link FsaParameters} encapsulating all parameters regarding fsa file(s)
	 * @return All {@link IFsaLoad fsa load beans}
	 */
	List<IFsaLoad> getFsaTableList(final FsaParameters fsaParameters);
	
	/**
	 * Execute the upload operation without a thread
	 * @param uploadDTO {@link FsaUploadDTO} encapsulating all parameters regarding a particular fsa file to be uploaded
	 * @throws IOException
	 */
	void executeWithoutThread(final FsaUploadDTO uploadDTO) throws IOException;

	/**
	 * Abort the process based on the provided list of {@link Long loadIds}
	 * @param loadIds the {@link Long loadIds} to be processed
	 */
	void abortProcesses(List<Long> loadIds);
}
