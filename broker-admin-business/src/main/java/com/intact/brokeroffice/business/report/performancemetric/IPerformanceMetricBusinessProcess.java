package com.intact.brokeroffice.business.report.performancemetric;

import java.util.Date;
import java.util.List;

import com.ing.canada.plp.domain.enums.ManufacturerCompanyCodeEnum;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.ing.canada.plp.report.performancemetric.PerformanceMetricInfo;

public interface IPerformanceMetricBusinessProcess {
	
	/**
	 * Find all the performance metric info 
	 *	 
	 * @param aDateFrom
	 * @param aDateTo
	 * @param aQuoteSource
	 * @param aProvinceCode
	 * @param aSubBrokerCompanyNumber
	 * @param aManufacturerCompanyCode
	 * 
	 * @return list of performance metric list info
	 */
	List<PerformanceMetricInfo> getPerformanceMetricListSP(Date aDateFrom, Date aDateTo, String aQuoteSource, ProvinceCodeEnum aProvinceCode, String aSubBrokerCompanyNumber, ManufacturerCompanyCodeEnum aManufacturerCompanyCode);

}
