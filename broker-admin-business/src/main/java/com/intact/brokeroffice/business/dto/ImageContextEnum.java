package com.intact.brokeroffice.business.dto;

import java.util.HashMap;
import java.util.Map;

import com.ing.canada.cif.domain.enums.ImageMimeTypeEnum;
import com.ing.canada.cif.domain.enums.ImageNatureEnum;
import com.ing.canada.cif.domain.enums.ImageUsageEnum;
import com.ing.canada.cif.domain.enums.LanguageEnum;

public enum ImageContextEnum {
	
	LOGO_EN_SVG_UI("LOGO", "EN", "SVG", "UI"),
	LOGO_FR_SVG_UI("LOGO", "FR", "SVG", "UI"),
	LOGO_EN_OTHER_UI("LOGO", "EN", "", "UI"),
	LOGO_FR_OTHER_UI("LOGO", "FR", "", "UI"),
	LOGO_EN_OTHER_PRINTING("LOGO", "EN", "", "PR"),
	LOGO_FR_OTHER_PRINTING("LOGO", "FR", "", "PR");
	
	private ImageNatureEnum nature = null;
	private LanguageEnum language = null;
	private ImageMimeTypeEnum type = null;
	private ImageUsageEnum usage = null;
	private String code = null;
	
	protected static Map<String, ImageContextEnum> contexts = null;
	
	private ImageContextEnum(String nature, String language, String type, String usage) {
		this.setNature(ImageNatureEnum.valueOfCode(nature));
		this.setLanguage(LanguageEnum.valueOfCode(language));
		this.setType(ImageMimeTypeEnum.valueOfCode(type));
		this.setUsage(ImageUsageEnum.valueOfCode(usage));
		this.setCode(nature + "-" + language + "-" + type + "-" + usage);
	}

	public ImageNatureEnum getNature() {
		return nature;
	}

	public void setNature(ImageNatureEnum nature) {
		this.nature = nature;
	}

	public LanguageEnum getLanguage() {
		return this.language;
	}

	public void setLanguage(LanguageEnum language) {
		this.language = language;
	}

	public ImageMimeTypeEnum getType() {
		return this.type;
	}

	public void setType(ImageMimeTypeEnum type) {
		this.type = type;
	}

	public ImageUsageEnum getUsage() {
		return this.usage;
	}

	public void setUsage(ImageUsageEnum usage) {
		this.usage = usage;
	}
	
	public String getCode() {
		return this.code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public static ImageContextEnum fromContext(String context) {
		if (ImageContextEnum.contexts == null) {
			Map<String, ImageContextEnum> newContexts = new HashMap<String, ImageContextEnum>();

			for (ImageContextEnum temp : ImageContextEnum.values()) {
				newContexts.put(temp.getCode(), temp);
			}

			ImageContextEnum.contexts = newContexts;
		}

		return ImageContextEnum.contexts.get(context);
	}

	public static ImageContextEnum fromContext(String nature, String language, String type,
			String usage) {
		return ImageContextEnum.fromContext(nature + "-" + language + "-" + (type == null || !type.equals("SVG") ? "" : "SVG") + "-" + usage);
	}

}
