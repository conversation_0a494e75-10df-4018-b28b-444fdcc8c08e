package com.intact.brokeroffice.business.fsa.impl;

import java.io.File;
import java.io.IOException;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.ing.canada.cif.domain.IFsaLoad;
import com.ing.canada.cif.domain.IFsaMessage;
import com.ing.canada.cif.domain.enums.FsaLoadStatusCodeEnum;
import com.ing.canada.cif.domain.impl.FsaLoad;
import com.ing.canada.cif.domain.impl.FsaParameters;
import com.ing.canada.cif.service.IFsaLoadService;
import com.intact.brokeroffice.business.fsa.FsaUploadDTO;
import com.intact.brokeroffice.business.fsa.IFsaBusinessProcess;

@Component
public class FsaBusinessProcess implements IFsaBusinessProcess {

	private static Logger logger = LoggerFactory.getLogger(FsaBusinessProcess.class);

	@Autowired
	private IFsaLoadService fsaLoadService;

	private static final String CONTROL_FILE_EXSTENSION = ".ctl";

	/***
	 * <AUTHOR> selleing
	 * @see IFsaBusinessProcess#loadingFsaProcess
	 */
	@Override
	public void loadingFsaProcess(final FsaParameters fsaParameters) {
		this.fsaLoadService.loadingFsaProcess(fsaParameters);
	}

	/**
	 * <AUTHOR> selleing
	 * @see IFsaBusinessProcess#getFsaMessagesList(Long)
	 */

	@Override
	public List<IFsaMessage> getFsaMessagesList(final FsaParameters fsaParameters) {
		return this.fsaLoadService.getFsaMessagesList(fsaParameters);
	}

	/**
	 * @see IFsaBusinessProcess#getFsaTableList(String)
	 */
	@Override
	public List<IFsaLoad> getFsaTableList(final FsaParameters fsaParameters) {
		return this.fsaLoadService.getFsaTableList(fsaParameters);
	}

	@Override
	public Long preLoadingFsaProcess(FsaUploadDTO fsaUploadDTO) throws IOException {
		FsaLoad anObject = new FsaLoad();

		anObject.setFileName(fsaUploadDTO.getFileName());
		anObject.setProvinceCd(fsaUploadDTO.getProvince());
		anObject.setStartDate(fsaUploadDTO.getDate());
		anObject.setStatusCd(FsaLoadStatusCodeEnum.PROCESSING.getCode());
		anObject.setContent(fsaUploadDTO.getInputStream());
		anObject.setLineOfBusiness(fsaUploadDTO.getLineOfBusiness());
		anObject.setApplicationId(fsaUploadDTO.getApplicationId());
		anObject.setCompanyCode(fsaUploadDTO.getCompanyCode());

		this.fsaLoadService.save(anObject);

		return anObject.getLoadId();
	}

	@Override
	public void executeWithoutThread(FsaUploadDTO uploadDTO) throws IOException {
		Long loadId = preLoadingFsaProcess(uploadDTO);
		createFileCTL(uploadDTO.getFilePath(), loadId);
	}

	/**
	 * Create a ctl file
	 * @param nfsCompletePath the complete nfs path
	 * @param aLoadId the id to be used in creating the ctl file
	 */
	private void createFileCTL(String nfsCompletePath, Long aLoadId) {
		File newFile = new File(nfsCompletePath, String.valueOf(aLoadId) + CONTROL_FILE_EXSTENSION);

		// if CTL FILE does not exist, then create it loadID + '.CTL'
		if (!newFile.exists()) {
			try {
				newFile.createNewFile();
			} catch (IOException e) {
				logger.error(e.getMessage());
				logger.debug(ExceptionUtils.getStackTrace(e));
			}
		}
	}

	public void abortProcesses(List<Long> loadIds) {
		IFsaLoad anObject = null;
		for (Long aLoadId : loadIds) {
			// CHANGE FSA LOAD STATUS TO ABORTED
			anObject = this.fsaLoadService.findById(aLoadId);
			anObject.setStatusCd(FsaLoadStatusCodeEnum.ABORTED.getCode());
			anObject.setEndDate(new Date());

			this.fsaLoadService.update(anObject);
		}
	}
	
}
