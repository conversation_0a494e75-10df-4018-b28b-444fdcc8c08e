package com.intact.brokeroffice.business.report.subbrokerassignment;

import java.util.Date;
import java.util.List;

import com.ing.canada.plp.domain.enums.ManufacturerCompanyCodeEnum;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.ing.canada.plp.report.SubBrokerAssignmentInfo;

public interface ISubBrokerAssignmentBusinessProcess {
	
	/**
	 * Find all the sub broker assignments from aDateFrom to a DateTo 
	 * 
	 * @param Date aDateFrom
	 * @param Date aDateTo
	 * 
	 * @return list of sub broker assignments info
	 */	
	List<SubBrokerAssignmentInfo> getAllSubBrokerAssignmentsSP(Date aDateFrom, Date aDateTo,
                                                             ProvinceCodeEnum aProvinceCode, String aSubBrokerCompanyNumber,
                                                             ManufacturerCompanyCodeEnum aManufacturerCompanyCode);

}
