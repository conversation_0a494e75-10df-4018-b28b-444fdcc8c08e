/**
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance
 * Copyright (c) 2010, Intact Insurance, All rights reserved.<br>
 */
package com.intact.brokeroffice.business.subbrokers.impl;

import com.ing.canada.cif.domain.ISubBrokers;
import com.ing.canada.cif.domain.enums.ApplicationIdEnum;
import com.ing.canada.cif.domain.enums.LineOfBusinessEnum;
import com.ing.canada.cif.service.ISubBrokersService;
import com.intact.brokeroffice.business.subbrokers.ISubBrokersBusinessProcess;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Business class to cover the sub broker page of the application
 */
@Component
public class SubBrokersBusinessProcess implements ISubBrokersBusinessProcess {

	@Autowired
	private ISubBrokersService subBrokersService;

	/**
	 * @see ISubBrokersBusinessProcess#save(com.ing.canada.cif.domain.ISubBrokers)
	 */

	public void save(ISubBrokers aSubBrokers) {
		this.subBrokersService.save(aSubBrokers);
	}

	/**
	 * @see ISubBrokersBusinessProcess#update(com.ing.canada.cif.domain.ISubBrokers)
	 */
	@Transactional
	public void update(ISubBrokers broker) {
		this.subBrokersService.update(broker);
	}

	/**
	 * @see ISubBrokersBusinessProcess#getWebSubBrokers(String)
	 */
	public List<ISubBrokers> getWebSubBrokers(String companyNumber, List<ApplicationIdEnum> apps, List<LineOfBusinessEnum> lobs) {
		return this.subBrokersService.getWebSubBrokers(companyNumber, null, apps, lobs);
	}

	public List<ISubBrokers> getWebSubBrokers(List<String> availableMasters, String companyNumber, List<ApplicationIdEnum> apps, List<LineOfBusinessEnum> lobs) {
		return this.subBrokersService.getWebSubBrokers(availableMasters, companyNumber, apps, lobs);
	}

	public List<ISubBrokers> getAllWebSubBrokers(String companyNumber, String aBrokerCompanyNbr) {
		return this.subBrokersService.getAllWebSubBrokers(companyNumber, aBrokerCompanyNbr);
	}

	/**
	 * @see ISubBrokersBusinessProcess#getSubBrokerByID(Long)
	 */
	public ISubBrokers getSubBrokerByID(Long subBrokerId) {
		return this.subBrokersService.getSubBrokerById(subBrokerId);
	}

	/**
	 * (non-Javadoc)
	 *
	 * @see ISubBrokersBusinessProcess#getWebSubBrokers(String,
	 *      String, String, String, boolean)
	 */
	public List<ISubBrokers> getWebSubBrokers(String companyNumber, String brokerCompanyNumber, String masterBrokerNbr, boolean adminRole,
			List<ApplicationIdEnum> apps, List<LineOfBusinessEnum> lobs) {
		return this.subBrokersService.getWebSubBrokers(companyNumber, brokerCompanyNumber, masterBrokerNbr, null, adminRole, apps, lobs);
	}

	/**
	 * @see ISubBrokersBusinessProcess#verifyPosUnassignedToFsa
	 */
	public boolean verifyPosUnassignedToFsa(long subBrokerId, ApplicationIdEnum applicationId, LineOfBusinessEnum lineOfBusiness) {
		return this.subBrokersService.verifyPosUnassignedToFsa(subBrokerId, applicationId, lineOfBusiness);
	}

	/**
	 * @see ISubBrokersBusinessProcess#verifyPosUnassignedToQuote(long, ApplicationIdEnum, LineOfBusinessEnum)
	 */
	public boolean verifyPosUnassignedToQuote(long aSubbrokerId, ApplicationIdEnum applicationId, LineOfBusinessEnum lineOfBusiness) {
		return this.subBrokersService.verifyPosUnassignedToQuoteNationwide(aSubbrokerId, applicationId, lineOfBusiness);
	}
}
