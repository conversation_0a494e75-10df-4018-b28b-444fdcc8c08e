package com.intact.brokeroffice.business.dto;

import java.util.HashMap;
import java.util.Map;

public enum UsageEnum {

	DEFAULT(""), 
	OFFER("OFFPG"),
	URL("URL"),
	EMAIL("EMAIL");
	
	
	private static Map<String, UsageEnum> usages = null;

	private String code = null;

	private UsageEnum(String code) {
		this.setCode(code);
	}

	public String getCode() {
		return this.code;
	}

	protected void setCode(String code) {
		this.code = code;
	}
	
	public static UsageEnum fromUsage(String usage) {
		if (UsageEnum.usages == null) {
			Map<String, UsageEnum> newUsages = new HashMap<String, UsageEnum>();

			for (UsageEnum temp : UsageEnum.values()) {
				newUsages.put(temp.getCode(), temp);
			}

			UsageEnum.usages = newUsages;
		}

		return UsageEnum.usages.get(usage);
	}
}
