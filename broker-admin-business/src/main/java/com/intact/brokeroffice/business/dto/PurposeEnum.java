package com.intact.brokeroffice.business.dto;

import java.util.HashMap;
import java.util.Map;

public enum PurposeEnum {

	DEFAULT(null), 
	CHAT_ID("7");
	
	
	private static Map<String, PurposeEnum> usages = null;

	private String code = null;

	private PurposeEnum(String code) {
		this.setCode(code);
	}

	public String getCode() {
		return this.code;
	}

	protected void setCode(String code) {
		this.code = code;
	}
	
	public static PurposeEnum fromUsage(String usage) {
		if (PurposeEnum.usages == null) {
			Map<String, PurposeEnum> newUsages = new HashMap<String, PurposeEnum>();

			for (PurposeEnum temp : PurposeEnum.values()) {
				newUsages.put(temp.getCode(), temp);
			}

			PurposeEnum.usages = newUsages;
		}

		return PurposeEnum.usages.get(usage);
	}
}
