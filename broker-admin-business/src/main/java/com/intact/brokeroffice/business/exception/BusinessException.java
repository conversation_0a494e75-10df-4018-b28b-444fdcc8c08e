package com.intact.brokeroffice.business.exception;

import java.io.Serial;
import java.text.MessageFormat;
import java.util.Map;

public class BusinessException extends Exception {

	@Serial
	private static final long serialVersionUID = 1L;

	private static Map<String, String> ERRORS = null;

	private String exceptionCode = null;

	public BusinessException() {
		super();
	}

	public BusinessException(String exceptionCode, Object... objects) {
		this(exceptionCode, null, objects);
	}

	public BusinessException(String exceptionCode, Exception exception, Object... objects) {

		super(BrokerException.getErrorMessage(exceptionCode, objects), exception);
		this.setExceptionCode(exceptionCode);
	}

	public static final Map<String, String> getErrors() {
		return BusinessException.ERRORS;
	}

	public final void setErrors(Map<String, String> errors) {
		BusinessException.ERRORS = errors;
	}

	public final String getExceptionCode() {
		return this.exceptionCode;
	}

	public final void setExceptionCode(String exceptionCode) {
		this.exceptionCode = exceptionCode;
	}

	protected static String getErrorMessage(String exceptionCode, Object... objects) {

		String errorMessage = null;

		if (BrokerException.getErrors() != null && BrokerException.getErrors().get(exceptionCode) != null) {

			errorMessage = MessageFormat.format(BrokerException.getErrors().get(exceptionCode), objects);
		}

		return errorMessage;
	}

	public String toString() {
		return super.toString() + "[exception code=" + this.getExceptionCode() + ", exception message=" + this.getMessage() + "]";
	}

}
