package com.intact.brokeroffice.business.common;

import java.util.Collection;
import java.util.HashSet;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import com.ing.canada.cif.domain.enums.LineOfBusinessEnum;
import com.ing.canada.plp.domain.enums.LineOfBusinessCodeEnum;

public class LineOfBusinessUtils {

	private static final String JOIN_PARAM = "ß";
	
	/**
	 * Join a collection of {@link LineOfBusinessCodeEnum} with the default join param ["ß"].
	 * (Mostly used to prepare the lineOfBusiness in performing search on stored procedures). 
	 * @param coll A collection of {@link LineOfBusinessCodeEnum}
	 * @return A string 
	 */
	public static String join(Collection<LineOfBusinessEnum> coll) {
		if(CollectionUtils.isEmpty(coll)) {
			return null;
		}

		Collection<String> result = new HashSet<String>(coll.size());
		for(LineOfBusinessEnum entry : coll) {
			if(entry != null) {
				result.add(entry.getCode());
			}
		}

		return StringUtils.join(result, JOIN_PARAM);
	}

	/**
	 * {@link LineOfBusinessUtils#toStringValue(LineOfBusinessCodeEnum)}
	 */
	public static String toStringValue(String lineOfBusiness) {
		return toStringValue(LineOfBusinessEnum.valueOfCode(lineOfBusiness));
	}

	/**
	 * Convert to a string value the line of business passed as a parameter
	 * @param lineOfBusiness Line of business from which to derive the string value
	 * @return <ul>
	 * 				<li>{@link Converter#PERSONAL.code} for personal</li>
	 * 				<li>{@link Converter#COMMERCIAL.code} for commercial</li>
	 *  	   </ul>
	 */
	public static String toStringValue(LineOfBusinessEnum lineOfBusiness) {
		return lineOfBusiness == null ? null : Converter.getBy(lineOfBusiness).getCode();
	}
}

/**
 * Enum matching a {@link LineOfBusinessEnum} from cif to its equivalent string representation
 */
enum Converter {
	/** Personal [PL] */
	PERSONAL(LineOfBusinessEnum.PERSONAL_LINE, "PL"),

	/** Commercial [CL] */
	COMMERCIAL(LineOfBusinessEnum.COMMERCIAL_LINE, "CL");
	
	private LineOfBusinessCodeEnum lineOfBusinessCodeEnum;
	private LineOfBusinessEnum lineOfBusinessEnum;
	private String code;

	Converter(/*LineOfBusinessCodeEnum lineOfBusinessCodeEnum,*/ LineOfBusinessEnum lineOfBusinessEnum, String code) {
		//this.lineOfBusinessCodeEnum = lineOfBusinessCodeEnum;
		this.lineOfBusinessEnum = lineOfBusinessEnum;
		this.code = code; 
	}
	
	public LineOfBusinessCodeEnum getLineOfBusinessCodeEnum() {
		return lineOfBusinessCodeEnum;
	}
	
	public LineOfBusinessEnum getLineOfBusinessEnum() {
		return lineOfBusinessEnum;
	}

	public String getCode() {
		return code;
	}

	public static Converter getBy(LineOfBusinessEnum lineOfBusinessEnum) {
		for(Converter c : Converter.values()) {
			if(c.getLineOfBusinessEnum() == lineOfBusinessEnum) {
				return c;
			}
		}

		throw new IllegalArgumentException("Provided line of business [" + lineOfBusinessEnum + "] not supported");
	}
	
}

