package com.intact.brokeroffice.business.clientservice;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.intact.brokeroffice.clientservice.dao.ClientServiceProfilMaster;
import com.intact.brokeroffice.clientservice.service.IClientServiceService;

@Service
public class ClientServiceBusiness implements IClientServiceBusiness {

	@Autowired
	private IClientServiceService clientServiceService;

	@Override
	public String updateProfilMaster(String subBrokerId, Date startDate, String profilMaster) {
		return clientServiceService.updateProfilMaster(subBrokerId, startDate, profilMaster);
	}

	@Override
	public List<ClientServiceProfilMaster> getProfilMasterInfo(String subBrokerId) {
		return clientServiceService.getProfilMasterInfo(subBrokerId);
	}
}
