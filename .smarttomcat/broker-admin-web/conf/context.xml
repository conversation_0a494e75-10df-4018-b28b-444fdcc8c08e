<?xml version="1.0" encoding="UTF-8"?>
<!--
  Licensed to the Apache Software Foundation (ASF) under one or more
  contributor license agreements.  See the NOTICE file distributed with
  this work for additional information regarding copyright ownership.
  The ASF licenses this file to You under the Apache License, Version 2.0
  (the "License"); you may not use this file except in compliance with
  the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
-->
<!-- The contents of this file will be loaded for each web application -->
<Context>

    <!-- Default set of monitored resources. If one of these changes, the    -->
    <!-- web application will be reloaded.                                   -->
    <WatchedResource>WEB-INF/web.xml</WatchedResource>
    <WatchedResource>WEB-INF/tomcat-web.xml</WatchedResource>
    <WatchedResource>${catalina.base}/conf/web.xml</WatchedResource>

    <!-- Uncomment this to enable session persistence across Tomcat restarts -->
    <!--
    <Manager pathname="SESSIONS.ser" />
    -->

    <Resource
        name="jdbc/plpolicy"
        auth="Container"
        type="javax.sql.DataSource"
        maxTotal="100"
        maxIdle="30"
        maxWaitMillis="10000"
        username="PLP_WZONE_INT"
        password="BNrx=k3M87b"
        driverClassName="oracle.jdbc.OracleDriver"
        url="*****************************************************************"/>

    <Resource
        name="jdbc/CI"
        auth="Container"
        type="javax.sql.DataSource"
        maxTotal="100"
        maxIdle="30"
        maxWaitMillis="10000"
        username="CIF_WZONE_INT"
        password="hr1rWT-7GOda1"
        driverClassName="oracle.jdbc.OracleDriver"
        url="*****************************************************************"/>

    <Resource
        name="jdbc/dw"
        auth="Container"
        type="javax.sql.DataSource"
        maxTotal="100"
        maxIdle="30"
        maxWaitMillis="10000"
        username="dw_report_intact1"
        password="tra23por"
        driverClassName="oracle.jdbc.OracleDriver"
        url="*******************************************************"/>

    <Resource
        name="jdbc/brm"
        auth="Container"
        type="javax.sql.DataSource"
        maxTotal="100"
        maxIdle="30"
        maxWaitMillis="10000"
        username="brmadmin"
        password="kanAd$Yc3ia"
        driverClassName="oracle.jdbc.OracleDriver"
        url="***************************************************"/>

    <Resource
        name="jdbc/SAD"
        auth="Container"
        type="javax.sql.DataSource"
        maxTotal="100"
        maxIdle="30"
        maxWaitMillis="10000"
        username="sadusrrw"
        password="93E220_6CA8"
        driverClassName="oracle.jdbc.OracleDriver"
        url="***************************************************************** "/>
</Context>
