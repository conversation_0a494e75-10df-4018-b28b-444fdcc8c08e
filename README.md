# broker-admin-app
[![CI](https://githubifc.iad.ca.inet/lab-se/broker-admin-app/actions/workflows/ci.yml/badge.svg)](https://githubifc.iad.ca.inet/lab-se/broker-admin-app/actions/workflows/ci.yml)
[![Quality Gate Status](https://sonarqube-enterprise.dsso.intactfc.com/sonarqube/api/project_badges/measure?project=broker-admin-app&metric=alert_status&token=sqb_5b8fce6d6a0c3722f5e812fb98312b637542cfbf)](https://sonarqube-enterprise.dsso.intactfc.com/sonarqube/dashboard?id=broker-admin-app)
[![Coverage](https://sonarqube-enterprise.dsso.intactfc.com/sonarqube/api/project_badges/measure?project=broker-admin-app&metric=coverage&token=sqb_5b8fce6d6a0c3722f5e812fb98312b637542cfbf)](https://sonarqube-enterprise.dsso.intactfc.com/sonarqube/dashboard?id=broker-admin-app)
[![Security Rating](https://sonarqube-enterprise.dsso.intactfc.com/sonarqube/api/project_badges/measure?project=broker-admin-app&metric=security_rating&token=sqb_5b8fce6d6a0c3722f5e812fb98312b637542cfbf)](https://sonarqube-enterprise.dsso.intactfc.com/sonarqube/dashboard?id=broker-admin-app)

## Util links

- [Documentation](https://confluence.tooling.intactfc.cloud/display/MARS/Webzone)
- [Test resources](https://confluence.tooling.intactfc.cloud/display/MARS/Resources+Needed)


## Installation:
To run the application locally, run the maven command :
    `mvn clean install -P local`
This will copy the broker-admin-local-config module resources files into the war.
To change any local properties, update them in the broker-admin-local-config module and run the maven command again.

In intellij, create a local tomcat 9 launcher

![img.png](img.png)

![img_1.png](img_1.png)

Access the application locally via the following url:
    `http://localhost:8080/BrokerAdm/timeout.jsf`

## Technical documentation

This project contains the BrokerAdmin application running into tomcat and using GIT Actions.
This project has been copied from https://githubifc.iad.ca.inet/Intact/broker-admin-project.git
The old project was running in websphere. We created a new repository in GIT and moved and cleaned the code
to make it running with tomcat.


