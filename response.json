{
  "expand": "operations,versionedRepresentations,editmeta,changelog,renderedFields",
  "id": "241062",
  "self": "https://jira.dc2.intactlab.ca/rest/api/2/issue/241062",
  "key": "MARS-3085",
  "fields": {
    "issuetype": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/issuetype/10002",
      "id": "10002",
      "description": "The sub-task of the issue",
      "iconUrl": "https://jira.dc2.intactlab.ca/secure/viewavatar?size=xsmall&avatarId=10316&avatarType=issuetype",
      "name": "Sub-task",
      "subtask": true,
      "avatarId": 10316
    },
    "parent": {
      "id": "239729",
      "key": "MARS-2964",
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/issue/239729",
      "fields": {
        "summary": "Goalkeeping PI.2024.4.1",
        "status": {
          "self": "https://jira.dc2.intactlab.ca/rest/api/2/status/10002",
          "description": "",
          "iconUrl": "https://jira.dc2.intactlab.ca/",
          "name": "Done",
          "id": "10002",
          "statusCategory": {
            "self": "https://jira.dc2.intactlab.ca/rest/api/2/statuscategory/3",
            "id": 3,
            "key": "done",
            "colorName": "success",
            "name": "Done"
          }
        },
        "priority": {
          "self": "https://jira.dc2.intactlab.ca/rest/api/2/priority/3",
          "iconUrl": "https://jira.dc2.intactlab.ca/images/icons/priorities/medium.svg",
          "name": "Medium",
          "id": "3"
        },
        "issuetype": {
          "self": "https://jira.dc2.intactlab.ca/rest/api/2/issuetype/10000",
          "id": "10000",
          "description": "Created by Jira Software - do not edit or delete. Issue type for a user story.",
          "iconUrl": "https://jira.dc2.intactlab.ca/images/icons/issuetypes/story.svg",
          "name": "Story",
          "subtask": false
        }
      }
    },
    "timespent": null,
    "customfield_13100": null,
    "project": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/project/14101",
      "id": "14101",
      "key": "MARS",
      "name": "The Martians",
      "projectTypeKey": "software",
      "avatarUrls": {
        "48x48": "https://jira.dc2.intactlab.ca/secure/projectavatar?pid=14101&avatarId=23504",
        "24x24": "https://jira.dc2.intactlab.ca/secure/projectavatar?size=small&pid=14101&avatarId=23504",
        "16x16": "https://jira.dc2.intactlab.ca/secure/projectavatar?size=xsmall&pid=14101&avatarId=23504",
        "32x32": "https://jira.dc2.intactlab.ca/secure/projectavatar?size=medium&pid=14101&avatarId=23504"
      }
    },
    "fixVersions": [
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/version/28478",
        "id": "28478",
        "description": "",
        "name": "Bloom + IRCA  + Auto/QuickQuote CVE Fix - MEP - November 25th",
        "archived": false,
        "released": false,
        "releaseDate": "2024-11-25"
      }
    ],
    "customfield_11001": null,
    "customfield_11002": null,
    "customfield_11200": null,
    "aggregatetimespent": null,
    "resolution": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/resolution/10000",
      "id": "10000",
      "description": "Work has been completed on this issue.",
      "name": "Done"
    },
    "customfield_11201": null,
    "customfield_11003": null,
    "customfield_11202": null,
    "customfield_11004": null,
    "customfield_11203": [],
    "customfield_11005": null,
    "customfield_10700": null,
    "customfield_10900": null,
    "resolutiondate": "2024-10-11T10:27:07.000-0400",
    "workratio": -1,
    "lastViewed": "2024-11-06T09:51:31.630-0500",
    "watches": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/issue/MARS-3085/watchers",
      "watchCount": 2,
      "isWatching": true
    },
    "created": "2024-10-10T14:47:33.000-0400",
    "customfield_10020": null,
    "customfield_12202": null,
    "priority": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/priority/3",
      "iconUrl": "https://jira.dc2.intactlab.ca/images/icons/priorities/medium.svg",
      "name": "Medium",
      "id": "3"
    },
    "customfield_10100": null,
    "customfield_12201": null,
    "customfield_10024": [
      "com.atlassian.greenhopper.service.sprint.Sprint@5ab4e0ce[id=3550,rapidViewId=385,state=CLOSED,name=PI.2024.4.1,startDate=2024-10-03T13:00:00.000-04:00,endDate=2024-10-17T08:00:00.000-04:00,completeDate=2024-10-17T10:54:13.488-04:00,activatedDate=2024-10-03T12:02:38.404-04:00,sequence=3123,goal=1 - Release Lead Sharing in Prod by October 17th (Mid Month release)\n2- Make sure we are ready for the Broker admin release planned october 21st.\n3- Start work on Referal prgram,autoStartStop=false,synced=false]"
    ],
    "customfield_10025": "0|i0vs98:",
    "labels": [
      "Twistlock"
    ],
    "customfield_10026": null,
    "customfield_11700": null,
    "customfield_13008": null,
    "customfield_11900": null,
    "customfield_11902": null,
    "timeestimate": null,
    "aggregatetimeoriginalestimate": null,
    "versions": [],
    "customfield_11901": null,
    "customfield_11906": null,
    "customfield_11905": null,
    "issuelinks": [],
    "assignee": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/user?username=rpmdh249",
      "name": "rpmdh249",
      "key": "JIRAUSER24818",
      "emailAddress": "<EMAIL>",
      "avatarUrls": {
        "48x48": "https://www.gravatar.com/avatar/9e331dcca191645ab52bbed1c8742ef3?d=mm&s=48",
        "24x24": "https://www.gravatar.com/avatar/9e331dcca191645ab52bbed1c8742ef3?d=mm&s=24",
        "16x16": "https://www.gravatar.com/avatar/9e331dcca191645ab52bbed1c8742ef3?d=mm&s=16",
        "32x32": "https://www.gravatar.com/avatar/9e331dcca191645ab52bbed1c8742ef3?d=mm&s=32"
      },
      "displayName": "Omar Bounouioua",
      "active": false,
      "timeZone": "America/New_York"
    },
    "updated": "2024-11-05T15:49:10.000-0500",
    "status": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/status/10002",
      "description": "",
      "iconUrl": "https://jira.dc2.intactlab.ca/",
      "name": "Done",
      "id": "10002",
      "statusCategory": {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/statuscategory/3",
        "id": 3,
        "key": "done",
        "colorName": "success",
        "name": "Done"
      }
    },
    "components": [],
    "timeoriginalestimate": null,
    "customfield_13001": null,
    "description": null,
    "customfield_13003": null,
    "customfield_11022": null,
    "customfield_11100": "{summaryBean=com.atlassian.jira.plugin.devstatus.rest.SummaryBean@7aba0131[summary={pullrequest=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@2afd57ac[overall=PullRequestOverallBean{stateCount=0, state='OPEN', details=PullRequestOverallDetails{openCount=0, mergedCount=0, declinedCount=0}},byInstanceType={}], build=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@11eea797[overall=com.atlassian.jira.plugin.devstatus.summary.beans.BuildOverallBean@516ec231[failedBuildCount=0,successfulBuildCount=0,unknownBuildCount=0,count=0,lastUpdated=<null>,lastUpdatedTimestamp=<null>],byInstanceType={}], review=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@18a523d9[overall=com.atlassian.jira.plugin.devstatus.summary.beans.ReviewsOverallBean@1f07178e[stateCount=0,state=<null>,dueDate=<null>,overDue=false,count=0,lastUpdated=<null>,lastUpdatedTimestamp=<null>],byInstanceType={}], deployment-environment=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@7b31c9e6[overall=com.atlassian.jira.plugin.devstatus.summary.beans.DeploymentOverallBean@5c69f3fb[topEnvironments=[],showProjects=false,successfulCount=0,count=0,lastUpdated=<null>,lastUpdatedTimestamp=<null>],byInstanceType={}], repository=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@6de3a844[overall=com.atlassian.jira.plugin.devstatus.summary.beans.CommitOverallBean@4aff7911[count=0,lastUpdated=<null>,lastUpdatedTimestamp=<null>],byInstanceType={}], branch=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@3f74e751[overall=com.atlassian.jira.plugin.devstatus.summary.beans.BranchOverallBean@2c913d6f[count=0,lastUpdated=<null>,lastUpdatedTimestamp=<null>],byInstanceType={}]},errors=[],configErrors=[]], devSummaryJson={\"cachedValue\":{\"errors\":[],\"configErrors\":[],\"summary\":{\"pullrequest\":{\"overall\":{\"count\":0,\"lastUpdated\":null,\"stateCount\":0,\"state\":\"OPEN\",\"details\":{\"openCount\":0,\"mergedCount\":0,\"declinedCount\":0,\"total\":0},\"open\":true},\"byInstanceType\":{}},\"build\":{\"overall\":{\"count\":0,\"lastUpdated\":null,\"failedBuildCount\":0,\"successfulBuildCount\":0,\"unknownBuildCount\":0},\"byInstanceType\":{}},\"review\":{\"overall\":{\"count\":0,\"lastUpdated\":null,\"stateCount\":0,\"state\":null,\"dueDate\":null,\"overDue\":false,\"completed\":false},\"byInstanceType\":{}},\"deployment-environment\":{\"overall\":{\"count\":0,\"lastUpdated\":null,\"topEnvironments\":[],\"showProjects\":false,\"successfulCount\":0},\"byInstanceType\":{}},\"repository\":{\"overall\":{\"count\":0,\"lastUpdated\":null},\"byInstanceType\":{}},\"branch\":{\"overall\":{\"count\":0,\"lastUpdated\":null},\"byInstanceType\":{}}}},\"isStale\":true}}",
    "customfield_13002": null,
    "customfield_11023": null,
    "customfield_13005": null,
    "customfield_11300": null,
    "customfield_13004": null,
    "customfield_13007": null,
    "customfield_11301": null,
    "customfield_13006": null,
    "customfield_10800": null,
    "customfield_10801": null,
    "aggregatetimeestimate": null,
    "customfield_10802": null,
    "summary": "Twistlocks - autoquote-intact-webservice-web:3.8.82",
    "creator": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/user?username=rpmdh249",
      "name": "rpmdh249",
      "key": "JIRAUSER24818",
      "emailAddress": "<EMAIL>",
      "avatarUrls": {
        "48x48": "https://www.gravatar.com/avatar/9e331dcca191645ab52bbed1c8742ef3?d=mm&s=48",
        "24x24": "https://www.gravatar.com/avatar/9e331dcca191645ab52bbed1c8742ef3?d=mm&s=24",
        "16x16": "https://www.gravatar.com/avatar/9e331dcca191645ab52bbed1c8742ef3?d=mm&s=16",
        "32x32": "https://www.gravatar.com/avatar/9e331dcca191645ab52bbed1c8742ef3?d=mm&s=32"
      },
      "displayName": "Omar Bounouioua",
      "active": false,
      "timeZone": "America/New_York"
    },
    "subtasks": [],
    "reporter": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/user?username=rpmdh249",
      "name": "rpmdh249",
      "key": "JIRAUSER24818",
      "emailAddress": "<EMAIL>",
      "avatarUrls": {
        "48x48": "https://www.gravatar.com/avatar/9e331dcca191645ab52bbed1c8742ef3?d=mm&s=48",
        "24x24": "https://www.gravatar.com/avatar/9e331dcca191645ab52bbed1c8742ef3?d=mm&s=24",
        "16x16": "https://www.gravatar.com/avatar/9e331dcca191645ab52bbed1c8742ef3?d=mm&s=16",
        "32x32": "https://www.gravatar.com/avatar/9e331dcca191645ab52bbed1c8742ef3?d=mm&s=32"
      },
      "displayName": "Omar Bounouioua",
      "active": false,
      "timeZone": "America/New_York"
    },
    "customfield_12100": [
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/10900",
        "value": "Belair",
        "id": "10900",
        "disabled": false
      },
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/10901",
        "value": "BNA",
        "id": "10901",
        "disabled": false
      },
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/10902",
        "value": "Intact",
        "id": "10902",
        "disabled": false
      },
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/12514",
        "value": "Servus",
        "id": "12514",
        "disabled": false
      },
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/12515",
        "value": "Scotia",
        "id": "12515",
        "disabled": false
      }
    ],
    "aggregateprogress": {
      "progress": 0,
      "total": 0
    },
    "customfield_10200": null,
    "customfield_10400": null,
    "customfield_12500": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/11102",
      "value": "Arch_Required",
      "id": "11102",
      "disabled": false
    },
    "customfield_13900": null,
    "customfield_11204": null,
    "customfield_11006": null,
    "customfield_11205": null,
    "environment": null,
    "customfield_11800": null,
    "customfield_13903": null,
    "duedate": "2024-11-13",
    "progress": {
      "progress": 0,
      "total": 0
    },
    "votes": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/issue/MARS-3085/votes",
      "votes": 0,
      "hasVoted": false
    }
  }
}
{
  "expand": "operations,versionedRepresentations,editmeta,changelog,renderedFields",
  "id": "241051",
  "self": "https://jira.dc2.intactlab.ca/rest/api/2/issue/241051",
  "key": "MARS-3083",
  "fields": {
    "issuetype": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/issuetype/10002",
      "id": "10002",
      "description": "The sub-task of the issue",
      "iconUrl": "https://jira.dc2.intactlab.ca/secure/viewavatar?size=xsmall&avatarId=10316&avatarType=issuetype",
      "name": "Sub-task",
      "subtask": true,
      "avatarId": 10316
    },
    "parent": {
      "id": "239729",
      "key": "MARS-2964",
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/issue/239729",
      "fields": {
        "summary": "Goalkeeping PI.2024.4.1",
        "status": {
          "self": "https://jira.dc2.intactlab.ca/rest/api/2/status/10002",
          "description": "",
          "iconUrl": "https://jira.dc2.intactlab.ca/",
          "name": "Done",
          "id": "10002",
          "statusCategory": {
            "self": "https://jira.dc2.intactlab.ca/rest/api/2/statuscategory/3",
            "id": 3,
            "key": "done",
            "colorName": "success",
            "name": "Done"
          }
        },
        "priority": {
          "self": "https://jira.dc2.intactlab.ca/rest/api/2/priority/3",
          "iconUrl": "https://jira.dc2.intactlab.ca/images/icons/priorities/medium.svg",
          "name": "Medium",
          "id": "3"
        },
        "issuetype": {
          "self": "https://jira.dc2.intactlab.ca/rest/api/2/issuetype/10000",
          "id": "10000",
          "description": "Created by Jira Software - do not edit or delete. Issue type for a user story.",
          "iconUrl": "https://jira.dc2.intactlab.ca/images/icons/issuetypes/story.svg",
          "name": "Story",
          "subtask": false
        }
      }
    },
    "timespent": null,
    "customfield_13100": null,
    "project": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/project/14101",
      "id": "14101",
      "key": "MARS",
      "name": "The Martians",
      "projectTypeKey": "software",
      "avatarUrls": {
        "48x48": "https://jira.dc2.intactlab.ca/secure/projectavatar?pid=14101&avatarId=23504",
        "24x24": "https://jira.dc2.intactlab.ca/secure/projectavatar?size=small&pid=14101&avatarId=23504",
        "16x16": "https://jira.dc2.intactlab.ca/secure/projectavatar?size=xsmall&pid=14101&avatarId=23504",
        "32x32": "https://jira.dc2.intactlab.ca/secure/projectavatar?size=medium&pid=14101&avatarId=23504"
      }
    },
    "fixVersions": [
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/version/28478",
        "id": "28478",
        "description": "",
        "name": "Bloom + IRCA  + Auto/QuickQuote CVE Fix - MEP - November 25th",
        "archived": false,
        "released": false,
        "releaseDate": "2024-11-25"
      }
    ],
    "customfield_11001": null,
    "customfield_11002": null,
    "customfield_11200": null,
    "aggregatetimespent": null,
    "resolution": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/resolution/10000",
      "id": "10000",
      "description": "Work has been completed on this issue.",
      "name": "Done"
    },
    "customfield_11201": null,
    "customfield_11003": null,
    "customfield_11202": null,
    "customfield_11004": null,
    "customfield_11203": [],
    "customfield_11005": null,
    "customfield_10700": null,
    "customfield_10900": null,
    "resolutiondate": "2024-10-10T16:39:08.000-0400",
    "workratio": -1,
    "lastViewed": "2024-11-05T16:11:25.971-0500",
    "watches": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/issue/MARS-3083/watchers",
      "watchCount": 2,
      "isWatching": true
    },
    "created": "2024-10-10T13:44:18.000-0400",
    "customfield_10020": null,
    "customfield_12202": null,
    "priority": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/priority/3",
      "iconUrl": "https://jira.dc2.intactlab.ca/images/icons/priorities/medium.svg",
      "name": "Medium",
      "id": "3"
    },
    "customfield_10100": null,
    "customfield_12201": null,
    "customfield_10024": [
      "com.atlassian.greenhopper.service.sprint.Sprint@5ab4e0ce[id=3550,rapidViewId=385,state=CLOSED,name=PI.2024.4.1,startDate=2024-10-03T13:00:00.000-04:00,endDate=2024-10-17T08:00:00.000-04:00,completeDate=2024-10-17T10:54:13.488-04:00,activatedDate=2024-10-03T12:02:38.404-04:00,sequence=3123,goal=1 - Release Lead Sharing in Prod by October 17th (Mid Month release)\n2- Make sure we are ready for the Broker admin release planned october 21st.\n3- Start work on Referal prgram,autoStartStop=false,synced=false]"
    ],
    "customfield_10025": "0|i0vs78:",
    "labels": [
      "Twistlock"
    ],
    "customfield_10026": null,
    "customfield_11700": null,
    "customfield_13008": null,
    "customfield_11900": null,
    "customfield_11902": null,
    "timeestimate": null,
    "aggregatetimeoriginalestimate": null,
    "versions": [],
    "customfield_11901": null,
    "customfield_11906": null,
    "customfield_11905": null,
    "issuelinks": [],
    "assignee": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/user?username=rpmdh249",
      "name": "rpmdh249",
      "key": "JIRAUSER24818",
      "emailAddress": "<EMAIL>",
      "avatarUrls": {
        "48x48": "https://www.gravatar.com/avatar/9e331dcca191645ab52bbed1c8742ef3?d=mm&s=48",
        "24x24": "https://www.gravatar.com/avatar/9e331dcca191645ab52bbed1c8742ef3?d=mm&s=24",
        "16x16": "https://www.gravatar.com/avatar/9e331dcca191645ab52bbed1c8742ef3?d=mm&s=16",
        "32x32": "https://www.gravatar.com/avatar/9e331dcca191645ab52bbed1c8742ef3?d=mm&s=32"
      },
      "displayName": "Omar Bounouioua",
      "active": false,
      "timeZone": "America/New_York"
    },
    "updated": "2024-11-05T15:57:14.000-0500",
    "status": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/status/10002",
      "description": "",
      "iconUrl": "https://jira.dc2.intactlab.ca/",
      "name": "Done",
      "id": "10002",
      "statusCategory": {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/statuscategory/3",
        "id": 3,
        "key": "done",
        "colorName": "success",
        "name": "Done"
      }
    },
    "components": [],
    "timeoriginalestimate": null,
    "customfield_13001": null,
    "description": null,
    "customfield_13003": null,
    "customfield_11022": null,
    "customfield_11100": "{summaryBean=com.atlassian.jira.plugin.devstatus.rest.SummaryBean@1f0d7131[summary={pullrequest=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@25e6fe81[overall=PullRequestOverallBean{stateCount=0, state='OPEN', details=PullRequestOverallDetails{openCount=0, mergedCount=0, declinedCount=0}},byInstanceType={}], build=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@3683a6f3[overall=com.atlassian.jira.plugin.devstatus.summary.beans.BuildOverallBean@24835454[failedBuildCount=0,successfulBuildCount=0,unknownBuildCount=0,count=0,lastUpdated=<null>,lastUpdatedTimestamp=<null>],byInstanceType={}], review=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@201a7e41[overall=com.atlassian.jira.plugin.devstatus.summary.beans.ReviewsOverallBean@7c2e695c[stateCount=0,state=<null>,dueDate=<null>,overDue=false,count=0,lastUpdated=<null>,lastUpdatedTimestamp=<null>],byInstanceType={}], deployment-environment=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@5731fc14[overall=com.atlassian.jira.plugin.devstatus.summary.beans.DeploymentOverallBean@69ae2352[topEnvironments=[],showProjects=false,successfulCount=0,count=0,lastUpdated=<null>,lastUpdatedTimestamp=<null>],byInstanceType={}], repository=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@1a35a050[overall=com.atlassian.jira.plugin.devstatus.summary.beans.CommitOverallBean@15de5ec3[count=0,lastUpdated=<null>,lastUpdatedTimestamp=<null>],byInstanceType={}], branch=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@4dd7d130[overall=com.atlassian.jira.plugin.devstatus.summary.beans.BranchOverallBean@55fbcf6b[count=0,lastUpdated=<null>,lastUpdatedTimestamp=<null>],byInstanceType={}]},errors=[],configErrors=[]], devSummaryJson={\"cachedValue\":{\"errors\":[],\"configErrors\":[],\"summary\":{\"pullrequest\":{\"overall\":{\"count\":0,\"lastUpdated\":null,\"stateCount\":0,\"state\":\"OPEN\",\"details\":{\"openCount\":0,\"mergedCount\":0,\"declinedCount\":0,\"total\":0},\"open\":true},\"byInstanceType\":{}},\"build\":{\"overall\":{\"count\":0,\"lastUpdated\":null,\"failedBuildCount\":0,\"successfulBuildCount\":0,\"unknownBuildCount\":0},\"byInstanceType\":{}},\"review\":{\"overall\":{\"count\":0,\"lastUpdated\":null,\"stateCount\":0,\"state\":null,\"dueDate\":null,\"overDue\":false,\"completed\":false},\"byInstanceType\":{}},\"deployment-environment\":{\"overall\":{\"count\":0,\"lastUpdated\":null,\"topEnvironments\":[],\"showProjects\":false,\"successfulCount\":0},\"byInstanceType\":{}},\"repository\":{\"overall\":{\"count\":0,\"lastUpdated\":null},\"byInstanceType\":{}},\"branch\":{\"overall\":{\"count\":0,\"lastUpdated\":null},\"byInstanceType\":{}}}},\"isStale\":true}}",
    "customfield_13002": null,
    "customfield_11023": null,
    "customfield_13005": null,
    "customfield_11300": null,
    "customfield_13004": null,
    "customfield_13007": null,
    "customfield_11301": null,
    "customfield_13006": null,
    "customfield_10800": null,
    "customfield_10801": null,
    "aggregatetimeestimate": null,
    "customfield_10802": null,
    "summary": "Twistlocks - quickquote-intact-web:4.0.3",
    "creator": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/user?username=rpmdh249",
      "name": "rpmdh249",
      "key": "JIRAUSER24818",
      "emailAddress": "<EMAIL>",
      "avatarUrls": {
        "48x48": "https://www.gravatar.com/avatar/9e331dcca191645ab52bbed1c8742ef3?d=mm&s=48",
        "24x24": "https://www.gravatar.com/avatar/9e331dcca191645ab52bbed1c8742ef3?d=mm&s=24",
        "16x16": "https://www.gravatar.com/avatar/9e331dcca191645ab52bbed1c8742ef3?d=mm&s=16",
        "32x32": "https://www.gravatar.com/avatar/9e331dcca191645ab52bbed1c8742ef3?d=mm&s=32"
      },
      "displayName": "Omar Bounouioua",
      "active": false,
      "timeZone": "America/New_York"
    },
    "subtasks": [],
    "reporter": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/user?username=rpmdh249",
      "name": "rpmdh249",
      "key": "JIRAUSER24818",
      "emailAddress": "<EMAIL>",
      "avatarUrls": {
        "48x48": "https://www.gravatar.com/avatar/9e331dcca191645ab52bbed1c8742ef3?d=mm&s=48",
        "24x24": "https://www.gravatar.com/avatar/9e331dcca191645ab52bbed1c8742ef3?d=mm&s=24",
        "16x16": "https://www.gravatar.com/avatar/9e331dcca191645ab52bbed1c8742ef3?d=mm&s=16",
        "32x32": "https://www.gravatar.com/avatar/9e331dcca191645ab52bbed1c8742ef3?d=mm&s=32"
      },
      "displayName": "Omar Bounouioua",
      "active": false,
      "timeZone": "America/New_York"
    },
    "customfield_12100": [
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/10900",
        "value": "Belair",
        "id": "10900",
        "disabled": false
      },
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/10901",
        "value": "BNA",
        "id": "10901",
        "disabled": false
      },
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/10902",
        "value": "Intact",
        "id": "10902",
        "disabled": false
      },
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/12514",
        "value": "Servus",
        "id": "12514",
        "disabled": false
      },
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/12515",
        "value": "Scotia",
        "id": "12515",
        "disabled": false
      }
    ],
    "aggregateprogress": {
      "progress": 0,
      "total": 0
    },
    "customfield_10200": null,
    "customfield_10400": null,
    "customfield_12500": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/11102",
      "value": "Arch_Required",
      "id": "11102",
      "disabled": false
    },
    "customfield_13900": null,
    "customfield_11204": null,
    "customfield_11006": null,
    "customfield_11205": null,
    "environment": null,
    "customfield_11800": null,
    "customfield_13903": null,
    "duedate": "2024-11-13",
    "progress": {
      "progress": 0,
      "total": 0
    },
    "votes": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/issue/MARS-3083/votes",
      "votes": 0,
      "hasVoted": false
    }
  }
}
{
  "expand": "operations,versionedRepresentations,editmeta,changelog,renderedFields",
  "id": "239424",
  "self": "https://jira.dc2.intactlab.ca/rest/api/2/issue/239424",
  "key": "MARS-2946",
  "fields": {
    "issuetype": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/issuetype/10002",
      "id": "10002",
      "description": "The sub-task of the issue",
      "iconUrl": "https://jira.dc2.intactlab.ca/secure/viewavatar?size=xsmall&avatarId=10316&avatarType=issuetype",
      "name": "Sub-task",
      "subtask": true,
      "avatarId": 10316
    },
    "parent": {
      "id": "239729",
      "key": "MARS-2964",
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/issue/239729",
      "fields": {
        "summary": "Goalkeeping PI.2024.4.1",
        "status": {
          "self": "https://jira.dc2.intactlab.ca/rest/api/2/status/10002",
          "description": "",
          "iconUrl": "https://jira.dc2.intactlab.ca/",
          "name": "Done",
          "id": "10002",
          "statusCategory": {
            "self": "https://jira.dc2.intactlab.ca/rest/api/2/statuscategory/3",
            "id": 3,
            "key": "done",
            "colorName": "success",
            "name": "Done"
          }
        },
        "priority": {
          "self": "https://jira.dc2.intactlab.ca/rest/api/2/priority/3",
          "iconUrl": "https://jira.dc2.intactlab.ca/images/icons/priorities/medium.svg",
          "name": "Medium",
          "id": "3"
        },
        "issuetype": {
          "self": "https://jira.dc2.intactlab.ca/rest/api/2/issuetype/10000",
          "id": "10000",
          "description": "Created by Jira Software - do not edit or delete. Issue type for a user story.",
          "iconUrl": "https://jira.dc2.intactlab.ca/images/icons/issuetypes/story.svg",
          "name": "Story",
          "subtask": false
        }
      }
    },
    "timespent": null,
    "customfield_13100": null,
    "project": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/project/14101",
      "id": "14101",
      "key": "MARS",
      "name": "The Martians",
      "projectTypeKey": "software",
      "avatarUrls": {
        "48x48": "https://jira.dc2.intactlab.ca/secure/projectavatar?pid=14101&avatarId=23504",
        "24x24": "https://jira.dc2.intactlab.ca/secure/projectavatar?size=small&pid=14101&avatarId=23504",
        "16x16": "https://jira.dc2.intactlab.ca/secure/projectavatar?size=xsmall&pid=14101&avatarId=23504",
        "32x32": "https://jira.dc2.intactlab.ca/secure/projectavatar?size=medium&pid=14101&avatarId=23504"
      }
    },
    "fixVersions": [
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/version/28478",
        "id": "28478",
        "description": "",
        "name": "Bloom + IRCA  + Auto/QuickQuote CVE Fix - MEP - November 25th",
        "archived": false,
        "released": false,
        "releaseDate": "2024-11-25"
      }
    ],
    "customfield_11001": null,
    "customfield_11002": null,
    "customfield_11200": null,
    "aggregatetimespent": null,
    "resolution": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/resolution/10000",
      "id": "10000",
      "description": "Work has been completed on this issue.",
      "name": "Done"
    },
    "customfield_11201": null,
    "customfield_11003": null,
    "customfield_11202": null,
    "customfield_11004": null,
    "customfield_11203": [],
    "customfield_11005": null,
    "customfield_10700": null,
    "customfield_10900": null,
    "resolutiondate": "2024-10-10T15:03:20.000-0400",
    "workratio": -1,
    "lastViewed": null,
    "watches": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/issue/MARS-2946/watchers",
      "watchCount": 1,
      "isWatching": true
    },
    "created": "2024-10-01T09:53:30.000-0400",
    "customfield_10020": null,
    "customfield_12202": null,
    "priority": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/priority/3",
      "iconUrl": "https://jira.dc2.intactlab.ca/images/icons/priorities/medium.svg",
      "name": "Medium",
      "id": "3"
    },
    "customfield_10100": null,
    "customfield_12201": null,
    "customfield_10024": [
      "com.atlassian.greenhopper.service.sprint.Sprint@5ab4e0ce[id=3550,rapidViewId=385,state=CLOSED,name=PI.2024.4.1,startDate=2024-10-03T13:00:00.000-04:00,endDate=2024-10-17T08:00:00.000-04:00,completeDate=2024-10-17T10:54:13.488-04:00,activatedDate=2024-10-03T12:02:38.404-04:00,sequence=3123,goal=1 - Release Lead Sharing in Prod by October 17th (Mid Month release)\n2- Make sure we are ready for the Broker admin release planned october 21st.\n3- Start work on Referal prgram,autoStartStop=false,synced=false]"
    ],
    "customfield_10025": "0|i0vj9o:",
    "labels": [
      "Twistlock"
    ],
    "customfield_10026": null,
    "customfield_11700": null,
    "customfield_13008": null,
    "customfield_11900": null,
    "customfield_11902": null,
    "timeestimate": null,
    "aggregatetimeoriginalestimate": null,
    "versions": [],
    "customfield_11901": null,
    "customfield_11906": null,
    "customfield_11905": null,
    "issuelinks": [],
    "assignee": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/user?username=rpmdh249",
      "name": "rpmdh249",
      "key": "JIRAUSER24818",
      "emailAddress": "<EMAIL>",
      "avatarUrls": {
        "48x48": "https://www.gravatar.com/avatar/9e331dcca191645ab52bbed1c8742ef3?d=mm&s=48",
        "24x24": "https://www.gravatar.com/avatar/9e331dcca191645ab52bbed1c8742ef3?d=mm&s=24",
        "16x16": "https://www.gravatar.com/avatar/9e331dcca191645ab52bbed1c8742ef3?d=mm&s=16",
        "32x32": "https://www.gravatar.com/avatar/9e331dcca191645ab52bbed1c8742ef3?d=mm&s=32"
      },
      "displayName": "Omar Bounouioua",
      "active": false,
      "timeZone": "America/New_York"
    },
    "updated": "2024-11-01T15:39:09.000-0400",
    "status": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/status/10002",
      "description": "",
      "iconUrl": "https://jira.dc2.intactlab.ca/",
      "name": "Done",
      "id": "10002",
      "statusCategory": {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/statuscategory/3",
        "id": 3,
        "key": "done",
        "colorName": "success",
        "name": "Done"
      }
    },
    "components": [],
    "timeoriginalestimate": null,
    "customfield_13001": null,
    "description": "|Vulnerability: CVE-2024-38816|\r\n|Scope: spring-webmvc, version: 6.1.11|\r\n|Recommendation: fixed in 6.1.13, 6.0.24, 5.3.40|\r\n|Severity: High|\r\n|Last Scan: 2024-09-30|\r\n|Fix By: 2024-11-26|\r\n|Overdue: No|",
    "customfield_13003": null,
    "customfield_11022": null,
    "customfield_11100": "{summaryBean=com.atlassian.jira.plugin.devstatus.rest.SummaryBean@7a536d5f[summary={pullrequest=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@230e3d0[overall=PullRequestOverallBean{stateCount=0, state='OPEN', details=PullRequestOverallDetails{openCount=0, mergedCount=0, declinedCount=0}},byInstanceType={}], build=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@4adbcd9b[overall=com.atlassian.jira.plugin.devstatus.summary.beans.BuildOverallBean@4f708973[failedBuildCount=0,successfulBuildCount=0,unknownBuildCount=0,count=0,lastUpdated=<null>,lastUpdatedTimestamp=<null>],byInstanceType={}], review=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@500196ae[overall=com.atlassian.jira.plugin.devstatus.summary.beans.ReviewsOverallBean@609352e3[stateCount=0,state=<null>,dueDate=<null>,overDue=false,count=0,lastUpdated=<null>,lastUpdatedTimestamp=<null>],byInstanceType={}], deployment-environment=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@74b0fc7f[overall=com.atlassian.jira.plugin.devstatus.summary.beans.DeploymentOverallBean@3fc0c88b[topEnvironments=[],showProjects=false,successfulCount=0,count=0,lastUpdated=<null>,lastUpdatedTimestamp=<null>],byInstanceType={}], repository=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@63288d3b[overall=com.atlassian.jira.plugin.devstatus.summary.beans.CommitOverallBean@19d6dff7[count=0,lastUpdated=<null>,lastUpdatedTimestamp=<null>],byInstanceType={}], branch=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@6af68bb6[overall=com.atlassian.jira.plugin.devstatus.summary.beans.BranchOverallBean@53cb92f3[count=0,lastUpdated=<null>,lastUpdatedTimestamp=<null>],byInstanceType={}]},errors=[],configErrors=[]], devSummaryJson={\"cachedValue\":{\"errors\":[],\"configErrors\":[],\"summary\":{\"pullrequest\":{\"overall\":{\"count\":0,\"lastUpdated\":null,\"stateCount\":0,\"state\":\"OPEN\",\"details\":{\"openCount\":0,\"mergedCount\":0,\"declinedCount\":0,\"total\":0},\"open\":true},\"byInstanceType\":{}},\"build\":{\"overall\":{\"count\":0,\"lastUpdated\":null,\"failedBuildCount\":0,\"successfulBuildCount\":0,\"unknownBuildCount\":0},\"byInstanceType\":{}},\"review\":{\"overall\":{\"count\":0,\"lastUpdated\":null,\"stateCount\":0,\"state\":null,\"dueDate\":null,\"overDue\":false,\"completed\":false},\"byInstanceType\":{}},\"deployment-environment\":{\"overall\":{\"count\":0,\"lastUpdated\":null,\"topEnvironments\":[],\"showProjects\":false,\"successfulCount\":0},\"byInstanceType\":{}},\"repository\":{\"overall\":{\"count\":0,\"lastUpdated\":null},\"byInstanceType\":{}},\"branch\":{\"overall\":{\"count\":0,\"lastUpdated\":null},\"byInstanceType\":{}}}},\"isStale\":true}}",
    "customfield_13002": null,
    "customfield_11023": null,
    "customfield_13005": null,
    "customfield_11300": null,
    "customfield_13004": null,
    "customfield_13007": null,
    "customfield_11301": null,
    "customfield_13006": null,
    "customfield_10800": null,
    "customfield_10801": null,
    "aggregatetimeestimate": null,
    "customfield_10802": null,
    "summary": "Twistlock - broker-dialer-service:3.1.55, Fix By: 2024-11-26",
    "creator": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/user?username=rpjzn388",
      "name": "rpjzn388",
      "key": "JIRAUSER25389",
      "emailAddress": "<EMAIL>",
      "avatarUrls": {
        "48x48": "https://jira.dc2.intactlab.ca/secure/useravatar?ownerId=JIRAUSER25389&avatarId=21503",
        "24x24": "https://jira.dc2.intactlab.ca/secure/useravatar?size=small&ownerId=JIRAUSER25389&avatarId=21503",
        "16x16": "https://jira.dc2.intactlab.ca/secure/useravatar?size=xsmall&ownerId=JIRAUSER25389&avatarId=21503",
        "32x32": "https://jira.dc2.intactlab.ca/secure/useravatar?size=medium&ownerId=JIRAUSER25389&avatarId=21503"
      },
      "displayName": "Leonardo Dias de Oliveira",
      "active": true,
      "timeZone": "America/New_York"
    },
    "subtasks": [],
    "reporter": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/user?username=rpjzn388",
      "name": "rpjzn388",
      "key": "JIRAUSER25389",
      "emailAddress": "<EMAIL>",
      "avatarUrls": {
        "48x48": "https://jira.dc2.intactlab.ca/secure/useravatar?ownerId=JIRAUSER25389&avatarId=21503",
        "24x24": "https://jira.dc2.intactlab.ca/secure/useravatar?size=small&ownerId=JIRAUSER25389&avatarId=21503",
        "16x16": "https://jira.dc2.intactlab.ca/secure/useravatar?size=xsmall&ownerId=JIRAUSER25389&avatarId=21503",
        "32x32": "https://jira.dc2.intactlab.ca/secure/useravatar?size=medium&ownerId=JIRAUSER25389&avatarId=21503"
      },
      "displayName": "Leonardo Dias de Oliveira",
      "active": true,
      "timeZone": "America/New_York"
    },
    "customfield_12100": [
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/10900",
        "value": "Belair",
        "id": "10900",
        "disabled": false
      },
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/10901",
        "value": "BNA",
        "id": "10901",
        "disabled": false
      },
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/10902",
        "value": "Intact",
        "id": "10902",
        "disabled": false
      },
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/12514",
        "value": "Servus",
        "id": "12514",
        "disabled": false
      },
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/12515",
        "value": "Scotia",
        "id": "12515",
        "disabled": false
      }
    ],
    "aggregateprogress": {
      "progress": 0,
      "total": 0
    },
    "customfield_10200": null,
    "customfield_10400": null,
    "customfield_12500": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/11102",
      "value": "Arch_Required",
      "id": "11102",
      "disabled": false
    },
    "customfield_13900": null,
    "customfield_11204": null,
    "customfield_11006": null,
    "customfield_11205": null,
    "environment": null,
    "customfield_11800": null,
    "customfield_13903": null,
    "duedate": "2024-11-26",
    "progress": {
      "progress": 0,
      "total": 0
    },
    "votes": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/issue/MARS-2946/votes",
      "votes": 0,
      "hasVoted": false
    }
  }
}
{
  "expand": "operations,versionedRepresentations,editmeta,changelog,renderedFields",
  "id": "239415",
  "self": "https://jira.dc2.intactlab.ca/rest/api/2/issue/239415",
  "key": "MARS-2941",
  "fields": {
    "issuetype": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/issuetype/10002",
      "id": "10002",
      "description": "The sub-task of the issue",
      "iconUrl": "https://jira.dc2.intactlab.ca/secure/viewavatar?size=xsmall&avatarId=10316&avatarType=issuetype",
      "name": "Sub-task",
      "subtask": true,
      "avatarId": 10316
    },
    "parent": {
      "id": "239729",
      "key": "MARS-2964",
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/issue/239729",
      "fields": {
        "summary": "Goalkeeping PI.2024.4.1",
        "status": {
          "self": "https://jira.dc2.intactlab.ca/rest/api/2/status/10002",
          "description": "",
          "iconUrl": "https://jira.dc2.intactlab.ca/",
          "name": "Done",
          "id": "10002",
          "statusCategory": {
            "self": "https://jira.dc2.intactlab.ca/rest/api/2/statuscategory/3",
            "id": 3,
            "key": "done",
            "colorName": "success",
            "name": "Done"
          }
        },
        "priority": {
          "self": "https://jira.dc2.intactlab.ca/rest/api/2/priority/3",
          "iconUrl": "https://jira.dc2.intactlab.ca/images/icons/priorities/medium.svg",
          "name": "Medium",
          "id": "3"
        },
        "issuetype": {
          "self": "https://jira.dc2.intactlab.ca/rest/api/2/issuetype/10000",
          "id": "10000",
          "description": "Created by Jira Software - do not edit or delete. Issue type for a user story.",
          "iconUrl": "https://jira.dc2.intactlab.ca/images/icons/issuetypes/story.svg",
          "name": "Story",
          "subtask": false
        }
      }
    },
    "timespent": null,
    "customfield_13100": null,
    "project": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/project/14101",
      "id": "14101",
      "key": "MARS",
      "name": "The Martians",
      "projectTypeKey": "software",
      "avatarUrls": {
        "48x48": "https://jira.dc2.intactlab.ca/secure/projectavatar?pid=14101&avatarId=23504",
        "24x24": "https://jira.dc2.intactlab.ca/secure/projectavatar?size=small&pid=14101&avatarId=23504",
        "16x16": "https://jira.dc2.intactlab.ca/secure/projectavatar?size=xsmall&pid=14101&avatarId=23504",
        "32x32": "https://jira.dc2.intactlab.ca/secure/projectavatar?size=medium&pid=14101&avatarId=23504"
      }
    },
    "fixVersions": [
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/version/28478",
        "id": "28478",
        "description": "",
        "name": "Bloom + IRCA  + Auto/QuickQuote CVE Fix - MEP - November 25th",
        "archived": false,
        "released": false,
        "releaseDate": "2024-11-25"
      }
    ],
    "customfield_11001": null,
    "customfield_11002": null,
    "customfield_11200": null,
    "aggregatetimespent": null,
    "resolution": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/resolution/10000",
      "id": "10000",
      "description": "Work has been completed on this issue.",
      "name": "Done"
    },
    "customfield_11201": null,
    "customfield_11003": null,
    "customfield_11202": null,
    "customfield_11004": null,
    "customfield_11203": [
      "main"
    ],
    "customfield_11005": null,
    "customfield_10700": null,
    "customfield_10900": null,
    "resolutiondate": "2024-10-08T12:45:14.000-0400",
    "workratio": -1,
    "lastViewed": "2024-11-01T16:16:31.244-0400",
    "watches": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/issue/MARS-2941/watchers",
      "watchCount": 1,
      "isWatching": true
    },
    "created": "2024-10-01T09:50:06.000-0400",
    "customfield_10020": null,
    "customfield_12202": null,
    "priority": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/priority/3",
      "iconUrl": "https://jira.dc2.intactlab.ca/images/icons/priorities/medium.svg",
      "name": "Medium",
      "id": "3"
    },
    "customfield_10100": null,
    "customfield_12201": null,
    "customfield_10024": [
      "com.atlassian.greenhopper.service.sprint.Sprint@5ab4e0ce[id=3550,rapidViewId=385,state=CLOSED,name=PI.2024.4.1,startDate=2024-10-03T13:00:00.000-04:00,endDate=2024-10-17T08:00:00.000-04:00,completeDate=2024-10-17T10:54:13.488-04:00,activatedDate=2024-10-03T12:02:38.404-04:00,sequence=3123,goal=1 - Release Lead Sharing in Prod by October 17th (Mid Month release)\n2- Make sure we are ready for the Broker admin release planned october 21st.\n3- Start work on Referal prgram,autoStartStop=false,synced=false]"
    ],
    "customfield_10025": "0|i0vj84:",
    "labels": [
      "Twistlock"
    ],
    "customfield_10026": null,
    "customfield_11700": null,
    "customfield_13008": null,
    "customfield_11900": null,
    "customfield_11902": null,
    "timeestimate": null,
    "aggregatetimeoriginalestimate": null,
    "versions": [],
    "customfield_11901": null,
    "customfield_11906": null,
    "customfield_11905": null,
    "issuelinks": [],
    "assignee": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/user?username=rpmdh249",
      "name": "rpmdh249",
      "key": "JIRAUSER24818",
      "emailAddress": "<EMAIL>",
      "avatarUrls": {
        "48x48": "https://www.gravatar.com/avatar/9e331dcca191645ab52bbed1c8742ef3?d=mm&s=48",
        "24x24": "https://www.gravatar.com/avatar/9e331dcca191645ab52bbed1c8742ef3?d=mm&s=24",
        "16x16": "https://www.gravatar.com/avatar/9e331dcca191645ab52bbed1c8742ef3?d=mm&s=16",
        "32x32": "https://www.gravatar.com/avatar/9e331dcca191645ab52bbed1c8742ef3?d=mm&s=32"
      },
      "displayName": "Omar Bounouioua",
      "active": false,
      "timeZone": "America/New_York"
    },
    "updated": "2024-11-01T15:39:08.000-0400",
    "status": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/status/10002",
      "description": "",
      "iconUrl": "https://jira.dc2.intactlab.ca/",
      "name": "Done",
      "id": "10002",
      "statusCategory": {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/statuscategory/3",
        "id": 3,
        "key": "done",
        "colorName": "success",
        "name": "Done"
      }
    },
    "components": [],
    "timeoriginalestimate": null,
    "customfield_13001": null,
    "description": "|Vulnerability: CVE-2024-38816|\r\n|Scope: spring-webmvc, version: 6.0.19|\r\n|Recommendation: fixed in 6.1.13, 6.0.24, 5.3.40|\r\n|Severity: High|\r\n|Last Scan: 2024-09-30|\r\n|Fix By: 2024-11-26|\r\n|Overdue: No|",
    "customfield_13003": null,
    "customfield_11022": null,
    "customfield_11100": "{summaryBean=com.atlassian.jira.plugin.devstatus.rest.SummaryBean@2037348e[summary={pullrequest=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@51a68677[overall=PullRequestOverallBean{stateCount=0, state='OPEN', details=PullRequestOverallDetails{openCount=0, mergedCount=0, declinedCount=0}},byInstanceType={}], build=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@68114a9b[overall=com.atlassian.jira.plugin.devstatus.summary.beans.BuildOverallBean@de1fcd0[failedBuildCount=0,successfulBuildCount=0,unknownBuildCount=0,count=0,lastUpdated=<null>,lastUpdatedTimestamp=<null>],byInstanceType={}], review=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@74bdccf2[overall=com.atlassian.jira.plugin.devstatus.summary.beans.ReviewsOverallBean@5910d194[stateCount=0,state=<null>,dueDate=<null>,overDue=false,count=0,lastUpdated=<null>,lastUpdatedTimestamp=<null>],byInstanceType={}], deployment-environment=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@3b370dbf[overall=com.atlassian.jira.plugin.devstatus.summary.beans.DeploymentOverallBean@39b27175[topEnvironments=[],showProjects=false,successfulCount=0,count=0,lastUpdated=<null>,lastUpdatedTimestamp=<null>],byInstanceType={}], repository=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@23f2e0ed[overall=com.atlassian.jira.plugin.devstatus.summary.beans.CommitOverallBean@7cb10976[count=0,lastUpdated=<null>,lastUpdatedTimestamp=<null>],byInstanceType={}], branch=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@7f68b9aa[overall=com.atlassian.jira.plugin.devstatus.summary.beans.BranchOverallBean@557b56eb[count=0,lastUpdated=<null>,lastUpdatedTimestamp=<null>],byInstanceType={}]},errors=[],configErrors=[]], devSummaryJson={\"cachedValue\":{\"errors\":[],\"configErrors\":[],\"summary\":{\"pullrequest\":{\"overall\":{\"count\":0,\"lastUpdated\":null,\"stateCount\":0,\"state\":\"OPEN\",\"details\":{\"openCount\":0,\"mergedCount\":0,\"declinedCount\":0,\"total\":0},\"open\":true},\"byInstanceType\":{}},\"build\":{\"overall\":{\"count\":0,\"lastUpdated\":null,\"failedBuildCount\":0,\"successfulBuildCount\":0,\"unknownBuildCount\":0},\"byInstanceType\":{}},\"review\":{\"overall\":{\"count\":0,\"lastUpdated\":null,\"stateCount\":0,\"state\":null,\"dueDate\":null,\"overDue\":false,\"completed\":false},\"byInstanceType\":{}},\"deployment-environment\":{\"overall\":{\"count\":0,\"lastUpdated\":null,\"topEnvironments\":[],\"showProjects\":false,\"successfulCount\":0},\"byInstanceType\":{}},\"repository\":{\"overall\":{\"count\":0,\"lastUpdated\":null},\"byInstanceType\":{}},\"branch\":{\"overall\":{\"count\":0,\"lastUpdated\":null},\"byInstanceType\":{}}}},\"isStale\":true}}",
    "customfield_13002": null,
    "customfield_11023": null,
    "customfield_13005": null,
    "customfield_11300": null,
    "customfield_13004": null,
    "customfield_13007": null,
    "customfield_11301": null,
    "customfield_13006": null,
    "customfield_10800": null,
    "customfield_10801": null,
    "aggregatetimeestimate": null,
    "customfield_10802": null,
    "summary": "Twistlock - bloom-contact-cl-processor:3.0.27, Fix By: 2024-11-26",
    "creator": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/user?username=rpjzn388",
      "name": "rpjzn388",
      "key": "JIRAUSER25389",
      "emailAddress": "<EMAIL>",
      "avatarUrls": {
        "48x48": "https://jira.dc2.intactlab.ca/secure/useravatar?ownerId=JIRAUSER25389&avatarId=21503",
        "24x24": "https://jira.dc2.intactlab.ca/secure/useravatar?size=small&ownerId=JIRAUSER25389&avatarId=21503",
        "16x16": "https://jira.dc2.intactlab.ca/secure/useravatar?size=xsmall&ownerId=JIRAUSER25389&avatarId=21503",
        "32x32": "https://jira.dc2.intactlab.ca/secure/useravatar?size=medium&ownerId=JIRAUSER25389&avatarId=21503"
      },
      "displayName": "Leonardo Dias de Oliveira",
      "active": true,
      "timeZone": "America/New_York"
    },
    "subtasks": [],
    "reporter": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/user?username=rpjzn388",
      "name": "rpjzn388",
      "key": "JIRAUSER25389",
      "emailAddress": "<EMAIL>",
      "avatarUrls": {
        "48x48": "https://jira.dc2.intactlab.ca/secure/useravatar?ownerId=JIRAUSER25389&avatarId=21503",
        "24x24": "https://jira.dc2.intactlab.ca/secure/useravatar?size=small&ownerId=JIRAUSER25389&avatarId=21503",
        "16x16": "https://jira.dc2.intactlab.ca/secure/useravatar?size=xsmall&ownerId=JIRAUSER25389&avatarId=21503",
        "32x32": "https://jira.dc2.intactlab.ca/secure/useravatar?size=medium&ownerId=JIRAUSER25389&avatarId=21503"
      },
      "displayName": "Leonardo Dias de Oliveira",
      "active": true,
      "timeZone": "America/New_York"
    },
    "customfield_12100": [
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/10900",
        "value": "Belair",
        "id": "10900",
        "disabled": false
      },
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/10901",
        "value": "BNA",
        "id": "10901",
        "disabled": false
      },
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/10902",
        "value": "Intact",
        "id": "10902",
        "disabled": false
      },
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/12514",
        "value": "Servus",
        "id": "12514",
        "disabled": false
      },
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/12515",
        "value": "Scotia",
        "id": "12515",
        "disabled": false
      }
    ],
    "aggregateprogress": {
      "progress": 0,
      "total": 0
    },
    "customfield_10200": null,
    "customfield_10400": null,
    "customfield_12500": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/11102",
      "value": "Arch_Required",
      "id": "11102",
      "disabled": false
    },
    "customfield_13900": null,
    "customfield_11204": "true",
    "customfield_11006": null,
    "customfield_11205": null,
    "environment": null,
    "customfield_11800": null,
    "customfield_13903": null,
    "duedate": "2024-11-26",
    "progress": {
      "progress": 0,
      "total": 0
    },
    "votes": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/issue/MARS-2941/votes",
      "votes": 0,
      "hasVoted": false
    }
  }
}
{
  "expand": "operations,versionedRepresentations,editmeta,changelog,renderedFields",
  "id": "239413",
  "self": "https://jira.dc2.intactlab.ca/rest/api/2/issue/239413",
  "key": "MARS-2940",
  "fields": {
    "issuetype": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/issuetype/10002",
      "id": "10002",
      "description": "The sub-task of the issue",
      "iconUrl": "https://jira.dc2.intactlab.ca/secure/viewavatar?size=xsmall&avatarId=10316&avatarType=issuetype",
      "name": "Sub-task",
      "subtask": true,
      "avatarId": 10316
    },
    "parent": {
      "id": "239729",
      "key": "MARS-2964",
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/issue/239729",
      "fields": {
        "summary": "Goalkeeping PI.2024.4.1",
        "status": {
          "self": "https://jira.dc2.intactlab.ca/rest/api/2/status/10002",
          "description": "",
          "iconUrl": "https://jira.dc2.intactlab.ca/",
          "name": "Done",
          "id": "10002",
          "statusCategory": {
            "self": "https://jira.dc2.intactlab.ca/rest/api/2/statuscategory/3",
            "id": 3,
            "key": "done",
            "colorName": "success",
            "name": "Done"
          }
        },
        "priority": {
          "self": "https://jira.dc2.intactlab.ca/rest/api/2/priority/3",
          "iconUrl": "https://jira.dc2.intactlab.ca/images/icons/priorities/medium.svg",
          "name": "Medium",
          "id": "3"
        },
        "issuetype": {
          "self": "https://jira.dc2.intactlab.ca/rest/api/2/issuetype/10000",
          "id": "10000",
          "description": "Created by Jira Software - do not edit or delete. Issue type for a user story.",
          "iconUrl": "https://jira.dc2.intactlab.ca/images/icons/issuetypes/story.svg",
          "name": "Story",
          "subtask": false
        }
      }
    },
    "timespent": null,
    "customfield_13100": null,
    "project": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/project/14101",
      "id": "14101",
      "key": "MARS",
      "name": "The Martians",
      "projectTypeKey": "software",
      "avatarUrls": {
        "48x48": "https://jira.dc2.intactlab.ca/secure/projectavatar?pid=14101&avatarId=23504",
        "24x24": "https://jira.dc2.intactlab.ca/secure/projectavatar?size=small&pid=14101&avatarId=23504",
        "16x16": "https://jira.dc2.intactlab.ca/secure/projectavatar?size=xsmall&pid=14101&avatarId=23504",
        "32x32": "https://jira.dc2.intactlab.ca/secure/projectavatar?size=medium&pid=14101&avatarId=23504"
      }
    },
    "fixVersions": [
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/version/28478",
        "id": "28478",
        "description": "",
        "name": "Bloom + IRCA  + Auto/QuickQuote CVE Fix - MEP - November 25th",
        "archived": false,
        "released": false,
        "releaseDate": "2024-11-25"
      }
    ],
    "customfield_11001": null,
    "customfield_11002": null,
    "customfield_11200": null,
    "aggregatetimespent": null,
    "resolution": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/resolution/10000",
      "id": "10000",
      "description": "Work has been completed on this issue.",
      "name": "Done"
    },
    "customfield_11201": null,
    "customfield_11003": null,
    "customfield_11202": null,
    "customfield_11004": null,
    "customfield_11203": [
      "main",
      "MARS-2940_update_tomcat_version"
    ],
    "customfield_11005": null,
    "customfield_10700": null,
    "customfield_10900": null,
    "resolutiondate": "2024-10-07T14:31:34.000-0400",
    "workratio": -1,
    "lastViewed": "2024-11-01T16:21:27.434-0400",
    "watches": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/issue/MARS-2940/watchers",
      "watchCount": 1,
      "isWatching": true
    },
    "created": "2024-10-01T09:49:03.000-0400",
    "customfield_10020": null,
    "customfield_12202": null,
    "priority": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/priority/3",
      "iconUrl": "https://jira.dc2.intactlab.ca/images/icons/priorities/medium.svg",
      "name": "Medium",
      "id": "3"
    },
    "customfield_10100": null,
    "customfield_12201": null,
    "customfield_10024": [
      "com.atlassian.greenhopper.service.sprint.Sprint@5ab4e0ce[id=3550,rapidViewId=385,state=CLOSED,name=PI.2024.4.1,startDate=2024-10-03T13:00:00.000-04:00,endDate=2024-10-17T08:00:00.000-04:00,completeDate=2024-10-17T10:54:13.488-04:00,activatedDate=2024-10-03T12:02:38.404-04:00,sequence=3123,goal=1 - Release Lead Sharing in Prod by October 17th (Mid Month release)\n2- Make sure we are ready for the Broker admin release planned october 21st.\n3- Start work on Referal prgram,autoStartStop=false,synced=false]"
    ],
    "customfield_10025": "0|i0vj7o:",
    "labels": [
      "Goalkeeper",
      "Twistlock"
    ],
    "customfield_10026": null,
    "customfield_11700": null,
    "customfield_13008": null,
    "customfield_11900": null,
    "customfield_11902": null,
    "timeestimate": null,
    "aggregatetimeoriginalestimate": null,
    "versions": [],
    "customfield_11901": null,
    "customfield_11906": null,
    "customfield_11905": null,
    "issuelinks": [],
    "assignee": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/user?username=rpmdh249",
      "name": "rpmdh249",
      "key": "JIRAUSER24818",
      "emailAddress": "<EMAIL>",
      "avatarUrls": {
        "48x48": "https://www.gravatar.com/avatar/9e331dcca191645ab52bbed1c8742ef3?d=mm&s=48",
        "24x24": "https://www.gravatar.com/avatar/9e331dcca191645ab52bbed1c8742ef3?d=mm&s=24",
        "16x16": "https://www.gravatar.com/avatar/9e331dcca191645ab52bbed1c8742ef3?d=mm&s=16",
        "32x32": "https://www.gravatar.com/avatar/9e331dcca191645ab52bbed1c8742ef3?d=mm&s=32"
      },
      "displayName": "Omar Bounouioua",
      "active": false,
      "timeZone": "America/New_York"
    },
    "updated": "2024-11-01T15:39:09.000-0400",
    "status": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/status/10002",
      "description": "",
      "iconUrl": "https://jira.dc2.intactlab.ca/",
      "name": "Done",
      "id": "10002",
      "statusCategory": {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/statuscategory/3",
        "id": 3,
        "key": "done",
        "colorName": "success",
        "name": "Done"
      }
    },
    "components": [],
    "timeoriginalestimate": null,
    "customfield_13001": null,
    "description": "|Vulnerability: CVE-2024-38816|\r\n|Scope: spring-webmvc, version: 6.0.19|\r\n|Recommendation: fixed in 6.1.13, 6.0.24, 5.3.40|\r\n|Severity: High|\r\n|Last Scan: 2024-09-30|\r\n|Fix By: 2024-11-26|\r\n|Overdue: No|",
    "customfield_13003": null,
    "customfield_11022": null,
    "customfield_11100": "{summaryBean=com.atlassian.jira.plugin.devstatus.rest.SummaryBean@7d311219[summary={pullrequest=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@45bf6d80[overall=PullRequestOverallBean{stateCount=0, state='OPEN', details=PullRequestOverallDetails{openCount=0, mergedCount=0, declinedCount=0}},byInstanceType={}], build=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@7f6b77fb[overall=com.atlassian.jira.plugin.devstatus.summary.beans.BuildOverallBean@4186c6fb[failedBuildCount=0,successfulBuildCount=0,unknownBuildCount=0,count=0,lastUpdated=<null>,lastUpdatedTimestamp=<null>],byInstanceType={}], review=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@3f3fd35a[overall=com.atlassian.jira.plugin.devstatus.summary.beans.ReviewsOverallBean@4d1975a7[stateCount=0,state=<null>,dueDate=<null>,overDue=false,count=0,lastUpdated=<null>,lastUpdatedTimestamp=<null>],byInstanceType={}], deployment-environment=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@69e91750[overall=com.atlassian.jira.plugin.devstatus.summary.beans.DeploymentOverallBean@1e2fa588[topEnvironments=[],showProjects=false,successfulCount=0,count=0,lastUpdated=<null>,lastUpdatedTimestamp=<null>],byInstanceType={}], repository=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@5f4b1014[overall=com.atlassian.jira.plugin.devstatus.summary.beans.CommitOverallBean@59f9e217[count=0,lastUpdated=<null>,lastUpdatedTimestamp=<null>],byInstanceType={}], branch=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@780b2def[overall=com.atlassian.jira.plugin.devstatus.summary.beans.BranchOverallBean@1bebd483[count=0,lastUpdated=<null>,lastUpdatedTimestamp=<null>],byInstanceType={}]},errors=[],configErrors=[]], devSummaryJson={\"cachedValue\":{\"errors\":[],\"configErrors\":[],\"summary\":{\"pullrequest\":{\"overall\":{\"count\":0,\"lastUpdated\":null,\"stateCount\":0,\"state\":\"OPEN\",\"details\":{\"openCount\":0,\"mergedCount\":0,\"declinedCount\":0,\"total\":0},\"open\":true},\"byInstanceType\":{}},\"build\":{\"overall\":{\"count\":0,\"lastUpdated\":null,\"failedBuildCount\":0,\"successfulBuildCount\":0,\"unknownBuildCount\":0},\"byInstanceType\":{}},\"review\":{\"overall\":{\"count\":0,\"lastUpdated\":null,\"stateCount\":0,\"state\":null,\"dueDate\":null,\"overDue\":false,\"completed\":false},\"byInstanceType\":{}},\"deployment-environment\":{\"overall\":{\"count\":0,\"lastUpdated\":null,\"topEnvironments\":[],\"showProjects\":false,\"successfulCount\":0},\"byInstanceType\":{}},\"repository\":{\"overall\":{\"count\":0,\"lastUpdated\":null},\"byInstanceType\":{}},\"branch\":{\"overall\":{\"count\":0,\"lastUpdated\":null},\"byInstanceType\":{}}}},\"isStale\":true}}",
    "customfield_13002": null,
    "customfield_11023": null,
    "customfield_13005": null,
    "customfield_11300": null,
    "customfield_13004": null,
    "customfield_13007": null,
    "customfield_11301": null,
    "customfield_13006": null,
    "customfield_10800": null,
    "customfield_10801": null,
    "aggregatetimeestimate": null,
    "customfield_10802": null,
    "summary": "Twistlock - bloom-api:3.0.64, Fix By: 2024-11-26",
    "creator": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/user?username=rpjzn388",
      "name": "rpjzn388",
      "key": "JIRAUSER25389",
      "emailAddress": "<EMAIL>",
      "avatarUrls": {
        "48x48": "https://jira.dc2.intactlab.ca/secure/useravatar?ownerId=JIRAUSER25389&avatarId=21503",
        "24x24": "https://jira.dc2.intactlab.ca/secure/useravatar?size=small&ownerId=JIRAUSER25389&avatarId=21503",
        "16x16": "https://jira.dc2.intactlab.ca/secure/useravatar?size=xsmall&ownerId=JIRAUSER25389&avatarId=21503",
        "32x32": "https://jira.dc2.intactlab.ca/secure/useravatar?size=medium&ownerId=JIRAUSER25389&avatarId=21503"
      },
      "displayName": "Leonardo Dias de Oliveira",
      "active": true,
      "timeZone": "America/New_York"
    },
    "subtasks": [],
    "reporter": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/user?username=rpjzn388",
      "name": "rpjzn388",
      "key": "JIRAUSER25389",
      "emailAddress": "<EMAIL>",
      "avatarUrls": {
        "48x48": "https://jira.dc2.intactlab.ca/secure/useravatar?ownerId=JIRAUSER25389&avatarId=21503",
        "24x24": "https://jira.dc2.intactlab.ca/secure/useravatar?size=small&ownerId=JIRAUSER25389&avatarId=21503",
        "16x16": "https://jira.dc2.intactlab.ca/secure/useravatar?size=xsmall&ownerId=JIRAUSER25389&avatarId=21503",
        "32x32": "https://jira.dc2.intactlab.ca/secure/useravatar?size=medium&ownerId=JIRAUSER25389&avatarId=21503"
      },
      "displayName": "Leonardo Dias de Oliveira",
      "active": true,
      "timeZone": "America/New_York"
    },
    "customfield_12100": [
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/10900",
        "value": "Belair",
        "id": "10900",
        "disabled": false
      },
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/10901",
        "value": "BNA",
        "id": "10901",
        "disabled": false
      },
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/10902",
        "value": "Intact",
        "id": "10902",
        "disabled": false
      },
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/12514",
        "value": "Servus",
        "id": "12514",
        "disabled": false
      },
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/12515",
        "value": "Scotia",
        "id": "12515",
        "disabled": false
      }
    ],
    "aggregateprogress": {
      "progress": 0,
      "total": 0
    },
    "customfield_10200": null,
    "customfield_10400": null,
    "customfield_12500": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/11102",
      "value": "Arch_Required",
      "id": "11102",
      "disabled": false
    },
    "customfield_13900": null,
    "customfield_11204": "true",
    "customfield_11006": null,
    "customfield_11205": null,
    "environment": null,
    "customfield_11800": null,
    "customfield_13903": null,
    "duedate": "2024-11-26",
    "progress": {
      "progress": 0,
      "total": 0
    },
    "votes": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/issue/MARS-2940/votes",
      "votes": 0,
      "hasVoted": false
    }
  }
}
{
  "expand": "operations,versionedRepresentations,editmeta,changelog,renderedFields",
  "id": "241805",
  "self": "https://jira.dc2.intactlab.ca/rest/api/2/issue/241805",
  "key": "MARS-3113",
  "fields": {
    "issuetype": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/issuetype/10002",
      "id": "10002",
      "description": "The sub-task of the issue",
      "iconUrl": "https://jira.dc2.intactlab.ca/secure/viewavatar?size=xsmall&avatarId=10316&avatarType=issuetype",
      "name": "Sub-task",
      "subtask": true,
      "avatarId": 10316
    },
    "parent": {
      "id": "239730",
      "key": "MARS-2965",
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/issue/239730",
      "fields": {
        "summary": "Goalkeeping PI.2024.4.2",
        "status": {
          "self": "https://jira.dc2.intactlab.ca/rest/api/2/status/10002",
          "description": "",
          "iconUrl": "https://jira.dc2.intactlab.ca/",
          "name": "Done",
          "id": "10002",
          "statusCategory": {
            "self": "https://jira.dc2.intactlab.ca/rest/api/2/statuscategory/3",
            "id": 3,
            "key": "done",
            "colorName": "success",
            "name": "Done"
          }
        },
        "priority": {
          "self": "https://jira.dc2.intactlab.ca/rest/api/2/priority/3",
          "iconUrl": "https://jira.dc2.intactlab.ca/images/icons/priorities/medium.svg",
          "name": "Medium",
          "id": "3"
        },
        "issuetype": {
          "self": "https://jira.dc2.intactlab.ca/rest/api/2/issuetype/10000",
          "id": "10000",
          "description": "Created by Jira Software - do not edit or delete. Issue type for a user story.",
          "iconUrl": "https://jira.dc2.intactlab.ca/images/icons/issuetypes/story.svg",
          "name": "Story",
          "subtask": false
        }
      }
    },
    "timespent": null,
    "customfield_13100": null,
    "project": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/project/14101",
      "id": "14101",
      "key": "MARS",
      "name": "The Martians",
      "projectTypeKey": "software",
      "avatarUrls": {
        "48x48": "https://jira.dc2.intactlab.ca/secure/projectavatar?pid=14101&avatarId=23504",
        "24x24": "https://jira.dc2.intactlab.ca/secure/projectavatar?size=small&pid=14101&avatarId=23504",
        "16x16": "https://jira.dc2.intactlab.ca/secure/projectavatar?size=xsmall&pid=14101&avatarId=23504",
        "32x32": "https://jira.dc2.intactlab.ca/secure/projectavatar?size=medium&pid=14101&avatarId=23504"
      }
    },
    "fixVersions": [
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/version/28403",
        "id": "28403",
        "description": "",
        "name": "November Mid Month Release",
        "archived": false,
        "released": false,
        "releaseDate": "2024-11-14"
      }
    ],
    "customfield_11001": null,
    "customfield_11002": null,
    "customfield_11200": null,
    "aggregatetimespent": null,
    "resolution": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/resolution/10000",
      "id": "10000",
      "description": "Work has been completed on this issue.",
      "name": "Done"
    },
    "customfield_11201": null,
    "customfield_11003": null,
    "customfield_11202": null,
    "customfield_11004": null,
    "customfield_11203": [],
    "customfield_11005": null,
    "customfield_10700": null,
    "customfield_10900": null,
    "resolutiondate": "2024-10-17T16:46:40.000-0400",
    "workratio": -1,
    "lastViewed": "2024-11-05T15:36:29.696-0500",
    "watches": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/issue/MARS-3113/watchers",
      "watchCount": 1,
      "isWatching": false
    },
    "created": "2024-10-17T16:16:26.000-0400",
    "customfield_10020": null,
    "customfield_12202": null,
    "priority": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/priority/3",
      "iconUrl": "https://jira.dc2.intactlab.ca/images/icons/priorities/medium.svg",
      "name": "Medium",
      "id": "3"
    },
    "customfield_10100": null,
    "customfield_12201": null,
    "customfield_10024": [
      "com.atlassian.greenhopper.service.sprint.Sprint@4008abed[id=3551,rapidViewId=385,state=CLOSED,name=PI.2024.4.2,startDate=2024-10-17T09:05:00.000-04:00,endDate=2024-10-30T17:00:00.000-04:00,completeDate=2024-10-31T09:14:08.691-04:00,activatedDate=2024-10-17T12:05:08.026-04:00,sequence=3303,goal=1- Successfully complete all our Releases planned this sprint (Mid-Month October, Broker Admin, Prep Rolls-Royce)\n2- Complete stories planned on Referal Program so QA could start next sprint,autoStartStop=false,synced=false]"
    ],
    "customfield_10025": "0|i0vwdo:",
    "labels": [
      "Twistlock"
    ],
    "customfield_10026": null,
    "customfield_11700": null,
    "customfield_13008": null,
    "customfield_11900": null,
    "customfield_11902": null,
    "timeestimate": null,
    "aggregatetimeoriginalestimate": null,
    "versions": [],
    "customfield_11901": null,
    "customfield_11906": null,
    "customfield_11905": null,
    "issuelinks": [],
    "assignee": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/user?username=rpmdh249",
      "name": "rpmdh249",
      "key": "JIRAUSER24818",
      "emailAddress": "<EMAIL>",
      "avatarUrls": {
        "48x48": "https://www.gravatar.com/avatar/9e331dcca191645ab52bbed1c8742ef3?d=mm&s=48",
        "24x24": "https://www.gravatar.com/avatar/9e331dcca191645ab52bbed1c8742ef3?d=mm&s=24",
        "16x16": "https://www.gravatar.com/avatar/9e331dcca191645ab52bbed1c8742ef3?d=mm&s=16",
        "32x32": "https://www.gravatar.com/avatar/9e331dcca191645ab52bbed1c8742ef3?d=mm&s=32"
      },
      "displayName": "Omar Bounouioua",
      "active": false,
      "timeZone": "America/New_York"
    },
    "updated": "2024-11-06T09:33:54.000-0500",
    "status": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/status/10002",
      "description": "",
      "iconUrl": "https://jira.dc2.intactlab.ca/",
      "name": "Done",
      "id": "10002",
      "statusCategory": {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/statuscategory/3",
        "id": 3,
        "key": "done",
        "colorName": "success",
        "name": "Done"
      }
    },
    "components": [
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/component/21400",
        "id": "21400",
        "name": "distributor-web-api"
      }
    ],
    "timeoriginalestimate": null,
    "customfield_13001": null,
    "description": "t.distributor-web-api.compliance-448",
    "customfield_13003": null,
    "customfield_11022": null,
    "customfield_11100": "{summaryBean=com.atlassian.jira.plugin.devstatus.rest.SummaryBean@568d31af[summary={pullrequest=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@4e329f54[overall=PullRequestOverallBean{stateCount=0, state='OPEN', details=PullRequestOverallDetails{openCount=0, mergedCount=0, declinedCount=0}},byInstanceType={}], build=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@1da31dcb[overall=com.atlassian.jira.plugin.devstatus.summary.beans.BuildOverallBean@47b1e914[failedBuildCount=0,successfulBuildCount=0,unknownBuildCount=0,count=0,lastUpdated=<null>,lastUpdatedTimestamp=<null>],byInstanceType={}], review=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@523dd5ab[overall=com.atlassian.jira.plugin.devstatus.summary.beans.ReviewsOverallBean@1262f0b2[stateCount=0,state=<null>,dueDate=<null>,overDue=false,count=0,lastUpdated=<null>,lastUpdatedTimestamp=<null>],byInstanceType={}], deployment-environment=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@172afa70[overall=com.atlassian.jira.plugin.devstatus.summary.beans.DeploymentOverallBean@5f9b48c8[topEnvironments=[],showProjects=false,successfulCount=0,count=0,lastUpdated=<null>,lastUpdatedTimestamp=<null>],byInstanceType={}], repository=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@63b75e1d[overall=com.atlassian.jira.plugin.devstatus.summary.beans.CommitOverallBean@6655c5b9[count=0,lastUpdated=<null>,lastUpdatedTimestamp=<null>],byInstanceType={}], branch=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@26ec5a79[overall=com.atlassian.jira.plugin.devstatus.summary.beans.BranchOverallBean@23b49b64[count=0,lastUpdated=<null>,lastUpdatedTimestamp=<null>],byInstanceType={}]},errors=[],configErrors=[]], devSummaryJson={\"cachedValue\":{\"errors\":[],\"configErrors\":[],\"summary\":{\"pullrequest\":{\"overall\":{\"count\":0,\"lastUpdated\":null,\"stateCount\":0,\"state\":\"OPEN\",\"details\":{\"openCount\":0,\"mergedCount\":0,\"declinedCount\":0,\"total\":0},\"open\":true},\"byInstanceType\":{}},\"build\":{\"overall\":{\"count\":0,\"lastUpdated\":null,\"failedBuildCount\":0,\"successfulBuildCount\":0,\"unknownBuildCount\":0},\"byInstanceType\":{}},\"review\":{\"overall\":{\"count\":0,\"lastUpdated\":null,\"stateCount\":0,\"state\":null,\"dueDate\":null,\"overDue\":false,\"completed\":false},\"byInstanceType\":{}},\"deployment-environment\":{\"overall\":{\"count\":0,\"lastUpdated\":null,\"topEnvironments\":[],\"showProjects\":false,\"successfulCount\":0},\"byInstanceType\":{}},\"repository\":{\"overall\":{\"count\":0,\"lastUpdated\":null},\"byInstanceType\":{}},\"branch\":{\"overall\":{\"count\":0,\"lastUpdated\":null},\"byInstanceType\":{}}}},\"isStale\":true}}",
    "customfield_13002": null,
    "customfield_11023": null,
    "customfield_13005": null,
    "customfield_11300": null,
    "customfield_13004": null,
    "customfield_13007": null,
    "customfield_11301": null,
    "customfield_13006": null,
    "customfield_10800": null,
    "customfield_10801": null,
    "aggregatetimeestimate": null,
    "customfield_10802": null,
    "summary": "Twislocks - distributor-web-api, Fix By 2024-11-26",
    "creator": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/user?username=rpmdh249",
      "name": "rpmdh249",
      "key": "JIRAUSER24818",
      "emailAddress": "<EMAIL>",
      "avatarUrls": {
        "48x48": "https://www.gravatar.com/avatar/9e331dcca191645ab52bbed1c8742ef3?d=mm&s=48",
        "24x24": "https://www.gravatar.com/avatar/9e331dcca191645ab52bbed1c8742ef3?d=mm&s=24",
        "16x16": "https://www.gravatar.com/avatar/9e331dcca191645ab52bbed1c8742ef3?d=mm&s=16",
        "32x32": "https://www.gravatar.com/avatar/9e331dcca191645ab52bbed1c8742ef3?d=mm&s=32"
      },
      "displayName": "Omar Bounouioua",
      "active": false,
      "timeZone": "America/New_York"
    },
    "subtasks": [],
    "reporter": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/user?username=rpmdh249",
      "name": "rpmdh249",
      "key": "JIRAUSER24818",
      "emailAddress": "<EMAIL>",
      "avatarUrls": {
        "48x48": "https://www.gravatar.com/avatar/9e331dcca191645ab52bbed1c8742ef3?d=mm&s=48",
        "24x24": "https://www.gravatar.com/avatar/9e331dcca191645ab52bbed1c8742ef3?d=mm&s=24",
        "16x16": "https://www.gravatar.com/avatar/9e331dcca191645ab52bbed1c8742ef3?d=mm&s=16",
        "32x32": "https://www.gravatar.com/avatar/9e331dcca191645ab52bbed1c8742ef3?d=mm&s=32"
      },
      "displayName": "Omar Bounouioua",
      "active": false,
      "timeZone": "America/New_York"
    },
    "customfield_12100": [
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/10900",
        "value": "Belair",
        "id": "10900",
        "disabled": false
      },
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/10901",
        "value": "BNA",
        "id": "10901",
        "disabled": false
      },
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/10902",
        "value": "Intact",
        "id": "10902",
        "disabled": false
      },
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/12514",
        "value": "Servus",
        "id": "12514",
        "disabled": false
      },
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/12515",
        "value": "Scotia",
        "id": "12515",
        "disabled": false
      }
    ],
    "aggregateprogress": {
      "progress": 0,
      "total": 0
    },
    "customfield_10200": null,
    "customfield_10400": null,
    "customfield_12500": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/11102",
      "value": "Arch_Required",
      "id": "11102",
      "disabled": false
    },
    "customfield_13900": null,
    "customfield_11204": null,
    "customfield_11006": null,
    "customfield_11205": null,
    "environment": null,
    "customfield_11800": null,
    "customfield_13903": null,
    "duedate": "2024-11-26",
    "progress": {
      "progress": 0,
      "total": 0
    },
    "votes": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/issue/MARS-3113/votes",
      "votes": 0,
      "hasVoted": false
    }
  }
}
{
  "expand": "operations,versionedRepresentations,editmeta,changelog,renderedFields",
  "id": "239422",
  "self": "https://jira.dc2.intactlab.ca/rest/api/2/issue/239422",
  "key": "MARS-2945",
  "fields": {
    "issuetype": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/issuetype/10002",
      "id": "10002",
      "description": "The sub-task of the issue",
      "iconUrl": "https://jira.dc2.intactlab.ca/secure/viewavatar?size=xsmall&avatarId=10316&avatarType=issuetype",
      "name": "Sub-task",
      "subtask": true,
      "avatarId": 10316
    },
    "parent": {
      "id": "239729",
      "key": "MARS-2964",
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/issue/239729",
      "fields": {
        "summary": "Goalkeeping PI.2024.4.1",
        "status": {
          "self": "https://jira.dc2.intactlab.ca/rest/api/2/status/10002",
          "description": "",
          "iconUrl": "https://jira.dc2.intactlab.ca/",
          "name": "Done",
          "id": "10002",
          "statusCategory": {
            "self": "https://jira.dc2.intactlab.ca/rest/api/2/statuscategory/3",
            "id": 3,
            "key": "done",
            "colorName": "success",
            "name": "Done"
          }
        },
        "priority": {
          "self": "https://jira.dc2.intactlab.ca/rest/api/2/priority/3",
          "iconUrl": "https://jira.dc2.intactlab.ca/images/icons/priorities/medium.svg",
          "name": "Medium",
          "id": "3"
        },
        "issuetype": {
          "self": "https://jira.dc2.intactlab.ca/rest/api/2/issuetype/10000",
          "id": "10000",
          "description": "Created by Jira Software - do not edit or delete. Issue type for a user story.",
          "iconUrl": "https://jira.dc2.intactlab.ca/images/icons/issuetypes/story.svg",
          "name": "Story",
          "subtask": false
        }
      }
    },
    "timespent": null,
    "customfield_13100": null,
    "project": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/project/14101",
      "id": "14101",
      "key": "MARS",
      "name": "The Martians",
      "projectTypeKey": "software",
      "avatarUrls": {
        "48x48": "https://jira.dc2.intactlab.ca/secure/projectavatar?pid=14101&avatarId=23504",
        "24x24": "https://jira.dc2.intactlab.ca/secure/projectavatar?size=small&pid=14101&avatarId=23504",
        "16x16": "https://jira.dc2.intactlab.ca/secure/projectavatar?size=xsmall&pid=14101&avatarId=23504",
        "32x32": "https://jira.dc2.intactlab.ca/secure/projectavatar?size=medium&pid=14101&avatarId=23504"
      }
    },
    "fixVersions": [
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/version/28478",
        "id": "28478",
        "description": "",
        "name": "Bloom + IRCA  + Auto/QuickQuote CVE Fix - MEP - November 25th",
        "archived": false,
        "released": false,
        "releaseDate": "2024-11-25"
      }
    ],
    "customfield_11001": null,
    "customfield_11002": null,
    "customfield_11200": null,
    "aggregatetimespent": null,
    "resolution": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/resolution/10000",
      "id": "10000",
      "description": "Work has been completed on this issue.",
      "name": "Done"
    },
    "customfield_11201": null,
    "customfield_11003": null,
    "customfield_11202": null,
    "customfield_11004": null,
    "customfield_11203": [],
    "customfield_11005": null,
    "customfield_10700": null,
    "customfield_10900": null,
    "resolutiondate": "2024-10-08T12:44:58.000-0400",
    "workratio": -1,
    "lastViewed": null,
    "watches": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/issue/MARS-2945/watchers",
      "watchCount": 1,
      "isWatching": true
    },
    "created": "2024-10-01T09:52:47.000-0400",
    "customfield_10020": null,
    "customfield_12202": null,
    "priority": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/priority/3",
      "iconUrl": "https://jira.dc2.intactlab.ca/images/icons/priorities/medium.svg",
      "name": "Medium",
      "id": "3"
    },
    "customfield_10100": null,
    "customfield_12201": null,
    "customfield_10024": [
      "com.atlassian.greenhopper.service.sprint.Sprint@5ab4e0ce[id=3550,rapidViewId=385,state=CLOSED,name=PI.2024.4.1,startDate=2024-10-03T13:00:00.000-04:00,endDate=2024-10-17T08:00:00.000-04:00,completeDate=2024-10-17T10:54:13.488-04:00,activatedDate=2024-10-03T12:02:38.404-04:00,sequence=3123,goal=1 - Release Lead Sharing in Prod by October 17th (Mid Month release)\n2- Make sure we are ready for the Broker admin release planned october 21st.\n3- Start work on Referal prgram,autoStartStop=false,synced=false]"
    ],
    "customfield_10025": "0|i0vj98:",
    "labels": [
      "Twistlock"
    ],
    "customfield_10026": null,
    "customfield_11700": null,
    "customfield_13008": null,
    "customfield_11900": null,
    "customfield_11902": null,
    "timeestimate": null,
    "aggregatetimeoriginalestimate": null,
    "versions": [],
    "customfield_11901": null,
    "customfield_11906": null,
    "customfield_11905": null,
    "issuelinks": [],
    "assignee": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/user?username=rpmdh249",
      "name": "rpmdh249",
      "key": "JIRAUSER24818",
      "emailAddress": "<EMAIL>",
      "avatarUrls": {
        "48x48": "https://www.gravatar.com/avatar/9e331dcca191645ab52bbed1c8742ef3?d=mm&s=48",
        "24x24": "https://www.gravatar.com/avatar/9e331dcca191645ab52bbed1c8742ef3?d=mm&s=24",
        "16x16": "https://www.gravatar.com/avatar/9e331dcca191645ab52bbed1c8742ef3?d=mm&s=16",
        "32x32": "https://www.gravatar.com/avatar/9e331dcca191645ab52bbed1c8742ef3?d=mm&s=32"
      },
      "displayName": "Omar Bounouioua",
      "active": false,
      "timeZone": "America/New_York"
    },
    "updated": "2024-11-01T15:39:09.000-0400",
    "status": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/status/10002",
      "description": "",
      "iconUrl": "https://jira.dc2.intactlab.ca/",
      "name": "Done",
      "id": "10002",
      "statusCategory": {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/statuscategory/3",
        "id": 3,
        "key": "done",
        "colorName": "success",
        "name": "Done"
      }
    },
    "components": [],
    "timeoriginalestimate": null,
    "customfield_13001": null,
    "description": "|Vulnerability: CVE-2024-38816|\r\n|Scope: spring-webmvc, version: 6.0.19|\r\n|Recommendation: fixed in 6.1.13, 6.0.24, 5.3.40|\r\n|Severity: High|\r\n|Last Scan: 2024-09-30|\r\n|Fix By: 2024-11-27|\r\n|Overdue: No|",
    "customfield_13003": null,
    "customfield_11022": null,
    "customfield_11100": "{summaryBean=com.atlassian.jira.plugin.devstatus.rest.SummaryBean@453d4590[summary={pullrequest=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@52339b7d[overall=PullRequestOverallBean{stateCount=0, state='OPEN', details=PullRequestOverallDetails{openCount=0, mergedCount=0, declinedCount=0}},byInstanceType={}], build=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@2fab5d72[overall=com.atlassian.jira.plugin.devstatus.summary.beans.BuildOverallBean@610dd389[failedBuildCount=0,successfulBuildCount=0,unknownBuildCount=0,count=0,lastUpdated=<null>,lastUpdatedTimestamp=<null>],byInstanceType={}], review=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@3265709a[overall=com.atlassian.jira.plugin.devstatus.summary.beans.ReviewsOverallBean@1e151bd2[stateCount=0,state=<null>,dueDate=<null>,overDue=false,count=0,lastUpdated=<null>,lastUpdatedTimestamp=<null>],byInstanceType={}], deployment-environment=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@6ba095af[overall=com.atlassian.jira.plugin.devstatus.summary.beans.DeploymentOverallBean@64be82c9[topEnvironments=[],showProjects=false,successfulCount=0,count=0,lastUpdated=<null>,lastUpdatedTimestamp=<null>],byInstanceType={}], repository=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@7d46cf64[overall=com.atlassian.jira.plugin.devstatus.summary.beans.CommitOverallBean@4b3b8095[count=0,lastUpdated=<null>,lastUpdatedTimestamp=<null>],byInstanceType={}], branch=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@24d979b0[overall=com.atlassian.jira.plugin.devstatus.summary.beans.BranchOverallBean@34e11f28[count=0,lastUpdated=<null>,lastUpdatedTimestamp=<null>],byInstanceType={}]},errors=[],configErrors=[]], devSummaryJson={\"cachedValue\":{\"errors\":[],\"configErrors\":[],\"summary\":{\"pullrequest\":{\"overall\":{\"count\":0,\"lastUpdated\":null,\"stateCount\":0,\"state\":\"OPEN\",\"details\":{\"openCount\":0,\"mergedCount\":0,\"declinedCount\":0,\"total\":0},\"open\":true},\"byInstanceType\":{}},\"build\":{\"overall\":{\"count\":0,\"lastUpdated\":null,\"failedBuildCount\":0,\"successfulBuildCount\":0,\"unknownBuildCount\":0},\"byInstanceType\":{}},\"review\":{\"overall\":{\"count\":0,\"lastUpdated\":null,\"stateCount\":0,\"state\":null,\"dueDate\":null,\"overDue\":false,\"completed\":false},\"byInstanceType\":{}},\"deployment-environment\":{\"overall\":{\"count\":0,\"lastUpdated\":null,\"topEnvironments\":[],\"showProjects\":false,\"successfulCount\":0},\"byInstanceType\":{}},\"repository\":{\"overall\":{\"count\":0,\"lastUpdated\":null},\"byInstanceType\":{}},\"branch\":{\"overall\":{\"count\":0,\"lastUpdated\":null},\"byInstanceType\":{}}}},\"isStale\":true}}",
    "customfield_13002": null,
    "customfield_11023": null,
    "customfield_13005": null,
    "customfield_11300": null,
    "customfield_13004": null,
    "customfield_13007": null,
    "customfield_11301": null,
    "customfield_13006": null,
    "customfield_10800": null,
    "customfield_10801": null,
    "aggregatetimeestimate": null,
    "customfield_10802": null,
    "summary": "Twistlock - bloom-xpas-processor:3.0.48, Fix By: 2024-11-27",
    "creator": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/user?username=rpjzn388",
      "name": "rpjzn388",
      "key": "JIRAUSER25389",
      "emailAddress": "<EMAIL>",
      "avatarUrls": {
        "48x48": "https://jira.dc2.intactlab.ca/secure/useravatar?ownerId=JIRAUSER25389&avatarId=21503",
        "24x24": "https://jira.dc2.intactlab.ca/secure/useravatar?size=small&ownerId=JIRAUSER25389&avatarId=21503",
        "16x16": "https://jira.dc2.intactlab.ca/secure/useravatar?size=xsmall&ownerId=JIRAUSER25389&avatarId=21503",
        "32x32": "https://jira.dc2.intactlab.ca/secure/useravatar?size=medium&ownerId=JIRAUSER25389&avatarId=21503"
      },
      "displayName": "Leonardo Dias de Oliveira",
      "active": true,
      "timeZone": "America/New_York"
    },
    "subtasks": [],
    "reporter": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/user?username=rpjzn388",
      "name": "rpjzn388",
      "key": "JIRAUSER25389",
      "emailAddress": "<EMAIL>",
      "avatarUrls": {
        "48x48": "https://jira.dc2.intactlab.ca/secure/useravatar?ownerId=JIRAUSER25389&avatarId=21503",
        "24x24": "https://jira.dc2.intactlab.ca/secure/useravatar?size=small&ownerId=JIRAUSER25389&avatarId=21503",
        "16x16": "https://jira.dc2.intactlab.ca/secure/useravatar?size=xsmall&ownerId=JIRAUSER25389&avatarId=21503",
        "32x32": "https://jira.dc2.intactlab.ca/secure/useravatar?size=medium&ownerId=JIRAUSER25389&avatarId=21503"
      },
      "displayName": "Leonardo Dias de Oliveira",
      "active": true,
      "timeZone": "America/New_York"
    },
    "customfield_12100": [
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/10900",
        "value": "Belair",
        "id": "10900",
        "disabled": false
      },
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/10901",
        "value": "BNA",
        "id": "10901",
        "disabled": false
      },
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/10902",
        "value": "Intact",
        "id": "10902",
        "disabled": false
      },
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/12514",
        "value": "Servus",
        "id": "12514",
        "disabled": false
      },
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/12515",
        "value": "Scotia",
        "id": "12515",
        "disabled": false
      }
    ],
    "aggregateprogress": {
      "progress": 0,
      "total": 0
    },
    "customfield_10200": null,
    "customfield_10400": null,
    "customfield_12500": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/11102",
      "value": "Arch_Required",
      "id": "11102",
      "disabled": false
    },
    "customfield_13900": null,
    "customfield_11204": null,
    "customfield_11006": null,
    "customfield_11205": null,
    "environment": null,
    "customfield_11800": null,
    "customfield_13903": null,
    "duedate": "2024-11-27",
    "progress": {
      "progress": 0,
      "total": 0
    },
    "votes": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/issue/MARS-2945/votes",
      "votes": 0,
      "hasVoted": false
    }
  }
}
{
  "expand": "operations,versionedRepresentations,editmeta,changelog,renderedFields",
  "id": "239418",
  "self": "https://jira.dc2.intactlab.ca/rest/api/2/issue/239418",
  "key": "MARS-2942",
  "fields": {
    "issuetype": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/issuetype/10002",
      "id": "10002",
      "description": "The sub-task of the issue",
      "iconUrl": "https://jira.dc2.intactlab.ca/secure/viewavatar?size=xsmall&avatarId=10316&avatarType=issuetype",
      "name": "Sub-task",
      "subtask": true,
      "avatarId": 10316
    },
    "parent": {
      "id": "239729",
      "key": "MARS-2964",
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/issue/239729",
      "fields": {
        "summary": "Goalkeeping PI.2024.4.1",
        "status": {
          "self": "https://jira.dc2.intactlab.ca/rest/api/2/status/10002",
          "description": "",
          "iconUrl": "https://jira.dc2.intactlab.ca/",
          "name": "Done",
          "id": "10002",
          "statusCategory": {
            "self": "https://jira.dc2.intactlab.ca/rest/api/2/statuscategory/3",
            "id": 3,
            "key": "done",
            "colorName": "success",
            "name": "Done"
          }
        },
        "priority": {
          "self": "https://jira.dc2.intactlab.ca/rest/api/2/priority/3",
          "iconUrl": "https://jira.dc2.intactlab.ca/images/icons/priorities/medium.svg",
          "name": "Medium",
          "id": "3"
        },
        "issuetype": {
          "self": "https://jira.dc2.intactlab.ca/rest/api/2/issuetype/10000",
          "id": "10000",
          "description": "Created by Jira Software - do not edit or delete. Issue type for a user story.",
          "iconUrl": "https://jira.dc2.intactlab.ca/images/icons/issuetypes/story.svg",
          "name": "Story",
          "subtask": false
        }
      }
    },
    "timespent": null,
    "customfield_13100": null,
    "project": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/project/14101",
      "id": "14101",
      "key": "MARS",
      "name": "The Martians",
      "projectTypeKey": "software",
      "avatarUrls": {
        "48x48": "https://jira.dc2.intactlab.ca/secure/projectavatar?pid=14101&avatarId=23504",
        "24x24": "https://jira.dc2.intactlab.ca/secure/projectavatar?size=small&pid=14101&avatarId=23504",
        "16x16": "https://jira.dc2.intactlab.ca/secure/projectavatar?size=xsmall&pid=14101&avatarId=23504",
        "32x32": "https://jira.dc2.intactlab.ca/secure/projectavatar?size=medium&pid=14101&avatarId=23504"
      }
    },
    "fixVersions": [
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/version/28478",
        "id": "28478",
        "description": "",
        "name": "Bloom + IRCA  + Auto/QuickQuote CVE Fix - MEP - November 25th",
        "archived": false,
        "released": false,
        "releaseDate": "2024-11-25"
      }
    ],
    "customfield_11001": null,
    "customfield_11002": null,
    "customfield_11200": null,
    "aggregatetimespent": null,
    "resolution": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/resolution/10000",
      "id": "10000",
      "description": "Work has been completed on this issue.",
      "name": "Done"
    },
    "customfield_11201": null,
    "customfield_11003": null,
    "customfield_11202": null,
    "customfield_11004": null,
    "customfield_11203": [],
    "customfield_11005": null,
    "customfield_10700": null,
    "customfield_10900": null,
    "resolutiondate": "2024-10-07T14:31:46.000-0400",
    "workratio": -1,
    "lastViewed": "2024-11-01T16:16:43.791-0400",
    "watches": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/issue/MARS-2942/watchers",
      "watchCount": 1,
      "isWatching": true
    },
    "created": "2024-10-01T09:50:46.000-0400",
    "customfield_10020": null,
    "customfield_12202": null,
    "priority": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/priority/3",
      "iconUrl": "https://jira.dc2.intactlab.ca/images/icons/priorities/medium.svg",
      "name": "Medium",
      "id": "3"
    },
    "customfield_10100": null,
    "customfield_12201": null,
    "customfield_10024": [
      "com.atlassian.greenhopper.service.sprint.Sprint@5ab4e0ce[id=3550,rapidViewId=385,state=CLOSED,name=PI.2024.4.1,startDate=2024-10-03T13:00:00.000-04:00,endDate=2024-10-17T08:00:00.000-04:00,completeDate=2024-10-17T10:54:13.488-04:00,activatedDate=2024-10-03T12:02:38.404-04:00,sequence=3123,goal=1 - Release Lead Sharing in Prod by October 17th (Mid Month release)\n2- Make sure we are ready for the Broker admin release planned october 21st.\n3- Start work on Referal prgram,autoStartStop=false,synced=false]"
    ],
    "customfield_10025": "0|i0vj8c:",
    "labels": [
      "Twistlock"
    ],
    "customfield_10026": null,
    "customfield_11700": null,
    "customfield_13008": null,
    "customfield_11900": null,
    "customfield_11902": null,
    "timeestimate": null,
    "aggregatetimeoriginalestimate": null,
    "versions": [],
    "customfield_11901": null,
    "customfield_11906": null,
    "customfield_11905": null,
    "issuelinks": [],
    "assignee": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/user?username=rpmdh249",
      "name": "rpmdh249",
      "key": "JIRAUSER24818",
      "emailAddress": "<EMAIL>",
      "avatarUrls": {
        "48x48": "https://www.gravatar.com/avatar/9e331dcca191645ab52bbed1c8742ef3?d=mm&s=48",
        "24x24": "https://www.gravatar.com/avatar/9e331dcca191645ab52bbed1c8742ef3?d=mm&s=24",
        "16x16": "https://www.gravatar.com/avatar/9e331dcca191645ab52bbed1c8742ef3?d=mm&s=16",
        "32x32": "https://www.gravatar.com/avatar/9e331dcca191645ab52bbed1c8742ef3?d=mm&s=32"
      },
      "displayName": "Omar Bounouioua",
      "active": false,
      "timeZone": "America/New_York"
    },
    "updated": "2024-11-01T15:39:08.000-0400",
    "status": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/status/10002",
      "description": "",
      "iconUrl": "https://jira.dc2.intactlab.ca/",
      "name": "Done",
      "id": "10002",
      "statusCategory": {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/statuscategory/3",
        "id": 3,
        "key": "done",
        "colorName": "success",
        "name": "Done"
      }
    },
    "components": [],
    "timeoriginalestimate": null,
    "customfield_13001": null,
    "description": "|Vulnerability: CVE-2024-38816|\r\n|Scope: spring-webmvc, version: 6.1.6|\r\n|Recommendation: fixed in 6.1.13, 6.0.24, 5.3.40|\r\n|Severity: High|\r\n|Last Scan: 2024-09-30|\r\n|Fix By: 2024-11-27|\r\n|Overdue: No|",
    "customfield_13003": null,
    "customfield_11022": null,
    "customfield_11100": "{summaryBean=com.atlassian.jira.plugin.devstatus.rest.SummaryBean@5ed255a4[summary={pullrequest=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@213c371[overall=PullRequestOverallBean{stateCount=0, state='OPEN', details=PullRequestOverallDetails{openCount=0, mergedCount=0, declinedCount=0}},byInstanceType={}], build=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@176ba893[overall=com.atlassian.jira.plugin.devstatus.summary.beans.BuildOverallBean@b7fe944[failedBuildCount=0,successfulBuildCount=0,unknownBuildCount=0,count=0,lastUpdated=<null>,lastUpdatedTimestamp=<null>],byInstanceType={}], review=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@17ed1f94[overall=com.atlassian.jira.plugin.devstatus.summary.beans.ReviewsOverallBean@9e27d21[stateCount=0,state=<null>,dueDate=<null>,overDue=false,count=0,lastUpdated=<null>,lastUpdatedTimestamp=<null>],byInstanceType={}], deployment-environment=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@438763be[overall=com.atlassian.jira.plugin.devstatus.summary.beans.DeploymentOverallBean@37469743[topEnvironments=[],showProjects=false,successfulCount=0,count=0,lastUpdated=<null>,lastUpdatedTimestamp=<null>],byInstanceType={}], repository=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@48282f80[overall=com.atlassian.jira.plugin.devstatus.summary.beans.CommitOverallBean@523948d1[count=0,lastUpdated=<null>,lastUpdatedTimestamp=<null>],byInstanceType={}], branch=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@2adb5bbb[overall=com.atlassian.jira.plugin.devstatus.summary.beans.BranchOverallBean@29a43d2c[count=0,lastUpdated=<null>,lastUpdatedTimestamp=<null>],byInstanceType={}]},errors=[],configErrors=[]], devSummaryJson={\"cachedValue\":{\"errors\":[],\"configErrors\":[],\"summary\":{\"pullrequest\":{\"overall\":{\"count\":0,\"lastUpdated\":null,\"stateCount\":0,\"state\":\"OPEN\",\"details\":{\"openCount\":0,\"mergedCount\":0,\"declinedCount\":0,\"total\":0},\"open\":true},\"byInstanceType\":{}},\"build\":{\"overall\":{\"count\":0,\"lastUpdated\":null,\"failedBuildCount\":0,\"successfulBuildCount\":0,\"unknownBuildCount\":0},\"byInstanceType\":{}},\"review\":{\"overall\":{\"count\":0,\"lastUpdated\":null,\"stateCount\":0,\"state\":null,\"dueDate\":null,\"overDue\":false,\"completed\":false},\"byInstanceType\":{}},\"deployment-environment\":{\"overall\":{\"count\":0,\"lastUpdated\":null,\"topEnvironments\":[],\"showProjects\":false,\"successfulCount\":0},\"byInstanceType\":{}},\"repository\":{\"overall\":{\"count\":0,\"lastUpdated\":null},\"byInstanceType\":{}},\"branch\":{\"overall\":{\"count\":0,\"lastUpdated\":null},\"byInstanceType\":{}}}},\"isStale\":true}}",
    "customfield_13002": null,
    "customfield_11023": null,
    "customfield_13005": null,
    "customfield_11300": null,
    "customfield_13004": null,
    "customfield_13007": null,
    "customfield_11301": null,
    "customfield_13006": null,
    "customfield_10800": null,
    "customfield_10801": null,
    "aggregatetimeestimate": null,
    "customfield_10802": null,
    "summary": "Twistlock - bloom-mq-handler:1.3.26, Fix By: 2024-11-27",
    "creator": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/user?username=rpjzn388",
      "name": "rpjzn388",
      "key": "JIRAUSER25389",
      "emailAddress": "<EMAIL>",
      "avatarUrls": {
        "48x48": "https://jira.dc2.intactlab.ca/secure/useravatar?ownerId=JIRAUSER25389&avatarId=21503",
        "24x24": "https://jira.dc2.intactlab.ca/secure/useravatar?size=small&ownerId=JIRAUSER25389&avatarId=21503",
        "16x16": "https://jira.dc2.intactlab.ca/secure/useravatar?size=xsmall&ownerId=JIRAUSER25389&avatarId=21503",
        "32x32": "https://jira.dc2.intactlab.ca/secure/useravatar?size=medium&ownerId=JIRAUSER25389&avatarId=21503"
      },
      "displayName": "Leonardo Dias de Oliveira",
      "active": true,
      "timeZone": "America/New_York"
    },
    "subtasks": [],
    "reporter": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/user?username=rpjzn388",
      "name": "rpjzn388",
      "key": "JIRAUSER25389",
      "emailAddress": "<EMAIL>",
      "avatarUrls": {
        "48x48": "https://jira.dc2.intactlab.ca/secure/useravatar?ownerId=JIRAUSER25389&avatarId=21503",
        "24x24": "https://jira.dc2.intactlab.ca/secure/useravatar?size=small&ownerId=JIRAUSER25389&avatarId=21503",
        "16x16": "https://jira.dc2.intactlab.ca/secure/useravatar?size=xsmall&ownerId=JIRAUSER25389&avatarId=21503",
        "32x32": "https://jira.dc2.intactlab.ca/secure/useravatar?size=medium&ownerId=JIRAUSER25389&avatarId=21503"
      },
      "displayName": "Leonardo Dias de Oliveira",
      "active": true,
      "timeZone": "America/New_York"
    },
    "customfield_12100": [
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/10900",
        "value": "Belair",
        "id": "10900",
        "disabled": false
      },
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/10901",
        "value": "BNA",
        "id": "10901",
        "disabled": false
      },
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/10902",
        "value": "Intact",
        "id": "10902",
        "disabled": false
      },
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/12514",
        "value": "Servus",
        "id": "12514",
        "disabled": false
      },
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/12515",
        "value": "Scotia",
        "id": "12515",
        "disabled": false
      }
    ],
    "aggregateprogress": {
      "progress": 0,
      "total": 0
    },
    "customfield_10200": null,
    "customfield_10400": null,
    "customfield_12500": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/11102",
      "value": "Arch_Required",
      "id": "11102",
      "disabled": false
    },
    "customfield_13900": null,
    "customfield_11204": null,
    "customfield_11006": null,
    "customfield_11205": null,
    "environment": null,
    "customfield_11800": null,
    "customfield_13903": null,
    "duedate": "2024-11-27",
    "progress": {
      "progress": 0,
      "total": 0
    },
    "votes": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/issue/MARS-2942/votes",
      "votes": 0,
      "hasVoted": false
    }
  }
}
{
  "expand": "operations,versionedRepresentations,editmeta,changelog,renderedFields",
  "id": "239419",
  "self": "https://jira.dc2.intactlab.ca/rest/api/2/issue/239419",
  "key": "MARS-2943",
  "fields": {
    "issuetype": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/issuetype/10002",
      "id": "10002",
      "description": "The sub-task of the issue",
      "iconUrl": "https://jira.dc2.intactlab.ca/secure/viewavatar?size=xsmall&avatarId=10316&avatarType=issuetype",
      "name": "Sub-task",
      "subtask": true,
      "avatarId": 10316
    },
    "parent": {
      "id": "239729",
      "key": "MARS-2964",
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/issue/239729",
      "fields": {
        "summary": "Goalkeeping PI.2024.4.1",
        "status": {
          "self": "https://jira.dc2.intactlab.ca/rest/api/2/status/10002",
          "description": "",
          "iconUrl": "https://jira.dc2.intactlab.ca/",
          "name": "Done",
          "id": "10002",
          "statusCategory": {
            "self": "https://jira.dc2.intactlab.ca/rest/api/2/statuscategory/3",
            "id": 3,
            "key": "done",
            "colorName": "success",
            "name": "Done"
          }
        },
        "priority": {
          "self": "https://jira.dc2.intactlab.ca/rest/api/2/priority/3",
          "iconUrl": "https://jira.dc2.intactlab.ca/images/icons/priorities/medium.svg",
          "name": "Medium",
          "id": "3"
        },
        "issuetype": {
          "self": "https://jira.dc2.intactlab.ca/rest/api/2/issuetype/10000",
          "id": "10000",
          "description": "Created by Jira Software - do not edit or delete. Issue type for a user story.",
          "iconUrl": "https://jira.dc2.intactlab.ca/images/icons/issuetypes/story.svg",
          "name": "Story",
          "subtask": false
        }
      }
    },
    "timespent": null,
    "customfield_13100": null,
    "project": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/project/14101",
      "id": "14101",
      "key": "MARS",
      "name": "The Martians",
      "projectTypeKey": "software",
      "avatarUrls": {
        "48x48": "https://jira.dc2.intactlab.ca/secure/projectavatar?pid=14101&avatarId=23504",
        "24x24": "https://jira.dc2.intactlab.ca/secure/projectavatar?size=small&pid=14101&avatarId=23504",
        "16x16": "https://jira.dc2.intactlab.ca/secure/projectavatar?size=xsmall&pid=14101&avatarId=23504",
        "32x32": "https://jira.dc2.intactlab.ca/secure/projectavatar?size=medium&pid=14101&avatarId=23504"
      }
    },
    "fixVersions": [
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/version/28478",
        "id": "28478",
        "description": "",
        "name": "Bloom + IRCA  + Auto/QuickQuote CVE Fix - MEP - November 25th",
        "archived": false,
        "released": false,
        "releaseDate": "2024-11-25"
      }
    ],
    "customfield_11001": null,
    "customfield_11002": null,
    "customfield_11200": null,
    "aggregatetimespent": null,
    "resolution": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/resolution/10000",
      "id": "10000",
      "description": "Work has been completed on this issue.",
      "name": "Done"
    },
    "customfield_11201": null,
    "customfield_11003": null,
    "customfield_11202": null,
    "customfield_11004": null,
    "customfield_11203": [],
    "customfield_11005": null,
    "customfield_10700": null,
    "customfield_10900": null,
    "resolutiondate": "2024-10-07T14:32:00.000-0400",
    "workratio": -1,
    "lastViewed": null,
    "watches": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/issue/MARS-2943/watchers",
      "watchCount": 1,
      "isWatching": true
    },
    "created": "2024-10-01T09:51:26.000-0400",
    "customfield_10020": null,
    "customfield_12202": null,
    "priority": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/priority/3",
      "iconUrl": "https://jira.dc2.intactlab.ca/images/icons/priorities/medium.svg",
      "name": "Medium",
      "id": "3"
    },
    "customfield_10100": null,
    "customfield_12201": null,
    "customfield_10024": [
      "com.atlassian.greenhopper.service.sprint.Sprint@5ab4e0ce[id=3550,rapidViewId=385,state=CLOSED,name=PI.2024.4.1,startDate=2024-10-03T13:00:00.000-04:00,endDate=2024-10-17T08:00:00.000-04:00,completeDate=2024-10-17T10:54:13.488-04:00,activatedDate=2024-10-03T12:02:38.404-04:00,sequence=3123,goal=1 - Release Lead Sharing in Prod by October 17th (Mid Month release)\n2- Make sure we are ready for the Broker admin release planned october 21st.\n3- Start work on Referal prgram,autoStartStop=false,synced=false]"
    ],
    "customfield_10025": "0|i0vj8k:",
    "labels": [
      "Twistlock"
    ],
    "customfield_10026": null,
    "customfield_11700": null,
    "customfield_13008": null,
    "customfield_11900": null,
    "customfield_11902": null,
    "timeestimate": null,
    "aggregatetimeoriginalestimate": null,
    "versions": [],
    "customfield_11901": null,
    "customfield_11906": null,
    "customfield_11905": null,
    "issuelinks": [],
    "assignee": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/user?username=rpmdh249",
      "name": "rpmdh249",
      "key": "JIRAUSER24818",
      "emailAddress": "<EMAIL>",
      "avatarUrls": {
        "48x48": "https://www.gravatar.com/avatar/9e331dcca191645ab52bbed1c8742ef3?d=mm&s=48",
        "24x24": "https://www.gravatar.com/avatar/9e331dcca191645ab52bbed1c8742ef3?d=mm&s=24",
        "16x16": "https://www.gravatar.com/avatar/9e331dcca191645ab52bbed1c8742ef3?d=mm&s=16",
        "32x32": "https://www.gravatar.com/avatar/9e331dcca191645ab52bbed1c8742ef3?d=mm&s=32"
      },
      "displayName": "Omar Bounouioua",
      "active": false,
      "timeZone": "America/New_York"
    },
    "updated": "2024-11-01T15:39:08.000-0400",
    "status": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/status/10002",
      "description": "",
      "iconUrl": "https://jira.dc2.intactlab.ca/",
      "name": "Done",
      "id": "10002",
      "statusCategory": {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/statuscategory/3",
        "id": 3,
        "key": "done",
        "colorName": "success",
        "name": "Done"
      }
    },
    "components": [],
    "timeoriginalestimate": null,
    "customfield_13001": null,
    "description": "|Vulnerability: CVE-2024-38816|\r\n|Scope: spring-webmvc, version: 6.0.19|\r\n|Recommendation: fixed in 6.1.13, 6.0.24, 5.3.40|\r\n|Severity: High|\r\n|Last Scan: 2024-09-30|\r\n|Fix By: 2024-11-27|\r\n|Overdue: No|",
    "customfield_13003": null,
    "customfield_11022": null,
    "customfield_11100": "{summaryBean=com.atlassian.jira.plugin.devstatus.rest.SummaryBean@94175d4[summary={pullrequest=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@3febd96f[overall=PullRequestOverallBean{stateCount=0, state='OPEN', details=PullRequestOverallDetails{openCount=0, mergedCount=0, declinedCount=0}},byInstanceType={}], build=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@2113fbdd[overall=com.atlassian.jira.plugin.devstatus.summary.beans.BuildOverallBean@40526e31[failedBuildCount=0,successfulBuildCount=0,unknownBuildCount=0,count=0,lastUpdated=<null>,lastUpdatedTimestamp=<null>],byInstanceType={}], review=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@27f76ee3[overall=com.atlassian.jira.plugin.devstatus.summary.beans.ReviewsOverallBean@72f1f473[stateCount=0,state=<null>,dueDate=<null>,overDue=false,count=0,lastUpdated=<null>,lastUpdatedTimestamp=<null>],byInstanceType={}], deployment-environment=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@4c0224e3[overall=com.atlassian.jira.plugin.devstatus.summary.beans.DeploymentOverallBean@1f72e8b4[topEnvironments=[],showProjects=false,successfulCount=0,count=0,lastUpdated=<null>,lastUpdatedTimestamp=<null>],byInstanceType={}], repository=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@36e1dcf[overall=com.atlassian.jira.plugin.devstatus.summary.beans.CommitOverallBean@7ec133bd[count=0,lastUpdated=<null>,lastUpdatedTimestamp=<null>],byInstanceType={}], branch=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@23b925ba[overall=com.atlassian.jira.plugin.devstatus.summary.beans.BranchOverallBean@2b065e31[count=0,lastUpdated=<null>,lastUpdatedTimestamp=<null>],byInstanceType={}]},errors=[],configErrors=[]], devSummaryJson={\"cachedValue\":{\"errors\":[],\"configErrors\":[],\"summary\":{\"pullrequest\":{\"overall\":{\"count\":0,\"lastUpdated\":null,\"stateCount\":0,\"state\":\"OPEN\",\"details\":{\"openCount\":0,\"mergedCount\":0,\"declinedCount\":0,\"total\":0},\"open\":true},\"byInstanceType\":{}},\"build\":{\"overall\":{\"count\":0,\"lastUpdated\":null,\"failedBuildCount\":0,\"successfulBuildCount\":0,\"unknownBuildCount\":0},\"byInstanceType\":{}},\"review\":{\"overall\":{\"count\":0,\"lastUpdated\":null,\"stateCount\":0,\"state\":null,\"dueDate\":null,\"overDue\":false,\"completed\":false},\"byInstanceType\":{}},\"deployment-environment\":{\"overall\":{\"count\":0,\"lastUpdated\":null,\"topEnvironments\":[],\"showProjects\":false,\"successfulCount\":0},\"byInstanceType\":{}},\"repository\":{\"overall\":{\"count\":0,\"lastUpdated\":null},\"byInstanceType\":{}},\"branch\":{\"overall\":{\"count\":0,\"lastUpdated\":null},\"byInstanceType\":{}}}},\"isStale\":true}}",
    "customfield_13002": null,
    "customfield_11023": null,
    "customfield_13005": null,
    "customfield_11300": null,
    "customfield_13004": null,
    "customfield_13007": null,
    "customfield_11301": null,
    "customfield_13006": null,
    "customfield_10800": null,
    "customfield_10801": null,
    "aggregatetimeestimate": null,
    "customfield_10802": null,
    "summary": "Twistlock - bloom-plp-processor:4.0.3, Fix By: 2024-11-27",
    "creator": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/user?username=rpjzn388",
      "name": "rpjzn388",
      "key": "JIRAUSER25389",
      "emailAddress": "<EMAIL>",
      "avatarUrls": {
        "48x48": "https://jira.dc2.intactlab.ca/secure/useravatar?ownerId=JIRAUSER25389&avatarId=21503",
        "24x24": "https://jira.dc2.intactlab.ca/secure/useravatar?size=small&ownerId=JIRAUSER25389&avatarId=21503",
        "16x16": "https://jira.dc2.intactlab.ca/secure/useravatar?size=xsmall&ownerId=JIRAUSER25389&avatarId=21503",
        "32x32": "https://jira.dc2.intactlab.ca/secure/useravatar?size=medium&ownerId=JIRAUSER25389&avatarId=21503"
      },
      "displayName": "Leonardo Dias de Oliveira",
      "active": true,
      "timeZone": "America/New_York"
    },
    "subtasks": [],
    "reporter": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/user?username=rpjzn388",
      "name": "rpjzn388",
      "key": "JIRAUSER25389",
      "emailAddress": "<EMAIL>",
      "avatarUrls": {
        "48x48": "https://jira.dc2.intactlab.ca/secure/useravatar?ownerId=JIRAUSER25389&avatarId=21503",
        "24x24": "https://jira.dc2.intactlab.ca/secure/useravatar?size=small&ownerId=JIRAUSER25389&avatarId=21503",
        "16x16": "https://jira.dc2.intactlab.ca/secure/useravatar?size=xsmall&ownerId=JIRAUSER25389&avatarId=21503",
        "32x32": "https://jira.dc2.intactlab.ca/secure/useravatar?size=medium&ownerId=JIRAUSER25389&avatarId=21503"
      },
      "displayName": "Leonardo Dias de Oliveira",
      "active": true,
      "timeZone": "America/New_York"
    },
    "customfield_12100": [
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/10900",
        "value": "Belair",
        "id": "10900",
        "disabled": false
      },
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/10901",
        "value": "BNA",
        "id": "10901",
        "disabled": false
      },
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/10902",
        "value": "Intact",
        "id": "10902",
        "disabled": false
      },
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/12514",
        "value": "Servus",
        "id": "12514",
        "disabled": false
      },
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/12515",
        "value": "Scotia",
        "id": "12515",
        "disabled": false
      }
    ],
    "aggregateprogress": {
      "progress": 0,
      "total": 0
    },
    "customfield_10200": null,
    "customfield_10400": null,
    "customfield_12500": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/11102",
      "value": "Arch_Required",
      "id": "11102",
      "disabled": false
    },
    "customfield_13900": null,
    "customfield_11204": null,
    "customfield_11006": null,
    "customfield_11205": null,
    "environment": null,
    "customfield_11800": null,
    "customfield_13903": null,
    "duedate": "2024-11-27",
    "progress": {
      "progress": 0,
      "total": 0
    },
    "votes": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/issue/MARS-2943/votes",
      "votes": 0,
      "hasVoted": false
    }
  }
}
{
  "expand": "operations,versionedRepresentations,editmeta,changelog,renderedFields",
  "id": "239420",
  "self": "https://jira.dc2.intactlab.ca/rest/api/2/issue/239420",
  "key": "MARS-2944",
  "fields": {
    "issuetype": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/issuetype/10002",
      "id": "10002",
      "description": "The sub-task of the issue",
      "iconUrl": "https://jira.dc2.intactlab.ca/secure/viewavatar?size=xsmall&avatarId=10316&avatarType=issuetype",
      "name": "Sub-task",
      "subtask": true,
      "avatarId": 10316
    },
    "parent": {
      "id": "239729",
      "key": "MARS-2964",
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/issue/239729",
      "fields": {
        "summary": "Goalkeeping PI.2024.4.1",
        "status": {
          "self": "https://jira.dc2.intactlab.ca/rest/api/2/status/10002",
          "description": "",
          "iconUrl": "https://jira.dc2.intactlab.ca/",
          "name": "Done",
          "id": "10002",
          "statusCategory": {
            "self": "https://jira.dc2.intactlab.ca/rest/api/2/statuscategory/3",
            "id": 3,
            "key": "done",
            "colorName": "success",
            "name": "Done"
          }
        },
        "priority": {
          "self": "https://jira.dc2.intactlab.ca/rest/api/2/priority/3",
          "iconUrl": "https://jira.dc2.intactlab.ca/images/icons/priorities/medium.svg",
          "name": "Medium",
          "id": "3"
        },
        "issuetype": {
          "self": "https://jira.dc2.intactlab.ca/rest/api/2/issuetype/10000",
          "id": "10000",
          "description": "Created by Jira Software - do not edit or delete. Issue type for a user story.",
          "iconUrl": "https://jira.dc2.intactlab.ca/images/icons/issuetypes/story.svg",
          "name": "Story",
          "subtask": false
        }
      }
    },
    "timespent": null,
    "customfield_13100": null,
    "project": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/project/14101",
      "id": "14101",
      "key": "MARS",
      "name": "The Martians",
      "projectTypeKey": "software",
      "avatarUrls": {
        "48x48": "https://jira.dc2.intactlab.ca/secure/projectavatar?pid=14101&avatarId=23504",
        "24x24": "https://jira.dc2.intactlab.ca/secure/projectavatar?size=small&pid=14101&avatarId=23504",
        "16x16": "https://jira.dc2.intactlab.ca/secure/projectavatar?size=xsmall&pid=14101&avatarId=23504",
        "32x32": "https://jira.dc2.intactlab.ca/secure/projectavatar?size=medium&pid=14101&avatarId=23504"
      }
    },
    "fixVersions": [
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/version/28478",
        "id": "28478",
        "description": "",
        "name": "Bloom + IRCA  + Auto/QuickQuote CVE Fix - MEP - November 25th",
        "archived": false,
        "released": false,
        "releaseDate": "2024-11-25"
      }
    ],
    "customfield_11001": null,
    "customfield_11002": null,
    "customfield_11200": null,
    "aggregatetimespent": null,
    "resolution": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/resolution/10000",
      "id": "10000",
      "description": "Work has been completed on this issue.",
      "name": "Done"
    },
    "customfield_11201": null,
    "customfield_11003": null,
    "customfield_11202": null,
    "customfield_11004": null,
    "customfield_11203": [],
    "customfield_11005": null,
    "customfield_10700": null,
    "customfield_10900": null,
    "resolutiondate": "2024-10-07T14:32:15.000-0400",
    "workratio": -1,
    "lastViewed": null,
    "watches": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/issue/MARS-2944/watchers",
      "watchCount": 1,
      "isWatching": true
    },
    "created": "2024-10-01T09:52:03.000-0400",
    "customfield_10020": null,
    "customfield_12202": null,
    "priority": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/priority/3",
      "iconUrl": "https://jira.dc2.intactlab.ca/images/icons/priorities/medium.svg",
      "name": "Medium",
      "id": "3"
    },
    "customfield_10100": null,
    "customfield_12201": null,
    "customfield_10024": [
      "com.atlassian.greenhopper.service.sprint.Sprint@5ab4e0ce[id=3550,rapidViewId=385,state=CLOSED,name=PI.2024.4.1,startDate=2024-10-03T13:00:00.000-04:00,endDate=2024-10-17T08:00:00.000-04:00,completeDate=2024-10-17T10:54:13.488-04:00,activatedDate=2024-10-03T12:02:38.404-04:00,sequence=3123,goal=1 - Release Lead Sharing in Prod by October 17th (Mid Month release)\n2- Make sure we are ready for the Broker admin release planned october 21st.\n3- Start work on Referal prgram,autoStartStop=false,synced=false]"
    ],
    "customfield_10025": "0|i0vj8s:",
    "labels": [
      "Twistlock"
    ],
    "customfield_10026": null,
    "customfield_11700": null,
    "customfield_13008": null,
    "customfield_11900": null,
    "customfield_11902": null,
    "timeestimate": null,
    "aggregatetimeoriginalestimate": null,
    "versions": [],
    "customfield_11901": null,
    "customfield_11906": null,
    "customfield_11905": null,
    "issuelinks": [],
    "assignee": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/user?username=rpmdh249",
      "name": "rpmdh249",
      "key": "JIRAUSER24818",
      "emailAddress": "<EMAIL>",
      "avatarUrls": {
        "48x48": "https://www.gravatar.com/avatar/9e331dcca191645ab52bbed1c8742ef3?d=mm&s=48",
        "24x24": "https://www.gravatar.com/avatar/9e331dcca191645ab52bbed1c8742ef3?d=mm&s=24",
        "16x16": "https://www.gravatar.com/avatar/9e331dcca191645ab52bbed1c8742ef3?d=mm&s=16",
        "32x32": "https://www.gravatar.com/avatar/9e331dcca191645ab52bbed1c8742ef3?d=mm&s=32"
      },
      "displayName": "Omar Bounouioua",
      "active": false,
      "timeZone": "America/New_York"
    },
    "updated": "2024-11-01T15:39:09.000-0400",
    "status": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/status/10002",
      "description": "",
      "iconUrl": "https://jira.dc2.intactlab.ca/",
      "name": "Done",
      "id": "10002",
      "statusCategory": {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/statuscategory/3",
        "id": 3,
        "key": "done",
        "colorName": "success",
        "name": "Done"
      }
    },
    "components": [],
    "timeoriginalestimate": null,
    "customfield_13001": null,
    "description": "|Vulnerability: CVE-2024-38816|\r\n|Scope: spring-webmvc, version: 6.0.19|\r\n|Recommendation: fixed in 6.1.13, 6.0.24, 5.3.40|\r\n|Severity: High|\r\n|Last Scan: 2024-09-30|\r\n|Fix By: 2024-11-27|\r\n|Overdue: No|",
    "customfield_13003": null,
    "customfield_11022": null,
    "customfield_11100": "{summaryBean=com.atlassian.jira.plugin.devstatus.rest.SummaryBean@7b7d733a[summary={pullrequest=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@3d7ec9e8[overall=PullRequestOverallBean{stateCount=0, state='OPEN', details=PullRequestOverallDetails{openCount=0, mergedCount=0, declinedCount=0}},byInstanceType={}], build=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@72c5035b[overall=com.atlassian.jira.plugin.devstatus.summary.beans.BuildOverallBean@393fb562[failedBuildCount=0,successfulBuildCount=0,unknownBuildCount=0,count=0,lastUpdated=<null>,lastUpdatedTimestamp=<null>],byInstanceType={}], review=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@205b9091[overall=com.atlassian.jira.plugin.devstatus.summary.beans.ReviewsOverallBean@484609f6[stateCount=0,state=<null>,dueDate=<null>,overDue=false,count=0,lastUpdated=<null>,lastUpdatedTimestamp=<null>],byInstanceType={}], deployment-environment=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@12c3147e[overall=com.atlassian.jira.plugin.devstatus.summary.beans.DeploymentOverallBean@561337e1[topEnvironments=[],showProjects=false,successfulCount=0,count=0,lastUpdated=<null>,lastUpdatedTimestamp=<null>],byInstanceType={}], repository=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@2ab0eaaa[overall=com.atlassian.jira.plugin.devstatus.summary.beans.CommitOverallBean@52414fb3[count=0,lastUpdated=<null>,lastUpdatedTimestamp=<null>],byInstanceType={}], branch=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@58abc161[overall=com.atlassian.jira.plugin.devstatus.summary.beans.BranchOverallBean@174849aa[count=0,lastUpdated=<null>,lastUpdatedTimestamp=<null>],byInstanceType={}]},errors=[],configErrors=[]], devSummaryJson={\"cachedValue\":{\"errors\":[],\"configErrors\":[],\"summary\":{\"pullrequest\":{\"overall\":{\"count\":0,\"lastUpdated\":null,\"stateCount\":0,\"state\":\"OPEN\",\"details\":{\"openCount\":0,\"mergedCount\":0,\"declinedCount\":0,\"total\":0},\"open\":true},\"byInstanceType\":{}},\"build\":{\"overall\":{\"count\":0,\"lastUpdated\":null,\"failedBuildCount\":0,\"successfulBuildCount\":0,\"unknownBuildCount\":0},\"byInstanceType\":{}},\"review\":{\"overall\":{\"count\":0,\"lastUpdated\":null,\"stateCount\":0,\"state\":null,\"dueDate\":null,\"overDue\":false,\"completed\":false},\"byInstanceType\":{}},\"deployment-environment\":{\"overall\":{\"count\":0,\"lastUpdated\":null,\"topEnvironments\":[],\"showProjects\":false,\"successfulCount\":0},\"byInstanceType\":{}},\"repository\":{\"overall\":{\"count\":0,\"lastUpdated\":null},\"byInstanceType\":{}},\"branch\":{\"overall\":{\"count\":0,\"lastUpdated\":null},\"byInstanceType\":{}}}},\"isStale\":true}}",
    "customfield_13002": null,
    "customfield_11023": null,
    "customfield_13005": null,
    "customfield_11300": null,
    "customfield_13004": null,
    "customfield_13007": null,
    "customfield_11301": null,
    "customfield_13006": null,
    "customfield_10800": null,
    "customfield_10801": null,
    "aggregatetimeestimate": null,
    "customfield_10802": null,
    "summary": "Twistlock - bloom-repository-service:3.0.35, Fix By: 2024-11-27",
    "creator": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/user?username=rpjzn388",
      "name": "rpjzn388",
      "key": "JIRAUSER25389",
      "emailAddress": "<EMAIL>",
      "avatarUrls": {
        "48x48": "https://jira.dc2.intactlab.ca/secure/useravatar?ownerId=JIRAUSER25389&avatarId=21503",
        "24x24": "https://jira.dc2.intactlab.ca/secure/useravatar?size=small&ownerId=JIRAUSER25389&avatarId=21503",
        "16x16": "https://jira.dc2.intactlab.ca/secure/useravatar?size=xsmall&ownerId=JIRAUSER25389&avatarId=21503",
        "32x32": "https://jira.dc2.intactlab.ca/secure/useravatar?size=medium&ownerId=JIRAUSER25389&avatarId=21503"
      },
      "displayName": "Leonardo Dias de Oliveira",
      "active": true,
      "timeZone": "America/New_York"
    },
    "subtasks": [],
    "reporter": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/user?username=rpjzn388",
      "name": "rpjzn388",
      "key": "JIRAUSER25389",
      "emailAddress": "<EMAIL>",
      "avatarUrls": {
        "48x48": "https://jira.dc2.intactlab.ca/secure/useravatar?ownerId=JIRAUSER25389&avatarId=21503",
        "24x24": "https://jira.dc2.intactlab.ca/secure/useravatar?size=small&ownerId=JIRAUSER25389&avatarId=21503",
        "16x16": "https://jira.dc2.intactlab.ca/secure/useravatar?size=xsmall&ownerId=JIRAUSER25389&avatarId=21503",
        "32x32": "https://jira.dc2.intactlab.ca/secure/useravatar?size=medium&ownerId=JIRAUSER25389&avatarId=21503"
      },
      "displayName": "Leonardo Dias de Oliveira",
      "active": true,
      "timeZone": "America/New_York"
    },
    "customfield_12100": [
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/10900",
        "value": "Belair",
        "id": "10900",
        "disabled": false
      },
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/10901",
        "value": "BNA",
        "id": "10901",
        "disabled": false
      },
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/10902",
        "value": "Intact",
        "id": "10902",
        "disabled": false
      },
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/12514",
        "value": "Servus",
        "id": "12514",
        "disabled": false
      },
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/12515",
        "value": "Scotia",
        "id": "12515",
        "disabled": false
      }
    ],
    "aggregateprogress": {
      "progress": 0,
      "total": 0
    },
    "customfield_10200": null,
    "customfield_10400": null,
    "customfield_12500": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/11102",
      "value": "Arch_Required",
      "id": "11102",
      "disabled": false
    },
    "customfield_13900": null,
    "customfield_11204": null,
    "customfield_11006": null,
    "customfield_11205": null,
    "environment": null,
    "customfield_11800": null,
    "customfield_13903": null,
    "duedate": "2024-11-27",
    "progress": {
      "progress": 0,
      "total": 0
    },
    "votes": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/issue/MARS-2944/votes",
      "votes": 0,
      "hasVoted": false
    }
  }
}
{
  "expand": "operations,versionedRepresentations,editmeta,changelog,renderedFields",
  "id": "239426",
  "self": "https://jira.dc2.intactlab.ca/rest/api/2/issue/239426",
  "key": "MARS-2947",
  "fields": {
    "issuetype": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/issuetype/10002",
      "id": "10002",
      "description": "The sub-task of the issue",
      "iconUrl": "https://jira.dc2.intactlab.ca/secure/viewavatar?size=xsmall&avatarId=10316&avatarType=issuetype",
      "name": "Sub-task",
      "subtask": true,
      "avatarId": 10316
    },
    "parent": {
      "id": "239729",
      "key": "MARS-2964",
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/issue/239729",
      "fields": {
        "summary": "Goalkeeping PI.2024.4.1",
        "status": {
          "self": "https://jira.dc2.intactlab.ca/rest/api/2/status/10002",
          "description": "",
          "iconUrl": "https://jira.dc2.intactlab.ca/",
          "name": "Done",
          "id": "10002",
          "statusCategory": {
            "self": "https://jira.dc2.intactlab.ca/rest/api/2/statuscategory/3",
            "id": 3,
            "key": "done",
            "colorName": "success",
            "name": "Done"
          }
        },
        "priority": {
          "self": "https://jira.dc2.intactlab.ca/rest/api/2/priority/3",
          "iconUrl": "https://jira.dc2.intactlab.ca/images/icons/priorities/medium.svg",
          "name": "Medium",
          "id": "3"
        },
        "issuetype": {
          "self": "https://jira.dc2.intactlab.ca/rest/api/2/issuetype/10000",
          "id": "10000",
          "description": "Created by Jira Software - do not edit or delete. Issue type for a user story.",
          "iconUrl": "https://jira.dc2.intactlab.ca/images/icons/issuetypes/story.svg",
          "name": "Story",
          "subtask": false
        }
      }
    },
    "timespent": null,
    "customfield_13100": null,
    "project": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/project/14101",
      "id": "14101",
      "key": "MARS",
      "name": "The Martians",
      "projectTypeKey": "software",
      "avatarUrls": {
        "48x48": "https://jira.dc2.intactlab.ca/secure/projectavatar?pid=14101&avatarId=23504",
        "24x24": "https://jira.dc2.intactlab.ca/secure/projectavatar?size=small&pid=14101&avatarId=23504",
        "16x16": "https://jira.dc2.intactlab.ca/secure/projectavatar?size=xsmall&pid=14101&avatarId=23504",
        "32x32": "https://jira.dc2.intactlab.ca/secure/projectavatar?size=medium&pid=14101&avatarId=23504"
      }
    },
    "fixVersions": [
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/version/27947",
        "id": "27947",
        "description": "Release critical security fixes to prod as planned end of PI 3",
        "name": "Broker Admin Release - October 24th",
        "archived": false,
        "released": true,
        "releaseDate": "2024-10-24"
      }
    ],
    "customfield_11001": null,
    "customfield_11002": null,
    "customfield_11200": null,
    "aggregatetimespent": null,
    "resolution": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/resolution/10000",
      "id": "10000",
      "description": "Work has been completed on this issue.",
      "name": "Done"
    },
    "customfield_11201": null,
    "customfield_11003": null,
    "customfield_11202": null,
    "customfield_11004": null,
    "customfield_11203": [],
    "customfield_11005": null,
    "customfield_10700": null,
    "customfield_10900": null,
    "resolutiondate": "2024-10-09T16:53:10.000-0400",
    "workratio": -1,
    "lastViewed": "2024-11-04T13:51:50.273-0500",
    "watches": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/issue/MARS-2947/watchers",
      "watchCount": 1,
      "isWatching": true
    },
    "created": "2024-10-01T09:54:13.000-0400",
    "customfield_10020": null,
    "customfield_12202": null,
    "priority": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/priority/3",
      "iconUrl": "https://jira.dc2.intactlab.ca/images/icons/priorities/medium.svg",
      "name": "Medium",
      "id": "3"
    },
    "customfield_10100": null,
    "customfield_12201": null,
    "customfield_10024": [
      "com.atlassian.greenhopper.service.sprint.Sprint@5ab4e0ce[id=3550,rapidViewId=385,state=CLOSED,name=PI.2024.4.1,startDate=2024-10-03T13:00:00.000-04:00,endDate=2024-10-17T08:00:00.000-04:00,completeDate=2024-10-17T10:54:13.488-04:00,activatedDate=2024-10-03T12:02:38.404-04:00,sequence=3123,goal=1 - Release Lead Sharing in Prod by October 17th (Mid Month release)\n2- Make sure we are ready for the Broker admin release planned october 21st.\n3- Start work on Referal prgram,autoStartStop=false,synced=false]"
    ],
    "customfield_10025": "0|i0vj9w:",
    "labels": [
      "Twistlock"
    ],
    "customfield_10026": null,
    "customfield_11700": null,
    "customfield_13008": null,
    "customfield_11900": null,
    "customfield_11902": null,
    "timeestimate": null,
    "aggregatetimeoriginalestimate": null,
    "versions": [],
    "customfield_11901": null,
    "customfield_11906": null,
    "customfield_11905": null,
    "issuelinks": [],
    "assignee": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/user?username=rpmdh249",
      "name": "rpmdh249",
      "key": "JIRAUSER24818",
      "emailAddress": "<EMAIL>",
      "avatarUrls": {
        "48x48": "https://www.gravatar.com/avatar/9e331dcca191645ab52bbed1c8742ef3?d=mm&s=48",
        "24x24": "https://www.gravatar.com/avatar/9e331dcca191645ab52bbed1c8742ef3?d=mm&s=24",
        "16x16": "https://www.gravatar.com/avatar/9e331dcca191645ab52bbed1c8742ef3?d=mm&s=16",
        "32x32": "https://www.gravatar.com/avatar/9e331dcca191645ab52bbed1c8742ef3?d=mm&s=32"
      },
      "displayName": "Omar Bounouioua",
      "active": false,
      "timeZone": "America/New_York"
    },
    "updated": "2024-11-04T13:51:49.000-0500",
    "status": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/status/10002",
      "description": "",
      "iconUrl": "https://jira.dc2.intactlab.ca/",
      "name": "Done",
      "id": "10002",
      "statusCategory": {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/statuscategory/3",
        "id": 3,
        "key": "done",
        "colorName": "success",
        "name": "Done"
      }
    },
    "components": [],
    "timeoriginalestimate": null,
    "customfield_13001": null,
    "description": "|Vulnerability: CVE-2024-34750|\r\n|Scope: tomcat-coyote, version: 9.0.89|\r\n|Recommendation: fixed in 11.0.0-M21, 10.1.25, 9.0.90|\r\n|Severity: High|\r\n|Last Scan: 2024-09-30|\r\n|Fix By: 2024-11-27|\r\n|Overdue: No|",
    "customfield_13003": null,
    "customfield_11022": null,
    "customfield_11100": "{summaryBean=com.atlassian.jira.plugin.devstatus.rest.SummaryBean@5079614b[summary={pullrequest=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@41bee580[overall=PullRequestOverallBean{stateCount=0, state='OPEN', details=PullRequestOverallDetails{openCount=0, mergedCount=0, declinedCount=0}},byInstanceType={}], build=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@4675eaf9[overall=com.atlassian.jira.plugin.devstatus.summary.beans.BuildOverallBean@70d19552[failedBuildCount=0,successfulBuildCount=0,unknownBuildCount=0,count=0,lastUpdated=<null>,lastUpdatedTimestamp=<null>],byInstanceType={}], review=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@27181d57[overall=com.atlassian.jira.plugin.devstatus.summary.beans.ReviewsOverallBean@17eca677[stateCount=0,state=<null>,dueDate=<null>,overDue=false,count=0,lastUpdated=<null>,lastUpdatedTimestamp=<null>],byInstanceType={}], deployment-environment=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@7e27a451[overall=com.atlassian.jira.plugin.devstatus.summary.beans.DeploymentOverallBean@2a0c5c2[topEnvironments=[],showProjects=false,successfulCount=0,count=0,lastUpdated=<null>,lastUpdatedTimestamp=<null>],byInstanceType={}], repository=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@65359264[overall=com.atlassian.jira.plugin.devstatus.summary.beans.CommitOverallBean@17985fab[count=0,lastUpdated=<null>,lastUpdatedTimestamp=<null>],byInstanceType={}], branch=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@545e7425[overall=com.atlassian.jira.plugin.devstatus.summary.beans.BranchOverallBean@50d435b9[count=0,lastUpdated=<null>,lastUpdatedTimestamp=<null>],byInstanceType={}]},errors=[],configErrors=[]], devSummaryJson={\"cachedValue\":{\"errors\":[],\"configErrors\":[],\"summary\":{\"pullrequest\":{\"overall\":{\"count\":0,\"lastUpdated\":null,\"stateCount\":0,\"state\":\"OPEN\",\"details\":{\"openCount\":0,\"mergedCount\":0,\"declinedCount\":0,\"total\":0},\"open\":true},\"byInstanceType\":{}},\"build\":{\"overall\":{\"count\":0,\"lastUpdated\":null,\"failedBuildCount\":0,\"successfulBuildCount\":0,\"unknownBuildCount\":0},\"byInstanceType\":{}},\"review\":{\"overall\":{\"count\":0,\"lastUpdated\":null,\"stateCount\":0,\"state\":null,\"dueDate\":null,\"overDue\":false,\"completed\":false},\"byInstanceType\":{}},\"deployment-environment\":{\"overall\":{\"count\":0,\"lastUpdated\":null,\"topEnvironments\":[],\"showProjects\":false,\"successfulCount\":0},\"byInstanceType\":{}},\"repository\":{\"overall\":{\"count\":0,\"lastUpdated\":null},\"byInstanceType\":{}},\"branch\":{\"overall\":{\"count\":0,\"lastUpdated\":null},\"byInstanceType\":{}}}},\"isStale\":true}}",
    "customfield_13002": null,
    "customfield_11023": null,
    "customfield_13005": null,
    "customfield_11300": null,
    "customfield_13004": null,
    "customfield_13007": null,
    "customfield_11301": null,
    "customfield_13006": null,
    "customfield_10800": null,
    "customfield_10801": null,
    "aggregatetimeestimate": null,
    "customfield_10802": null,
    "summary": "Twistlock - broker-admin-app:1.0.7, Fix By: 2024-11-27",
    "creator": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/user?username=rpjzn388",
      "name": "rpjzn388",
      "key": "JIRAUSER25389",
      "emailAddress": "<EMAIL>",
      "avatarUrls": {
        "48x48": "https://jira.dc2.intactlab.ca/secure/useravatar?ownerId=JIRAUSER25389&avatarId=21503",
        "24x24": "https://jira.dc2.intactlab.ca/secure/useravatar?size=small&ownerId=JIRAUSER25389&avatarId=21503",
        "16x16": "https://jira.dc2.intactlab.ca/secure/useravatar?size=xsmall&ownerId=JIRAUSER25389&avatarId=21503",
        "32x32": "https://jira.dc2.intactlab.ca/secure/useravatar?size=medium&ownerId=JIRAUSER25389&avatarId=21503"
      },
      "displayName": "Leonardo Dias de Oliveira",
      "active": true,
      "timeZone": "America/New_York"
    },
    "subtasks": [],
    "reporter": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/user?username=rpjzn388",
      "name": "rpjzn388",
      "key": "JIRAUSER25389",
      "emailAddress": "<EMAIL>",
      "avatarUrls": {
        "48x48": "https://jira.dc2.intactlab.ca/secure/useravatar?ownerId=JIRAUSER25389&avatarId=21503",
        "24x24": "https://jira.dc2.intactlab.ca/secure/useravatar?size=small&ownerId=JIRAUSER25389&avatarId=21503",
        "16x16": "https://jira.dc2.intactlab.ca/secure/useravatar?size=xsmall&ownerId=JIRAUSER25389&avatarId=21503",
        "32x32": "https://jira.dc2.intactlab.ca/secure/useravatar?size=medium&ownerId=JIRAUSER25389&avatarId=21503"
      },
      "displayName": "Leonardo Dias de Oliveira",
      "active": true,
      "timeZone": "America/New_York"
    },
    "customfield_12100": [
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/10900",
        "value": "Belair",
        "id": "10900",
        "disabled": false
      },
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/10901",
        "value": "BNA",
        "id": "10901",
        "disabled": false
      },
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/10902",
        "value": "Intact",
        "id": "10902",
        "disabled": false
      },
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/12514",
        "value": "Servus",
        "id": "12514",
        "disabled": false
      },
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/12515",
        "value": "Scotia",
        "id": "12515",
        "disabled": false
      }
    ],
    "aggregateprogress": {
      "progress": 0,
      "total": 0
    },
    "customfield_10200": null,
    "customfield_10400": null,
    "customfield_12500": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/11102",
      "value": "Arch_Required",
      "id": "11102",
      "disabled": false
    },
    "customfield_13900": null,
    "customfield_11204": null,
    "customfield_11006": null,
    "customfield_11205": null,
    "environment": null,
    "customfield_11800": null,
    "customfield_13903": null,
    "duedate": "2024-11-27",
    "progress": {
      "progress": 0,
      "total": 0
    },
    "votes": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/issue/MARS-2947/votes",
      "votes": 0,
      "hasVoted": false
    }
  }
}
{
  "expand": "operations,versionedRepresentations,editmeta,changelog,renderedFields",
  "id": "243971",
  "self": "https://jira.dc2.intactlab.ca/rest/api/2/issue/243971",
  "key": "MARS-3158",
  "fields": {
    "issuetype": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/issuetype/10002",
      "id": "10002",
      "description": "The sub-task of the issue",
      "iconUrl": "https://jira.dc2.intactlab.ca/secure/viewavatar?size=xsmall&avatarId=10316&avatarType=issuetype",
      "name": "Sub-task",
      "subtask": true,
      "avatarId": 10316
    },
    "parent": {
      "id": "239731",
      "key": "MARS-2966",
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/issue/239731",
      "fields": {
        "summary": "Goalkeeping PI.2024.4.3",
        "status": {
          "self": "https://jira.dc2.intactlab.ca/rest/api/2/status/3",
          "description": "This issue is being actively worked on at the moment by the assignee.",
          "iconUrl": "https://jira.dc2.intactlab.ca/images/icons/statuses/inprogress.png",
          "name": "In Progress",
          "id": "3",
          "statusCategory": {
            "self": "https://jira.dc2.intactlab.ca/rest/api/2/statuscategory/4",
            "id": 4,
            "key": "indeterminate",
            "colorName": "inprogress",
            "name": "In Progress"
          }
        },
        "priority": {
          "self": "https://jira.dc2.intactlab.ca/rest/api/2/priority/3",
          "iconUrl": "https://jira.dc2.intactlab.ca/images/icons/priorities/medium.svg",
          "name": "Medium",
          "id": "3"
        },
        "issuetype": {
          "self": "https://jira.dc2.intactlab.ca/rest/api/2/issuetype/10000",
          "id": "10000",
          "description": "Created by Jira Software - do not edit or delete. Issue type for a user story.",
          "iconUrl": "https://jira.dc2.intactlab.ca/images/icons/issuetypes/story.svg",
          "name": "Story",
          "subtask": false
        }
      }
    },
    "timespent": null,
    "customfield_13100": null,
    "project": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/project/14101",
      "id": "14101",
      "key": "MARS",
      "name": "The Martians",
      "projectTypeKey": "software",
      "avatarUrls": {
        "48x48": "https://jira.dc2.intactlab.ca/secure/projectavatar?pid=14101&avatarId=23504",
        "24x24": "https://jira.dc2.intactlab.ca/secure/projectavatar?size=small&pid=14101&avatarId=23504",
        "16x16": "https://jira.dc2.intactlab.ca/secure/projectavatar?size=xsmall&pid=14101&avatarId=23504",
        "32x32": "https://jira.dc2.intactlab.ca/secure/projectavatar?size=medium&pid=14101&avatarId=23504"
      }
    },
    "fixVersions": [],
    "customfield_11001": null,
    "customfield_11002": null,
    "customfield_11200": null,
    "aggregatetimespent": null,
    "resolution": null,
    "customfield_11201": null,
    "customfield_11003": null,
    "customfield_11202": null,
    "customfield_11004": null,
    "customfield_11203": [],
    "customfield_11005": null,
    "customfield_10700": null,
    "customfield_10900": null,
    "resolutiondate": null,
    "workratio": -1,
    "lastViewed": "2024-11-05T16:03:36.415-0500",
    "watches": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/issue/MARS-3158/watchers",
      "watchCount": 1,
      "isWatching": true
    },
    "created": "2024-11-05T15:53:05.000-0500",
    "customfield_10020": null,
    "customfield_12202": null,
    "priority": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/priority/3",
      "iconUrl": "https://jira.dc2.intactlab.ca/images/icons/priorities/medium.svg",
      "name": "Medium",
      "id": "3"
    },
    "customfield_10100": null,
    "customfield_12201": null,
    "customfield_10024": [
      "com.atlassian.greenhopper.service.sprint.Sprint@5685d2fb[id=3552,rapidViewId=385,state=ACTIVE,name=PI.2024.4.3,startDate=2024-10-31T05:00:00.000-04:00,endDate=2024-11-13T16:00:00.000-05:00,completeDate=<null>,activatedDate=2024-11-01T09:28:12.986-04:00,sequence=3304,goal=,autoStartStop=false,synced=false]"
    ],
    "customfield_10025": "0|i0w878:",
    "labels": [
      "Twistlock"
    ],
    "customfield_10026": null,
    "customfield_11700": null,
    "customfield_13008": null,
    "customfield_11900": null,
    "customfield_11902": null,
    "timeestimate": null,
    "aggregatetimeoriginalestimate": null,
    "versions": [],
    "customfield_11901": null,
    "customfield_11906": null,
    "customfield_11905": null,
    "issuelinks": [],
    "assignee": null,
    "updated": "2024-11-05T16:03:36.000-0500",
    "status": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/status/10100",
      "description": "",
      "iconUrl": "https://jira.dc2.intactlab.ca/",
      "name": "To Do",
      "id": "10100",
      "statusCategory": {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/statuscategory/2",
        "id": 2,
        "key": "new",
        "colorName": "default",
        "name": "To Do"
      }
    },
    "components": [],
    "timeoriginalestimate": null,
    "customfield_13001": null,
    "description": "Vulnerability: CVE-2024-47554\r\nScope: commons-io_commons-io, version: 2.4\r\nRecommendation: fixed in 2.14.0\r\nSeverity: High\r\nLast Scan: 2024-11-01\r\nFix By: 2024-12-03\r\nOverdue: No",
    "customfield_13003": null,
    "customfield_11022": null,
    "customfield_11100": "{summaryBean=com.atlassian.jira.plugin.devstatus.rest.SummaryBean@fd7a919[summary={pullrequest=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@10c4e4ee[overall=PullRequestOverallBean{stateCount=0, state='OPEN', details=PullRequestOverallDetails{openCount=0, mergedCount=0, declinedCount=0}},byInstanceType={}], build=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@1695e6d0[overall=com.atlassian.jira.plugin.devstatus.summary.beans.BuildOverallBean@21419378[failedBuildCount=0,successfulBuildCount=0,unknownBuildCount=0,count=0,lastUpdated=<null>,lastUpdatedTimestamp=<null>],byInstanceType={}], review=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@312b7bce[overall=com.atlassian.jira.plugin.devstatus.summary.beans.ReviewsOverallBean@5516d0a0[stateCount=0,state=<null>,dueDate=<null>,overDue=false,count=0,lastUpdated=<null>,lastUpdatedTimestamp=<null>],byInstanceType={}], deployment-environment=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@6c0c1fb4[overall=com.atlassian.jira.plugin.devstatus.summary.beans.DeploymentOverallBean@41fb8b1e[topEnvironments=[],showProjects=false,successfulCount=0,count=0,lastUpdated=<null>,lastUpdatedTimestamp=<null>],byInstanceType={}], repository=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@2be46de4[overall=com.atlassian.jira.plugin.devstatus.summary.beans.CommitOverallBean@48942be8[count=0,lastUpdated=<null>,lastUpdatedTimestamp=<null>],byInstanceType={}], branch=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@44e97cf1[overall=com.atlassian.jira.plugin.devstatus.summary.beans.BranchOverallBean@5957ac09[count=0,lastUpdated=<null>,lastUpdatedTimestamp=<null>],byInstanceType={}]},errors=[],configErrors=[]], devSummaryJson={\"cachedValue\":{\"errors\":[],\"configErrors\":[],\"summary\":{\"pullrequest\":{\"overall\":{\"count\":0,\"lastUpdated\":null,\"stateCount\":0,\"state\":\"OPEN\",\"details\":{\"openCount\":0,\"mergedCount\":0,\"declinedCount\":0,\"total\":0},\"open\":true},\"byInstanceType\":{}},\"build\":{\"overall\":{\"count\":0,\"lastUpdated\":null,\"failedBuildCount\":0,\"successfulBuildCount\":0,\"unknownBuildCount\":0},\"byInstanceType\":{}},\"review\":{\"overall\":{\"count\":0,\"lastUpdated\":null,\"stateCount\":0,\"state\":null,\"dueDate\":null,\"overDue\":false,\"completed\":false},\"byInstanceType\":{}},\"deployment-environment\":{\"overall\":{\"count\":0,\"lastUpdated\":null,\"topEnvironments\":[],\"showProjects\":false,\"successfulCount\":0},\"byInstanceType\":{}},\"repository\":{\"overall\":{\"count\":0,\"lastUpdated\":null},\"byInstanceType\":{}},\"branch\":{\"overall\":{\"count\":0,\"lastUpdated\":null},\"byInstanceType\":{}}}},\"isStale\":true}}",
    "customfield_13002": null,
    "customfield_11023": null,
    "customfield_13005": null,
    "customfield_11300": null,
    "customfield_13004": null,
    "customfield_13007": null,
    "customfield_11301": null,
    "customfield_13006": null,
    "customfield_10800": null,
    "customfield_10801": null,
    "aggregatetimeestimate": null,
    "customfield_10802": null,
    "summary": "Twistlock: webzone-app:4.0.7, Fix by: 2024-12-03",
    "creator": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/user?username=rpjzn388",
      "name": "rpjzn388",
      "key": "JIRAUSER25389",
      "emailAddress": "<EMAIL>",
      "avatarUrls": {
        "48x48": "https://jira.dc2.intactlab.ca/secure/useravatar?ownerId=JIRAUSER25389&avatarId=21503",
        "24x24": "https://jira.dc2.intactlab.ca/secure/useravatar?size=small&ownerId=JIRAUSER25389&avatarId=21503",
        "16x16": "https://jira.dc2.intactlab.ca/secure/useravatar?size=xsmall&ownerId=JIRAUSER25389&avatarId=21503",
        "32x32": "https://jira.dc2.intactlab.ca/secure/useravatar?size=medium&ownerId=JIRAUSER25389&avatarId=21503"
      },
      "displayName": "Leonardo Dias de Oliveira",
      "active": true,
      "timeZone": "America/New_York"
    },
    "subtasks": [],
    "reporter": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/user?username=rpjzn388",
      "name": "rpjzn388",
      "key": "JIRAUSER25389",
      "emailAddress": "<EMAIL>",
      "avatarUrls": {
        "48x48": "https://jira.dc2.intactlab.ca/secure/useravatar?ownerId=JIRAUSER25389&avatarId=21503",
        "24x24": "https://jira.dc2.intactlab.ca/secure/useravatar?size=small&ownerId=JIRAUSER25389&avatarId=21503",
        "16x16": "https://jira.dc2.intactlab.ca/secure/useravatar?size=xsmall&ownerId=JIRAUSER25389&avatarId=21503",
        "32x32": "https://jira.dc2.intactlab.ca/secure/useravatar?size=medium&ownerId=JIRAUSER25389&avatarId=21503"
      },
      "displayName": "Leonardo Dias de Oliveira",
      "active": true,
      "timeZone": "America/New_York"
    },
    "customfield_12100": [
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/10900",
        "value": "Belair",
        "id": "10900",
        "disabled": false
      },
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/10901",
        "value": "BNA",
        "id": "10901",
        "disabled": false
      },
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/10902",
        "value": "Intact",
        "id": "10902",
        "disabled": false
      },
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/12514",
        "value": "Servus",
        "id": "12514",
        "disabled": false
      },
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/12515",
        "value": "Scotia",
        "id": "12515",
        "disabled": false
      }
    ],
    "aggregateprogress": {
      "progress": 0,
      "total": 0
    },
    "customfield_10200": null,
    "customfield_10400": null,
    "customfield_12500": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/11102",
      "value": "Arch_Required",
      "id": "11102",
      "disabled": false
    },
    "customfield_13900": null,
    "customfield_11204": null,
    "customfield_11006": null,
    "customfield_11205": null,
    "environment": null,
    "customfield_11800": null,
    "customfield_13903": null,
    "duedate": "2024-12-03",
    "progress": {
      "progress": 0,
      "total": 0
    },
    "votes": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/issue/MARS-3158/votes",
      "votes": 0,
      "hasVoted": false
    }
  }
}
{
  "expand": "operations,versionedRepresentations,editmeta,changelog,renderedFields",
  "id": "243486",
  "self": "https://jira.dc2.intactlab.ca/rest/api/2/issue/243486",
  "key": "MARS-3149",
  "fields": {
    "issuetype": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/issuetype/10002",
      "id": "10002",
      "description": "The sub-task of the issue",
      "iconUrl": "https://jira.dc2.intactlab.ca/secure/viewavatar?size=xsmall&avatarId=10316&avatarType=issuetype",
      "name": "Sub-task",
      "subtask": true,
      "avatarId": 10316
    },
    "parent": {
      "id": "239731",
      "key": "MARS-2966",
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/issue/239731",
      "fields": {
        "summary": "Goalkeeping PI.2024.4.3",
        "status": {
          "self": "https://jira.dc2.intactlab.ca/rest/api/2/status/3",
          "description": "This issue is being actively worked on at the moment by the assignee.",
          "iconUrl": "https://jira.dc2.intactlab.ca/images/icons/statuses/inprogress.png",
          "name": "In Progress",
          "id": "3",
          "statusCategory": {
            "self": "https://jira.dc2.intactlab.ca/rest/api/2/statuscategory/4",
            "id": 4,
            "key": "indeterminate",
            "colorName": "inprogress",
            "name": "In Progress"
          }
        },
        "priority": {
          "self": "https://jira.dc2.intactlab.ca/rest/api/2/priority/3",
          "iconUrl": "https://jira.dc2.intactlab.ca/images/icons/priorities/medium.svg",
          "name": "Medium",
          "id": "3"
        },
        "issuetype": {
          "self": "https://jira.dc2.intactlab.ca/rest/api/2/issuetype/10000",
          "id": "10000",
          "description": "Created by Jira Software - do not edit or delete. Issue type for a user story.",
          "iconUrl": "https://jira.dc2.intactlab.ca/images/icons/issuetypes/story.svg",
          "name": "Story",
          "subtask": false
        }
      }
    },
    "timespent": null,
    "customfield_13100": null,
    "project": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/project/14101",
      "id": "14101",
      "key": "MARS",
      "name": "The Martians",
      "projectTypeKey": "software",
      "avatarUrls": {
        "48x48": "https://jira.dc2.intactlab.ca/secure/projectavatar?pid=14101&avatarId=23504",
        "24x24": "https://jira.dc2.intactlab.ca/secure/projectavatar?size=small&pid=14101&avatarId=23504",
        "16x16": "https://jira.dc2.intactlab.ca/secure/projectavatar?size=xsmall&pid=14101&avatarId=23504",
        "32x32": "https://jira.dc2.intactlab.ca/secure/projectavatar?size=medium&pid=14101&avatarId=23504"
      }
    },
    "fixVersions": [],
    "customfield_11001": null,
    "customfield_11002": null,
    "customfield_11200": null,
    "aggregatetimespent": null,
    "resolution": null,
    "customfield_11201": null,
    "customfield_11003": null,
    "customfield_11202": null,
    "customfield_11004": null,
    "customfield_11203": [],
    "customfield_11005": null,
    "customfield_10700": null,
    "customfield_10900": null,
    "resolutiondate": null,
    "workratio": -1,
    "lastViewed": "2024-11-06T09:48:35.466-0500",
    "watches": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/issue/MARS-3149/watchers",
      "watchCount": 1,
      "isWatching": true
    },
    "created": "2024-11-01T15:03:30.000-0400",
    "customfield_10020": null,
    "customfield_12202": null,
    "priority": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/priority/3",
      "iconUrl": "https://jira.dc2.intactlab.ca/images/icons/priorities/medium.svg",
      "name": "Medium",
      "id": "3"
    },
    "customfield_10100": null,
    "customfield_12201": null,
    "customfield_10024": [
      "com.atlassian.greenhopper.service.sprint.Sprint@5685d2fb[id=3552,rapidViewId=385,state=ACTIVE,name=PI.2024.4.3,startDate=2024-10-31T05:00:00.000-04:00,endDate=2024-11-13T16:00:00.000-05:00,completeDate=<null>,activatedDate=2024-11-01T09:28:12.986-04:00,sequence=3304,goal=,autoStartStop=false,synced=false]"
    ],
    "customfield_10025": "0|i0w5i4:",
    "labels": [
      "Twistlock"
    ],
    "customfield_10026": null,
    "customfield_11700": null,
    "customfield_13008": null,
    "customfield_11900": null,
    "customfield_11902": null,
    "timeestimate": null,
    "aggregatetimeoriginalestimate": null,
    "versions": [],
    "customfield_11901": null,
    "customfield_11906": null,
    "customfield_11905": null,
    "issuelinks": [],
    "assignee": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/user?username=rpjzn388",
      "name": "rpjzn388",
      "key": "JIRAUSER25389",
      "emailAddress": "<EMAIL>",
      "avatarUrls": {
        "48x48": "https://jira.dc2.intactlab.ca/secure/useravatar?ownerId=JIRAUSER25389&avatarId=21503",
        "24x24": "https://jira.dc2.intactlab.ca/secure/useravatar?size=small&ownerId=JIRAUSER25389&avatarId=21503",
        "16x16": "https://jira.dc2.intactlab.ca/secure/useravatar?size=xsmall&ownerId=JIRAUSER25389&avatarId=21503",
        "32x32": "https://jira.dc2.intactlab.ca/secure/useravatar?size=medium&ownerId=JIRAUSER25389&avatarId=21503"
      },
      "displayName": "Leonardo Dias de Oliveira",
      "active": true,
      "timeZone": "America/New_York"
    },
    "updated": "2024-11-05T16:06:50.000-0500",
    "status": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/status/3",
      "description": "This issue is being actively worked on at the moment by the assignee.",
      "iconUrl": "https://jira.dc2.intactlab.ca/images/icons/statuses/inprogress.png",
      "name": "In Progress",
      "id": "3",
      "statusCategory": {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/statuscategory/4",
        "id": 4,
        "key": "indeterminate",
        "colorName": "inprogress",
        "name": "In Progress"
      }
    },
    "components": [],
    "timeoriginalestimate": null,
    "customfield_13001": null,
    "description": "|Vulnerability: CVE-2016-1000027|\r\n|Scope: spring-web, version: 5.3.34|\r\n|Recommendation: fixed in 6.0.0|\r\n|Severity: Critical|\r\n|Last Scan: 2024-11-01|\r\n|Fix By: 2024-12-24|\r\n|Overdue: No|\r\n| |\r\n ",
    "customfield_13003": null,
    "customfield_11022": null,
    "customfield_11100": "{summaryBean=com.atlassian.jira.plugin.devstatus.rest.SummaryBean@16dc8dc5[summary={pullrequest=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@6c7109fe[overall=PullRequestOverallBean{stateCount=0, state='OPEN', details=PullRequestOverallDetails{openCount=0, mergedCount=0, declinedCount=0}},byInstanceType={}], build=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@16dd219e[overall=com.atlassian.jira.plugin.devstatus.summary.beans.BuildOverallBean@7207f442[failedBuildCount=0,successfulBuildCount=0,unknownBuildCount=0,count=0,lastUpdated=<null>,lastUpdatedTimestamp=<null>],byInstanceType={}], review=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@159482b7[overall=com.atlassian.jira.plugin.devstatus.summary.beans.ReviewsOverallBean@18579c3a[stateCount=0,state=<null>,dueDate=<null>,overDue=false,count=0,lastUpdated=<null>,lastUpdatedTimestamp=<null>],byInstanceType={}], deployment-environment=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@67f98f7f[overall=com.atlassian.jira.plugin.devstatus.summary.beans.DeploymentOverallBean@2a11d226[topEnvironments=[],showProjects=false,successfulCount=0,count=0,lastUpdated=<null>,lastUpdatedTimestamp=<null>],byInstanceType={}], repository=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@1b217ce9[overall=com.atlassian.jira.plugin.devstatus.summary.beans.CommitOverallBean@3fb385fb[count=0,lastUpdated=<null>,lastUpdatedTimestamp=<null>],byInstanceType={}], branch=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@141a7705[overall=com.atlassian.jira.plugin.devstatus.summary.beans.BranchOverallBean@30be1742[count=0,lastUpdated=<null>,lastUpdatedTimestamp=<null>],byInstanceType={}]},errors=[],configErrors=[]], devSummaryJson={\"cachedValue\":{\"errors\":[],\"configErrors\":[],\"summary\":{\"pullrequest\":{\"overall\":{\"count\":0,\"lastUpdated\":null,\"stateCount\":0,\"state\":\"OPEN\",\"details\":{\"openCount\":0,\"mergedCount\":0,\"declinedCount\":0,\"total\":0},\"open\":true},\"byInstanceType\":{}},\"build\":{\"overall\":{\"count\":0,\"lastUpdated\":null,\"failedBuildCount\":0,\"successfulBuildCount\":0,\"unknownBuildCount\":0},\"byInstanceType\":{}},\"review\":{\"overall\":{\"count\":0,\"lastUpdated\":null,\"stateCount\":0,\"state\":null,\"dueDate\":null,\"overDue\":false,\"completed\":false},\"byInstanceType\":{}},\"deployment-environment\":{\"overall\":{\"count\":0,\"lastUpdated\":null,\"topEnvironments\":[],\"showProjects\":false,\"successfulCount\":0},\"byInstanceType\":{}},\"repository\":{\"overall\":{\"count\":0,\"lastUpdated\":null},\"byInstanceType\":{}},\"branch\":{\"overall\":{\"count\":0,\"lastUpdated\":null},\"byInstanceType\":{}}}},\"isStale\":true}}",
    "customfield_13002": null,
    "customfield_11023": null,
    "customfield_13005": null,
    "customfield_11300": null,
    "customfield_13004": null,
    "customfield_13007": null,
    "customfield_11301": null,
    "customfield_13006": null,
    "customfield_10800": null,
    "customfield_10801": null,
    "aggregatetimeestimate": null,
    "customfield_10802": null,
    "summary": "Twistlock: broker-admin-app:2.0.1, Fix By: 2024-12-24",
    "creator": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/user?username=rpjzn388",
      "name": "rpjzn388",
      "key": "JIRAUSER25389",
      "emailAddress": "<EMAIL>",
      "avatarUrls": {
        "48x48": "https://jira.dc2.intactlab.ca/secure/useravatar?ownerId=JIRAUSER25389&avatarId=21503",
        "24x24": "https://jira.dc2.intactlab.ca/secure/useravatar?size=small&ownerId=JIRAUSER25389&avatarId=21503",
        "16x16": "https://jira.dc2.intactlab.ca/secure/useravatar?size=xsmall&ownerId=JIRAUSER25389&avatarId=21503",
        "32x32": "https://jira.dc2.intactlab.ca/secure/useravatar?size=medium&ownerId=JIRAUSER25389&avatarId=21503"
      },
      "displayName": "Leonardo Dias de Oliveira",
      "active": true,
      "timeZone": "America/New_York"
    },
    "subtasks": [],
    "reporter": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/user?username=rpjzn388",
      "name": "rpjzn388",
      "key": "JIRAUSER25389",
      "emailAddress": "<EMAIL>",
      "avatarUrls": {
        "48x48": "https://jira.dc2.intactlab.ca/secure/useravatar?ownerId=JIRAUSER25389&avatarId=21503",
        "24x24": "https://jira.dc2.intactlab.ca/secure/useravatar?size=small&ownerId=JIRAUSER25389&avatarId=21503",
        "16x16": "https://jira.dc2.intactlab.ca/secure/useravatar?size=xsmall&ownerId=JIRAUSER25389&avatarId=21503",
        "32x32": "https://jira.dc2.intactlab.ca/secure/useravatar?size=medium&ownerId=JIRAUSER25389&avatarId=21503"
      },
      "displayName": "Leonardo Dias de Oliveira",
      "active": true,
      "timeZone": "America/New_York"
    },
    "customfield_12100": [
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/10900",
        "value": "Belair",
        "id": "10900",
        "disabled": false
      },
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/10901",
        "value": "BNA",
        "id": "10901",
        "disabled": false
      },
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/10902",
        "value": "Intact",
        "id": "10902",
        "disabled": false
      },
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/12514",
        "value": "Servus",
        "id": "12514",
        "disabled": false
      },
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/12515",
        "value": "Scotia",
        "id": "12515",
        "disabled": false
      }
    ],
    "aggregateprogress": {
      "progress": 0,
      "total": 0
    },
    "customfield_10200": null,
    "customfield_10400": null,
    "customfield_12500": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/11102",
      "value": "Arch_Required",
      "id": "11102",
      "disabled": false
    },
    "customfield_13900": null,
    "customfield_11204": null,
    "customfield_11006": null,
    "customfield_11205": null,
    "environment": null,
    "customfield_11800": null,
    "customfield_13903": null,
    "duedate": "2024-12-24",
    "progress": {
      "progress": 0,
      "total": 0
    },
    "votes": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/issue/MARS-3149/votes",
      "votes": 0,
      "hasVoted": false
    }
  }
}
{
  "expand": "operations,versionedRepresentations,editmeta,changelog,renderedFields",
  "id": "243977",
  "self": "https://jira.dc2.intactlab.ca/rest/api/2/issue/243977",
  "key": "MARS-3159",
  "fields": {
    "issuetype": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/issuetype/10002",
      "id": "10002",
      "description": "The sub-task of the issue",
      "iconUrl": "https://jira.dc2.intactlab.ca/secure/viewavatar?size=xsmall&avatarId=10316&avatarType=issuetype",
      "name": "Sub-task",
      "subtask": true,
      "avatarId": 10316
    },
    "parent": {
      "id": "239731",
      "key": "MARS-2966",
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/issue/239731",
      "fields": {
        "summary": "Goalkeeping PI.2024.4.3",
        "status": {
          "self": "https://jira.dc2.intactlab.ca/rest/api/2/status/3",
          "description": "This issue is being actively worked on at the moment by the assignee.",
          "iconUrl": "https://jira.dc2.intactlab.ca/images/icons/statuses/inprogress.png",
          "name": "In Progress",
          "id": "3",
          "statusCategory": {
            "self": "https://jira.dc2.intactlab.ca/rest/api/2/statuscategory/4",
            "id": 4,
            "key": "indeterminate",
            "colorName": "inprogress",
            "name": "In Progress"
          }
        },
        "priority": {
          "self": "https://jira.dc2.intactlab.ca/rest/api/2/priority/3",
          "iconUrl": "https://jira.dc2.intactlab.ca/images/icons/priorities/medium.svg",
          "name": "Medium",
          "id": "3"
        },
        "issuetype": {
          "self": "https://jira.dc2.intactlab.ca/rest/api/2/issuetype/10000",
          "id": "10000",
          "description": "Created by Jira Software - do not edit or delete. Issue type for a user story.",
          "iconUrl": "https://jira.dc2.intactlab.ca/images/icons/issuetypes/story.svg",
          "name": "Story",
          "subtask": false
        }
      }
    },
    "timespent": null,
    "customfield_13100": null,
    "project": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/project/14101",
      "id": "14101",
      "key": "MARS",
      "name": "The Martians",
      "projectTypeKey": "software",
      "avatarUrls": {
        "48x48": "https://jira.dc2.intactlab.ca/secure/projectavatar?pid=14101&avatarId=23504",
        "24x24": "https://jira.dc2.intactlab.ca/secure/projectavatar?size=small&pid=14101&avatarId=23504",
        "16x16": "https://jira.dc2.intactlab.ca/secure/projectavatar?size=xsmall&pid=14101&avatarId=23504",
        "32x32": "https://jira.dc2.intactlab.ca/secure/projectavatar?size=medium&pid=14101&avatarId=23504"
      }
    },
    "fixVersions": [],
    "customfield_11001": null,
    "customfield_11002": null,
    "customfield_11200": null,
    "aggregatetimespent": null,
    "resolution": null,
    "customfield_11201": null,
    "customfield_11003": null,
    "customfield_11202": null,
    "customfield_11004": null,
    "customfield_11203": [],
    "customfield_11005": null,
    "customfield_10700": null,
    "customfield_10900": null,
    "resolutiondate": null,
    "workratio": -1,
    "lastViewed": "2024-11-05T16:08:39.667-0500",
    "watches": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/issue/MARS-3159/watchers",
      "watchCount": 1,
      "isWatching": true
    },
    "created": "2024-11-05T16:08:02.000-0500",
    "customfield_10020": null,
    "customfield_12202": null,
    "priority": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/priority/3",
      "iconUrl": "https://jira.dc2.intactlab.ca/images/icons/priorities/medium.svg",
      "name": "Medium",
      "id": "3"
    },
    "customfield_10100": null,
    "customfield_12201": null,
    "customfield_10024": [
      "com.atlassian.greenhopper.service.sprint.Sprint@5685d2fb[id=3552,rapidViewId=385,state=ACTIVE,name=PI.2024.4.3,startDate=2024-10-31T05:00:00.000-04:00,endDate=2024-11-13T16:00:00.000-05:00,completeDate=<null>,activatedDate=2024-11-01T09:28:12.986-04:00,sequence=3304,goal=,autoStartStop=false,synced=false]"
    ],
    "customfield_10025": "0|i0w88c:",
    "labels": [
      "Twistlock"
    ],
    "customfield_10026": null,
    "customfield_11700": null,
    "customfield_13008": null,
    "customfield_11900": null,
    "customfield_11902": null,
    "timeestimate": null,
    "aggregatetimeoriginalestimate": null,
    "versions": [],
    "customfield_11901": null,
    "customfield_11906": null,
    "customfield_11905": null,
    "issuelinks": [],
    "assignee": null,
    "updated": "2024-11-05T16:08:02.000-0500",
    "status": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/status/10100",
      "description": "",
      "iconUrl": "https://jira.dc2.intactlab.ca/",
      "name": "To Do",
      "id": "10100",
      "statusCategory": {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/statuscategory/2",
        "id": 2,
        "key": "new",
        "colorName": "default",
        "name": "To Do"
      }
    },
    "components": [],
    "timeoriginalestimate": null,
    "customfield_13001": null,
    "description": "|Vulnerability: compliance-448|\r\n|Scope: image|\r\n|Recommendation: Image contains binaries installed by package manager and altered post installation|\r\n|Severity: Critical|\r\n|Last Scan: 2024-11-04|\r\n|Fix By: 2024-12-24|\r\n|Overdue: No|",
    "customfield_13003": null,
    "customfield_11022": null,
    "customfield_11100": "{summaryBean=com.atlassian.jira.plugin.devstatus.rest.SummaryBean@6d2ae2a5[summary={pullrequest=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@7a49160[overall=PullRequestOverallBean{stateCount=0, state='OPEN', details=PullRequestOverallDetails{openCount=0, mergedCount=0, declinedCount=0}},byInstanceType={}], build=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@70636cae[overall=com.atlassian.jira.plugin.devstatus.summary.beans.BuildOverallBean@536fcc53[failedBuildCount=0,successfulBuildCount=0,unknownBuildCount=0,count=0,lastUpdated=<null>,lastUpdatedTimestamp=<null>],byInstanceType={}], review=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@419f6ebc[overall=com.atlassian.jira.plugin.devstatus.summary.beans.ReviewsOverallBean@3f03fcea[stateCount=0,state=<null>,dueDate=<null>,overDue=false,count=0,lastUpdated=<null>,lastUpdatedTimestamp=<null>],byInstanceType={}], deployment-environment=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@54eee1b8[overall=com.atlassian.jira.plugin.devstatus.summary.beans.DeploymentOverallBean@794ea222[topEnvironments=[],showProjects=false,successfulCount=0,count=0,lastUpdated=<null>,lastUpdatedTimestamp=<null>],byInstanceType={}], repository=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@439ec939[overall=com.atlassian.jira.plugin.devstatus.summary.beans.CommitOverallBean@635a890c[count=0,lastUpdated=<null>,lastUpdatedTimestamp=<null>],byInstanceType={}], branch=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@401a47fe[overall=com.atlassian.jira.plugin.devstatus.summary.beans.BranchOverallBean@4c49b26d[count=0,lastUpdated=<null>,lastUpdatedTimestamp=<null>],byInstanceType={}]},errors=[],configErrors=[]], devSummaryJson={\"cachedValue\":{\"errors\":[],\"configErrors\":[],\"summary\":{\"pullrequest\":{\"overall\":{\"count\":0,\"lastUpdated\":null,\"stateCount\":0,\"state\":\"OPEN\",\"details\":{\"openCount\":0,\"mergedCount\":0,\"declinedCount\":0,\"total\":0},\"open\":true},\"byInstanceType\":{}},\"build\":{\"overall\":{\"count\":0,\"lastUpdated\":null,\"failedBuildCount\":0,\"successfulBuildCount\":0,\"unknownBuildCount\":0},\"byInstanceType\":{}},\"review\":{\"overall\":{\"count\":0,\"lastUpdated\":null,\"stateCount\":0,\"state\":null,\"dueDate\":null,\"overDue\":false,\"completed\":false},\"byInstanceType\":{}},\"deployment-environment\":{\"overall\":{\"count\":0,\"lastUpdated\":null,\"topEnvironments\":[],\"showProjects\":false,\"successfulCount\":0},\"byInstanceType\":{}},\"repository\":{\"overall\":{\"count\":0,\"lastUpdated\":null},\"byInstanceType\":{}},\"branch\":{\"overall\":{\"count\":0,\"lastUpdated\":null},\"byInstanceType\":{}}}},\"isStale\":true}}",
    "customfield_13002": null,
    "customfield_11023": null,
    "customfield_13005": null,
    "customfield_11300": null,
    "customfield_13004": null,
    "customfield_13007": null,
    "customfield_11301": null,
    "customfield_13006": null,
    "customfield_10800": null,
    "customfield_10801": null,
    "aggregatetimeestimate": null,
    "customfield_10802": null,
    "summary": "Twistlock: broker-admin-app:2.0.1, Fix by: 2024-12-24",
    "creator": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/user?username=rpjzn388",
      "name": "rpjzn388",
      "key": "JIRAUSER25389",
      "emailAddress": "<EMAIL>",
      "avatarUrls": {
        "48x48": "https://jira.dc2.intactlab.ca/secure/useravatar?ownerId=JIRAUSER25389&avatarId=21503",
        "24x24": "https://jira.dc2.intactlab.ca/secure/useravatar?size=small&ownerId=JIRAUSER25389&avatarId=21503",
        "16x16": "https://jira.dc2.intactlab.ca/secure/useravatar?size=xsmall&ownerId=JIRAUSER25389&avatarId=21503",
        "32x32": "https://jira.dc2.intactlab.ca/secure/useravatar?size=medium&ownerId=JIRAUSER25389&avatarId=21503"
      },
      "displayName": "Leonardo Dias de Oliveira",
      "active": true,
      "timeZone": "America/New_York"
    },
    "subtasks": [],
    "reporter": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/user?username=rpjzn388",
      "name": "rpjzn388",
      "key": "JIRAUSER25389",
      "emailAddress": "<EMAIL>",
      "avatarUrls": {
        "48x48": "https://jira.dc2.intactlab.ca/secure/useravatar?ownerId=JIRAUSER25389&avatarId=21503",
        "24x24": "https://jira.dc2.intactlab.ca/secure/useravatar?size=small&ownerId=JIRAUSER25389&avatarId=21503",
        "16x16": "https://jira.dc2.intactlab.ca/secure/useravatar?size=xsmall&ownerId=JIRAUSER25389&avatarId=21503",
        "32x32": "https://jira.dc2.intactlab.ca/secure/useravatar?size=medium&ownerId=JIRAUSER25389&avatarId=21503"
      },
      "displayName": "Leonardo Dias de Oliveira",
      "active": true,
      "timeZone": "America/New_York"
    },
    "customfield_12100": [
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/10900",
        "value": "Belair",
        "id": "10900",
        "disabled": false
      },
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/10901",
        "value": "BNA",
        "id": "10901",
        "disabled": false
      },
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/10902",
        "value": "Intact",
        "id": "10902",
        "disabled": false
      },
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/12514",
        "value": "Servus",
        "id": "12514",
        "disabled": false
      },
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/12515",
        "value": "Scotia",
        "id": "12515",
        "disabled": false
      }
    ],
    "aggregateprogress": {
      "progress": 0,
      "total": 0
    },
    "customfield_10200": null,
    "customfield_10400": null,
    "customfield_12500": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/11102",
      "value": "Arch_Required",
      "id": "11102",
      "disabled": false
    },
    "customfield_13900": null,
    "customfield_11204": null,
    "customfield_11006": null,
    "customfield_11205": null,
    "environment": null,
    "customfield_11800": null,
    "customfield_13903": null,
    "duedate": "2024-12-24",
    "progress": {
      "progress": 0,
      "total": 0
    },
    "votes": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/issue/MARS-3159/votes",
      "votes": 0,
      "hasVoted": false
    }
  }
}
{
  "expand": "operations,versionedRepresentations,editmeta,changelog,renderedFields",
  "id": "243484",
  "self": "https://jira.dc2.intactlab.ca/rest/api/2/issue/243484",
  "key": "MARS-3147",
  "fields": {
    "issuetype": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/issuetype/10002",
      "id": "10002",
      "description": "The sub-task of the issue",
      "iconUrl": "https://jira.dc2.intactlab.ca/secure/viewavatar?size=xsmall&avatarId=10316&avatarType=issuetype",
      "name": "Sub-task",
      "subtask": true,
      "avatarId": 10316
    },
    "parent": {
      "id": "239731",
      "key": "MARS-2966",
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/issue/239731",
      "fields": {
        "summary": "Goalkeeping PI.2024.4.3",
        "status": {
          "self": "https://jira.dc2.intactlab.ca/rest/api/2/status/3",
          "description": "This issue is being actively worked on at the moment by the assignee.",
          "iconUrl": "https://jira.dc2.intactlab.ca/images/icons/statuses/inprogress.png",
          "name": "In Progress",
          "id": "3",
          "statusCategory": {
            "self": "https://jira.dc2.intactlab.ca/rest/api/2/statuscategory/4",
            "id": 4,
            "key": "indeterminate",
            "colorName": "inprogress",
            "name": "In Progress"
          }
        },
        "priority": {
          "self": "https://jira.dc2.intactlab.ca/rest/api/2/priority/3",
          "iconUrl": "https://jira.dc2.intactlab.ca/images/icons/priorities/medium.svg",
          "name": "Medium",
          "id": "3"
        },
        "issuetype": {
          "self": "https://jira.dc2.intactlab.ca/rest/api/2/issuetype/10000",
          "id": "10000",
          "description": "Created by Jira Software - do not edit or delete. Issue type for a user story.",
          "iconUrl": "https://jira.dc2.intactlab.ca/images/icons/issuetypes/story.svg",
          "name": "Story",
          "subtask": false
        }
      }
    },
    "timespent": null,
    "customfield_13100": null,
    "project": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/project/14101",
      "id": "14101",
      "key": "MARS",
      "name": "The Martians",
      "projectTypeKey": "software",
      "avatarUrls": {
        "48x48": "https://jira.dc2.intactlab.ca/secure/projectavatar?pid=14101&avatarId=23504",
        "24x24": "https://jira.dc2.intactlab.ca/secure/projectavatar?size=small&pid=14101&avatarId=23504",
        "16x16": "https://jira.dc2.intactlab.ca/secure/projectavatar?size=xsmall&pid=14101&avatarId=23504",
        "32x32": "https://jira.dc2.intactlab.ca/secure/projectavatar?size=medium&pid=14101&avatarId=23504"
      }
    },
    "fixVersions": [],
    "customfield_11001": null,
    "customfield_11002": null,
    "customfield_11200": null,
    "aggregatetimespent": null,
    "resolution": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/resolution/10000",
      "id": "10000",
      "description": "Work has been completed on this issue.",
      "name": "Done"
    },
    "customfield_11201": null,
    "customfield_11003": null,
    "customfield_11202": null,
    "customfield_11004": null,
    "customfield_11203": [
      "main"
    ],
    "customfield_11005": null,
    "customfield_10700": null,
    "customfield_10900": null,
    "resolutiondate": "2024-11-05T10:59:00.000-0500",
    "workratio": -1,
    "lastViewed": "2024-11-05T10:59:00.120-0500",
    "watches": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/issue/MARS-3147/watchers",
      "watchCount": 1,
      "isWatching": true
    },
    "created": "2024-11-01T14:57:41.000-0400",
    "customfield_10020": null,
    "customfield_12202": null,
    "priority": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/priority/3",
      "iconUrl": "https://jira.dc2.intactlab.ca/images/icons/priorities/medium.svg",
      "name": "Medium",
      "id": "3"
    },
    "customfield_10100": null,
    "customfield_12201": null,
    "customfield_10024": [
      "com.atlassian.greenhopper.service.sprint.Sprint@5685d2fb[id=3552,rapidViewId=385,state=ACTIVE,name=PI.2024.4.3,startDate=2024-10-31T05:00:00.000-04:00,endDate=2024-11-13T16:00:00.000-05:00,completeDate=<null>,activatedDate=2024-11-01T09:28:12.986-04:00,sequence=3304,goal=,autoStartStop=false,synced=false]"
    ],
    "customfield_10025": "0|i0w5ho:",
    "labels": [
      "Twistlock"
    ],
    "customfield_10026": null,
    "customfield_11700": null,
    "customfield_13008": null,
    "customfield_11900": null,
    "customfield_11902": null,
    "timeestimate": null,
    "aggregatetimeoriginalestimate": null,
    "versions": [],
    "customfield_11901": null,
    "customfield_11906": null,
    "customfield_11905": null,
    "issuelinks": [],
    "assignee": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/user?username=rpjzn388",
      "name": "rpjzn388",
      "key": "JIRAUSER25389",
      "emailAddress": "<EMAIL>",
      "avatarUrls": {
        "48x48": "https://jira.dc2.intactlab.ca/secure/useravatar?ownerId=JIRAUSER25389&avatarId=21503",
        "24x24": "https://jira.dc2.intactlab.ca/secure/useravatar?size=small&ownerId=JIRAUSER25389&avatarId=21503",
        "16x16": "https://jira.dc2.intactlab.ca/secure/useravatar?size=xsmall&ownerId=JIRAUSER25389&avatarId=21503",
        "32x32": "https://jira.dc2.intactlab.ca/secure/useravatar?size=medium&ownerId=JIRAUSER25389&avatarId=21503"
      },
      "displayName": "Leonardo Dias de Oliveira",
      "active": true,
      "timeZone": "America/New_York"
    },
    "updated": "2024-11-05T10:59:00.000-0500",
    "status": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/status/10002",
      "description": "",
      "iconUrl": "https://jira.dc2.intactlab.ca/",
      "name": "Done",
      "id": "10002",
      "statusCategory": {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/statuscategory/3",
        "id": 3,
        "key": "done",
        "colorName": "success",
        "name": "Done"
      }
    },
    "components": [],
    "timeoriginalestimate": null,
    "customfield_13001": null,
    "description": "|Vulnerability: CVE-2024-38821|\r\n|Scope: spring-security-web, version: 6.1.9|\r\n|Recommendation: fixed in 6.3.4, 6.2.7, 6.1.11,...|\r\n|Severity: Critical|\r\n|Last Scan: 2024-11-01|\r\n|Fix By: 2024-12-30|\r\n|Overdue: No|",
    "customfield_13003": null,
    "customfield_11022": null,
    "customfield_11100": "{summaryBean=com.atlassian.jira.plugin.devstatus.rest.SummaryBean@105e3589[summary={pullrequest=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@738f8e6c[overall=PullRequestOverallBean{stateCount=0, state='OPEN', details=PullRequestOverallDetails{openCount=0, mergedCount=0, declinedCount=0}},byInstanceType={}], build=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@1deb63e2[overall=com.atlassian.jira.plugin.devstatus.summary.beans.BuildOverallBean@129862dd[failedBuildCount=0,successfulBuildCount=0,unknownBuildCount=0,count=0,lastUpdated=<null>,lastUpdatedTimestamp=<null>],byInstanceType={}], review=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@139ed162[overall=com.atlassian.jira.plugin.devstatus.summary.beans.ReviewsOverallBean@28c19c33[stateCount=0,state=<null>,dueDate=<null>,overDue=false,count=0,lastUpdated=<null>,lastUpdatedTimestamp=<null>],byInstanceType={}], deployment-environment=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@6ef30eba[overall=com.atlassian.jira.plugin.devstatus.summary.beans.DeploymentOverallBean@3fac0733[topEnvironments=[],showProjects=false,successfulCount=0,count=0,lastUpdated=<null>,lastUpdatedTimestamp=<null>],byInstanceType={}], repository=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@5a5cc465[overall=com.atlassian.jira.plugin.devstatus.summary.beans.CommitOverallBean@7ed8eb19[count=0,lastUpdated=<null>,lastUpdatedTimestamp=<null>],byInstanceType={}], branch=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@8a8aca6[overall=com.atlassian.jira.plugin.devstatus.summary.beans.BranchOverallBean@57e2a71f[count=0,lastUpdated=<null>,lastUpdatedTimestamp=<null>],byInstanceType={}]},errors=[],configErrors=[]], devSummaryJson={\"cachedValue\":{\"errors\":[],\"configErrors\":[],\"summary\":{\"pullrequest\":{\"overall\":{\"count\":0,\"lastUpdated\":null,\"stateCount\":0,\"state\":\"OPEN\",\"details\":{\"openCount\":0,\"mergedCount\":0,\"declinedCount\":0,\"total\":0},\"open\":true},\"byInstanceType\":{}},\"build\":{\"overall\":{\"count\":0,\"lastUpdated\":null,\"failedBuildCount\":0,\"successfulBuildCount\":0,\"unknownBuildCount\":0},\"byInstanceType\":{}},\"review\":{\"overall\":{\"count\":0,\"lastUpdated\":null,\"stateCount\":0,\"state\":null,\"dueDate\":null,\"overDue\":false,\"completed\":false},\"byInstanceType\":{}},\"deployment-environment\":{\"overall\":{\"count\":0,\"lastUpdated\":null,\"topEnvironments\":[],\"showProjects\":false,\"successfulCount\":0},\"byInstanceType\":{}},\"repository\":{\"overall\":{\"count\":0,\"lastUpdated\":null},\"byInstanceType\":{}},\"branch\":{\"overall\":{\"count\":0,\"lastUpdated\":null},\"byInstanceType\":{}}}},\"isStale\":true}}",
    "customfield_13002": null,
    "customfield_11023": null,
    "customfield_13005": null,
    "customfield_11300": null,
    "customfield_13004": null,
    "customfield_13007": null,
    "customfield_11301": null,
    "customfield_13006": null,
    "customfield_10800": null,
    "customfield_10801": null,
    "aggregatetimeestimate": null,
    "customfield_10802": null,
    "summary": "Twistlock: bloom-repository-service:3.0.35, Fix By: 2024-12-30",
    "creator": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/user?username=rpjzn388",
      "name": "rpjzn388",
      "key": "JIRAUSER25389",
      "emailAddress": "<EMAIL>",
      "avatarUrls": {
        "48x48": "https://jira.dc2.intactlab.ca/secure/useravatar?ownerId=JIRAUSER25389&avatarId=21503",
        "24x24": "https://jira.dc2.intactlab.ca/secure/useravatar?size=small&ownerId=JIRAUSER25389&avatarId=21503",
        "16x16": "https://jira.dc2.intactlab.ca/secure/useravatar?size=xsmall&ownerId=JIRAUSER25389&avatarId=21503",
        "32x32": "https://jira.dc2.intactlab.ca/secure/useravatar?size=medium&ownerId=JIRAUSER25389&avatarId=21503"
      },
      "displayName": "Leonardo Dias de Oliveira",
      "active": true,
      "timeZone": "America/New_York"
    },
    "subtasks": [],
    "reporter": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/user?username=rpjzn388",
      "name": "rpjzn388",
      "key": "JIRAUSER25389",
      "emailAddress": "<EMAIL>",
      "avatarUrls": {
        "48x48": "https://jira.dc2.intactlab.ca/secure/useravatar?ownerId=JIRAUSER25389&avatarId=21503",
        "24x24": "https://jira.dc2.intactlab.ca/secure/useravatar?size=small&ownerId=JIRAUSER25389&avatarId=21503",
        "16x16": "https://jira.dc2.intactlab.ca/secure/useravatar?size=xsmall&ownerId=JIRAUSER25389&avatarId=21503",
        "32x32": "https://jira.dc2.intactlab.ca/secure/useravatar?size=medium&ownerId=JIRAUSER25389&avatarId=21503"
      },
      "displayName": "Leonardo Dias de Oliveira",
      "active": true,
      "timeZone": "America/New_York"
    },
    "customfield_12100": [
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/10900",
        "value": "Belair",
        "id": "10900",
        "disabled": false
      },
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/10901",
        "value": "BNA",
        "id": "10901",
        "disabled": false
      },
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/10902",
        "value": "Intact",
        "id": "10902",
        "disabled": false
      },
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/12514",
        "value": "Servus",
        "id": "12514",
        "disabled": false
      },
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/12515",
        "value": "Scotia",
        "id": "12515",
        "disabled": false
      }
    ],
    "aggregateprogress": {
      "progress": 0,
      "total": 0
    },
    "customfield_10200": null,
    "customfield_10400": null,
    "customfield_12500": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/11102",
      "value": "Arch_Required",
      "id": "11102",
      "disabled": false
    },
    "customfield_13900": null,
    "customfield_11204": "true",
    "customfield_11006": null,
    "customfield_11205": null,
    "environment": null,
    "customfield_11800": null,
    "customfield_13903": null,
    "duedate": "2024-12-30",
    "progress": {
      "progress": 0,
      "total": 0
    },
    "votes": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/issue/MARS-3147/votes",
      "votes": 0,
      "hasVoted": false
    }
  }
}
{
  "expand": "operations,versionedRepresentations,editmeta,changelog,renderedFields",
  "id": "243485",
  "self": "https://jira.dc2.intactlab.ca/rest/api/2/issue/243485",
  "key": "MARS-3148",
  "fields": {
    "issuetype": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/issuetype/10002",
      "id": "10002",
      "description": "The sub-task of the issue",
      "iconUrl": "https://jira.dc2.intactlab.ca/secure/viewavatar?size=xsmall&avatarId=10316&avatarType=issuetype",
      "name": "Sub-task",
      "subtask": true,
      "avatarId": 10316
    },
    "parent": {
      "id": "239731",
      "key": "MARS-2966",
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/issue/239731",
      "fields": {
        "summary": "Goalkeeping PI.2024.4.3",
        "status": {
          "self": "https://jira.dc2.intactlab.ca/rest/api/2/status/3",
          "description": "This issue is being actively worked on at the moment by the assignee.",
          "iconUrl": "https://jira.dc2.intactlab.ca/images/icons/statuses/inprogress.png",
          "name": "In Progress",
          "id": "3",
          "statusCategory": {
            "self": "https://jira.dc2.intactlab.ca/rest/api/2/statuscategory/4",
            "id": 4,
            "key": "indeterminate",
            "colorName": "inprogress",
            "name": "In Progress"
          }
        },
        "priority": {
          "self": "https://jira.dc2.intactlab.ca/rest/api/2/priority/3",
          "iconUrl": "https://jira.dc2.intactlab.ca/images/icons/priorities/medium.svg",
          "name": "Medium",
          "id": "3"
        },
        "issuetype": {
          "self": "https://jira.dc2.intactlab.ca/rest/api/2/issuetype/10000",
          "id": "10000",
          "description": "Created by Jira Software - do not edit or delete. Issue type for a user story.",
          "iconUrl": "https://jira.dc2.intactlab.ca/images/icons/issuetypes/story.svg",
          "name": "Story",
          "subtask": false
        }
      }
    },
    "timespent": null,
    "customfield_13100": null,
    "project": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/project/14101",
      "id": "14101",
      "key": "MARS",
      "name": "The Martians",
      "projectTypeKey": "software",
      "avatarUrls": {
        "48x48": "https://jira.dc2.intactlab.ca/secure/projectavatar?pid=14101&avatarId=23504",
        "24x24": "https://jira.dc2.intactlab.ca/secure/projectavatar?size=small&pid=14101&avatarId=23504",
        "16x16": "https://jira.dc2.intactlab.ca/secure/projectavatar?size=xsmall&pid=14101&avatarId=23504",
        "32x32": "https://jira.dc2.intactlab.ca/secure/projectavatar?size=medium&pid=14101&avatarId=23504"
      }
    },
    "fixVersions": [],
    "customfield_11001": null,
    "customfield_11002": null,
    "customfield_11200": null,
    "aggregatetimespent": null,
    "resolution": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/resolution/10000",
      "id": "10000",
      "description": "Work has been completed on this issue.",
      "name": "Done"
    },
    "customfield_11201": null,
    "customfield_11003": null,
    "customfield_11202": null,
    "customfield_11004": null,
    "customfield_11203": [
      "main"
    ],
    "customfield_11005": null,
    "customfield_10700": null,
    "customfield_10900": null,
    "resolutiondate": "2024-11-05T11:37:23.000-0500",
    "workratio": -1,
    "lastViewed": "2024-11-05T11:37:23.302-0500",
    "watches": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/issue/MARS-3148/watchers",
      "watchCount": 1,
      "isWatching": true
    },
    "created": "2024-11-01T15:02:05.000-0400",
    "customfield_10020": null,
    "customfield_12202": null,
    "priority": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/priority/3",
      "iconUrl": "https://jira.dc2.intactlab.ca/images/icons/priorities/medium.svg",
      "name": "Medium",
      "id": "3"
    },
    "customfield_10100": null,
    "customfield_12201": null,
    "customfield_10024": [
      "com.atlassian.greenhopper.service.sprint.Sprint@5685d2fb[id=3552,rapidViewId=385,state=ACTIVE,name=PI.2024.4.3,startDate=2024-10-31T05:00:00.000-04:00,endDate=2024-11-13T16:00:00.000-05:00,completeDate=<null>,activatedDate=2024-11-01T09:28:12.986-04:00,sequence=3304,goal=,autoStartStop=false,synced=false]"
    ],
    "customfield_10025": "0|i0w5hw:",
    "labels": [
      "Twistlock"
    ],
    "customfield_10026": null,
    "customfield_11700": null,
    "customfield_13008": null,
    "customfield_11900": null,
    "customfield_11902": null,
    "timeestimate": null,
    "aggregatetimeoriginalestimate": null,
    "versions": [],
    "customfield_11901": null,
    "customfield_11906": null,
    "customfield_11905": null,
    "issuelinks": [],
    "assignee": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/user?username=rpjzn388",
      "name": "rpjzn388",
      "key": "JIRAUSER25389",
      "emailAddress": "<EMAIL>",
      "avatarUrls": {
        "48x48": "https://jira.dc2.intactlab.ca/secure/useravatar?ownerId=JIRAUSER25389&avatarId=21503",
        "24x24": "https://jira.dc2.intactlab.ca/secure/useravatar?size=small&ownerId=JIRAUSER25389&avatarId=21503",
        "16x16": "https://jira.dc2.intactlab.ca/secure/useravatar?size=xsmall&ownerId=JIRAUSER25389&avatarId=21503",
        "32x32": "https://jira.dc2.intactlab.ca/secure/useravatar?size=medium&ownerId=JIRAUSER25389&avatarId=21503"
      },
      "displayName": "Leonardo Dias de Oliveira",
      "active": true,
      "timeZone": "America/New_York"
    },
    "updated": "2024-11-05T11:37:23.000-0500",
    "status": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/status/10002",
      "description": "",
      "iconUrl": "https://jira.dc2.intactlab.ca/",
      "name": "Done",
      "id": "10002",
      "statusCategory": {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/statuscategory/3",
        "id": 3,
        "key": "done",
        "colorName": "success",
        "name": "Done"
      }
    },
    "components": [],
    "timeoriginalestimate": null,
    "customfield_13001": null,
    "description": "|Vulnerability: CVE-2024-38821|\r\n|Scope: spring-security-web, version: 6.3.1|\r\n|Recommendation: fixed in 6.3.4, 6.2.7, 6.1.11,...|\r\n|Severity: Critical|\r\n|Last Scan: 2024-11-01|\r\n|Fix By: 2024-12-30|\r\n|Overdue: No|",
    "customfield_13003": null,
    "customfield_11022": null,
    "customfield_11100": "{summaryBean=com.atlassian.jira.plugin.devstatus.rest.SummaryBean@4fbb5ea5[summary={pullrequest=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@62f331cc[overall=PullRequestOverallBean{stateCount=0, state='OPEN', details=PullRequestOverallDetails{openCount=0, mergedCount=0, declinedCount=0}},byInstanceType={}], build=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@448eb22d[overall=com.atlassian.jira.plugin.devstatus.summary.beans.BuildOverallBean@3f889621[failedBuildCount=0,successfulBuildCount=0,unknownBuildCount=0,count=0,lastUpdated=<null>,lastUpdatedTimestamp=<null>],byInstanceType={}], review=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@12e3fbc7[overall=com.atlassian.jira.plugin.devstatus.summary.beans.ReviewsOverallBean@6fcd9aff[stateCount=0,state=<null>,dueDate=<null>,overDue=false,count=0,lastUpdated=<null>,lastUpdatedTimestamp=<null>],byInstanceType={}], deployment-environment=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@5ee352f1[overall=com.atlassian.jira.plugin.devstatus.summary.beans.DeploymentOverallBean@25a1fe92[topEnvironments=[],showProjects=false,successfulCount=0,count=0,lastUpdated=<null>,lastUpdatedTimestamp=<null>],byInstanceType={}], repository=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@28110422[overall=com.atlassian.jira.plugin.devstatus.summary.beans.CommitOverallBean@2b0869bd[count=0,lastUpdated=<null>,lastUpdatedTimestamp=<null>],byInstanceType={}], branch=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@6f38d2f7[overall=com.atlassian.jira.plugin.devstatus.summary.beans.BranchOverallBean@4547f4ec[count=0,lastUpdated=<null>,lastUpdatedTimestamp=<null>],byInstanceType={}]},errors=[],configErrors=[]], devSummaryJson={\"cachedValue\":{\"errors\":[],\"configErrors\":[],\"summary\":{\"pullrequest\":{\"overall\":{\"count\":0,\"lastUpdated\":null,\"stateCount\":0,\"state\":\"OPEN\",\"details\":{\"openCount\":0,\"mergedCount\":0,\"declinedCount\":0,\"total\":0},\"open\":true},\"byInstanceType\":{}},\"build\":{\"overall\":{\"count\":0,\"lastUpdated\":null,\"failedBuildCount\":0,\"successfulBuildCount\":0,\"unknownBuildCount\":0},\"byInstanceType\":{}},\"review\":{\"overall\":{\"count\":0,\"lastUpdated\":null,\"stateCount\":0,\"state\":null,\"dueDate\":null,\"overDue\":false,\"completed\":false},\"byInstanceType\":{}},\"deployment-environment\":{\"overall\":{\"count\":0,\"lastUpdated\":null,\"topEnvironments\":[],\"showProjects\":false,\"successfulCount\":0},\"byInstanceType\":{}},\"repository\":{\"overall\":{\"count\":0,\"lastUpdated\":null},\"byInstanceType\":{}},\"branch\":{\"overall\":{\"count\":0,\"lastUpdated\":null},\"byInstanceType\":{}}}},\"isStale\":true}}",
    "customfield_13002": null,
    "customfield_11023": null,
    "customfield_13005": null,
    "customfield_11300": null,
    "customfield_13004": null,
    "customfield_13007": null,
    "customfield_11301": null,
    "customfield_13006": null,
    "customfield_10800": null,
    "customfield_10801": null,
    "aggregatetimeestimate": null,
    "customfield_10802": null,
    "summary": "Twistlock: broker-dialer-service:3.1.55, Fix By: 2024-12-30",
    "creator": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/user?username=rpjzn388",
      "name": "rpjzn388",
      "key": "JIRAUSER25389",
      "emailAddress": "<EMAIL>",
      "avatarUrls": {
        "48x48": "https://jira.dc2.intactlab.ca/secure/useravatar?ownerId=JIRAUSER25389&avatarId=21503",
        "24x24": "https://jira.dc2.intactlab.ca/secure/useravatar?size=small&ownerId=JIRAUSER25389&avatarId=21503",
        "16x16": "https://jira.dc2.intactlab.ca/secure/useravatar?size=xsmall&ownerId=JIRAUSER25389&avatarId=21503",
        "32x32": "https://jira.dc2.intactlab.ca/secure/useravatar?size=medium&ownerId=JIRAUSER25389&avatarId=21503"
      },
      "displayName": "Leonardo Dias de Oliveira",
      "active": true,
      "timeZone": "America/New_York"
    },
    "subtasks": [],
    "reporter": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/user?username=rpjzn388",
      "name": "rpjzn388",
      "key": "JIRAUSER25389",
      "emailAddress": "<EMAIL>",
      "avatarUrls": {
        "48x48": "https://jira.dc2.intactlab.ca/secure/useravatar?ownerId=JIRAUSER25389&avatarId=21503",
        "24x24": "https://jira.dc2.intactlab.ca/secure/useravatar?size=small&ownerId=JIRAUSER25389&avatarId=21503",
        "16x16": "https://jira.dc2.intactlab.ca/secure/useravatar?size=xsmall&ownerId=JIRAUSER25389&avatarId=21503",
        "32x32": "https://jira.dc2.intactlab.ca/secure/useravatar?size=medium&ownerId=JIRAUSER25389&avatarId=21503"
      },
      "displayName": "Leonardo Dias de Oliveira",
      "active": true,
      "timeZone": "America/New_York"
    },
    "customfield_12100": [
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/10900",
        "value": "Belair",
        "id": "10900",
        "disabled": false
      },
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/10901",
        "value": "BNA",
        "id": "10901",
        "disabled": false
      },
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/10902",
        "value": "Intact",
        "id": "10902",
        "disabled": false
      },
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/12514",
        "value": "Servus",
        "id": "12514",
        "disabled": false
      },
      {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/12515",
        "value": "Scotia",
        "id": "12515",
        "disabled": false
      }
    ],
    "aggregateprogress": {
      "progress": 0,
      "total": 0
    },
    "customfield_10200": null,
    "customfield_10400": null,
    "customfield_12500": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/customFieldOption/11102",
      "value": "Arch_Required",
      "id": "11102",
      "disabled": false
    },
    "customfield_13900": null,
    "customfield_11204": "true",
    "customfield_11006": null,
    "customfield_11205": null,
    "environment": null,
    "customfield_11800": null,
    "customfield_13903": null,
    "duedate": "2024-12-30",
    "progress": {
      "progress": 0,
      "total": 0
    },
    "votes": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/issue/MARS-3148/votes",
      "votes": 0,
      "hasVoted": false
    }
  }
}
{
  "expand": "operations,versionedRepresentations,editmeta,changelog,renderedFields",
  "id": "239729",
  "self": "https://jira.dc2.intactlab.ca/rest/api/2/issue/239729",
  "key": "MARS-2964",
  "fields": {
    "issuetype": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/issuetype/10000",
      "id": "10000",
      "description": "Created by Jira Software - do not edit or delete. Issue type for a user story.",
      "iconUrl": "https://jira.dc2.intactlab.ca/images/icons/issuetypes/story.svg",
      "name": "Story",
      "subtask": false
    },
    "timespent": null,
    "customfield_13100": null,
    "project": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/project/14101",
      "id": "14101",
      "key": "MARS",
      "name": "The Martians",
      "projectTypeKey": "software",
      "avatarUrls": {
        "48x48": "https://jira.dc2.intactlab.ca/secure/projectavatar?pid=14101&avatarId=23504",
        "24x24": "https://jira.dc2.intactlab.ca/secure/projectavatar?size=small&pid=14101&avatarId=23504",
        "16x16": "https://jira.dc2.intactlab.ca/secure/projectavatar?size=xsmall&pid=14101&avatarId=23504",
        "32x32": "https://jira.dc2.intactlab.ca/secure/projectavatar?size=medium&pid=14101&avatarId=23504"
      }
    },
    "customfield_13300": null,
    "fixVersions": [],
    "customfield_11001": null,
    "customfield_11002": null,
    "customfield_11200": null,
    "aggregatetimespent": null,
    "resolution": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/resolution/10000",
      "id": "10000",
      "description": "Work has been completed on this issue.",
      "name": "Done"
    },
    "customfield_11201": null,
    "customfield_11003": null,
    "customfield_11202": null,
    "customfield_11004": null,
    "customfield_11203": [],
    "customfield_11005": null,
    "customfield_10700": null,
    "customfield_10900": null,
    "resolutiondate": "2024-10-17T10:40:23.000-0400",
    "workratio": -1,
    "lastViewed": "2024-11-05T16:00:32.016-0500",
    "watches": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/issue/MARS-2964/watchers",
      "watchCount": 0,
      "isWatching": false
    },
    "created": "2024-10-02T14:43:40.000-0400",
    "customfield_10020": null,
    "customfield_12202": null,
    "priority": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/priority/3",
      "iconUrl": "https://jira.dc2.intactlab.ca/images/icons/priorities/medium.svg",
      "name": "Medium",
      "id": "3"
    },
    "customfield_10100": null,
    "customfield_12201": null,
    "customfield_10024": [
      "com.atlassian.greenhopper.service.sprint.Sprint@5ab4e0ce[id=3550,rapidViewId=385,state=CLOSED,name=PI.2024.4.1,startDate=2024-10-03T13:00:00.000-04:00,endDate=2024-10-17T08:00:00.000-04:00,completeDate=2024-10-17T10:54:13.488-04:00,activatedDate=2024-10-03T12:02:38.404-04:00,sequence=3123,goal=1 - Release Lead Sharing in Prod by October 17th (Mid Month release)\n2- Make sure we are ready for the Broker admin release planned october 21st.\n3- Start work on Referal prgram,autoStartStop=false,synced=false]"
    ],
    "customfield_10025": "0|hzzw80:2",
    "labels": [
      "Goalkeeper",
      "Twistlock"
    ],
    "customfield_10026": null,
    "customfield_11700": null,
    "customfield_13008": null,
    "customfield_11900": null,
    "customfield_11902": null,
    "timeestimate": null,
    "aggregatetimeoriginalestimate": null,
    "versions": [],
    "customfield_11901": null,
    "customfield_11906": null,
    "customfield_11905": null,
    "issuelinks": [
      {
        "id": "261474",
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/issueLink/261474",
        "type": {
          "id": "10300",
          "name": "Issue split",
          "inward": "split from",
          "outward": "split to",
          "self": "https://jira.dc2.intactlab.ca/rest/api/2/issueLinkType/10300"
        },
        "inwardIssue": {
          "id": "238196",
          "key": "MARS-2886",
          "self": "https://jira.dc2.intactlab.ca/rest/api/2/issue/238196",
          "fields": {
            "summary": "Goalkeeping PI.2024.3.6",
            "status": {
              "self": "https://jira.dc2.intactlab.ca/rest/api/2/status/3",
              "description": "This issue is being actively worked on at the moment by the assignee.",
              "iconUrl": "https://jira.dc2.intactlab.ca/images/icons/statuses/inprogress.png",
              "name": "In Progress",
              "id": "3",
              "statusCategory": {
                "self": "https://jira.dc2.intactlab.ca/rest/api/2/statuscategory/4",
                "id": 4,
                "key": "indeterminate",
                "colorName": "inprogress",
                "name": "In Progress"
              }
            },
            "priority": {
              "self": "https://jira.dc2.intactlab.ca/rest/api/2/priority/3",
              "iconUrl": "https://jira.dc2.intactlab.ca/images/icons/priorities/medium.svg",
              "name": "Medium",
              "id": "3"
            },
            "issuetype": {
              "self": "https://jira.dc2.intactlab.ca/rest/api/2/issuetype/10000",
              "id": "10000",
              "description": "Created by Jira Software - do not edit or delete. Issue type for a user story.",
              "iconUrl": "https://jira.dc2.intactlab.ca/images/icons/issuetypes/story.svg",
              "name": "Story",
              "subtask": false
            }
          }
        }
      }
    ],
    "assignee": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/user?username=rpmdh249",
      "name": "rpmdh249",
      "key": "JIRAUSER24818",
      "emailAddress": "<EMAIL>",
      "avatarUrls": {
        "48x48": "https://www.gravatar.com/avatar/9e331dcca191645ab52bbed1c8742ef3?d=mm&s=48",
        "24x24": "https://www.gravatar.com/avatar/9e331dcca191645ab52bbed1c8742ef3?d=mm&s=24",
        "16x16": "https://www.gravatar.com/avatar/9e331dcca191645ab52bbed1c8742ef3?d=mm&s=16",
        "32x32": "https://www.gravatar.com/avatar/9e331dcca191645ab52bbed1c8742ef3?d=mm&s=32"
      },
      "displayName": "Omar Bounouioua",
      "active": false,
      "timeZone": "America/New_York"
    },
    "updated": "2024-11-01T16:05:57.000-0400",
    "status": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/status/10002",
      "description": "",
      "iconUrl": "https://jira.dc2.intactlab.ca/",
      "name": "Done",
      "id": "10002",
      "statusCategory": {
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/statuscategory/3",
        "id": 3,
        "key": "done",
        "colorName": "success",
        "name": "Done"
      }
    },
    "components": [],
    "timeoriginalestimate": null,
    "customfield_13001": null,
    "description": null,
    "customfield_13003": null,
    "customfield_11022": null,
    "customfield_11100": "{summaryBean=com.atlassian.jira.plugin.devstatus.rest.SummaryBean@7a83f5a6[summary={pullrequest=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@40c1c722[overall=PullRequestOverallBean{stateCount=0, state='OPEN', details=PullRequestOverallDetails{openCount=0, mergedCount=0, declinedCount=0}},byInstanceType={}], build=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@183b7d8a[overall=com.atlassian.jira.plugin.devstatus.summary.beans.BuildOverallBean@568d4178[failedBuildCount=0,successfulBuildCount=0,unknownBuildCount=0,count=0,lastUpdated=<null>,lastUpdatedTimestamp=<null>],byInstanceType={}], review=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@7d597b65[overall=com.atlassian.jira.plugin.devstatus.summary.beans.ReviewsOverallBean@49a7fb8[stateCount=0,state=<null>,dueDate=<null>,overDue=false,count=0,lastUpdated=<null>,lastUpdatedTimestamp=<null>],byInstanceType={}], deployment-environment=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@470e858c[overall=com.atlassian.jira.plugin.devstatus.summary.beans.DeploymentOverallBean@7b348a94[topEnvironments=[],showProjects=false,successfulCount=0,count=0,lastUpdated=<null>,lastUpdatedTimestamp=<null>],byInstanceType={}], repository=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@4d8044c4[overall=com.atlassian.jira.plugin.devstatus.summary.beans.CommitOverallBean@1a308573[count=0,lastUpdated=<null>,lastUpdatedTimestamp=<null>],byInstanceType={}], branch=com.atlassian.jira.plugin.devstatus.rest.SummaryItemBean@2921415c[overall=com.atlassian.jira.plugin.devstatus.summary.beans.BranchOverallBean@761e0ec6[count=0,lastUpdated=<null>,lastUpdatedTimestamp=<null>],byInstanceType={}]},errors=[],configErrors=[]], devSummaryJson={\"cachedValue\":{\"errors\":[],\"configErrors\":[],\"summary\":{\"pullrequest\":{\"overall\":{\"count\":0,\"lastUpdated\":null,\"stateCount\":0,\"state\":\"OPEN\",\"details\":{\"openCount\":0,\"mergedCount\":0,\"declinedCount\":0,\"total\":0},\"open\":true},\"byInstanceType\":{}},\"build\":{\"overall\":{\"count\":0,\"lastUpdated\":null,\"failedBuildCount\":0,\"successfulBuildCount\":0,\"unknownBuildCount\":0},\"byInstanceType\":{}},\"review\":{\"overall\":{\"count\":0,\"lastUpdated\":null,\"stateCount\":0,\"state\":null,\"dueDate\":null,\"overDue\":false,\"completed\":false},\"byInstanceType\":{}},\"deployment-environment\":{\"overall\":{\"count\":0,\"lastUpdated\":null,\"topEnvironments\":[],\"showProjects\":false,\"successfulCount\":0},\"byInstanceType\":{}},\"repository\":{\"overall\":{\"count\":0,\"lastUpdated\":null},\"byInstanceType\":{}},\"branch\":{\"overall\":{\"count\":0,\"lastUpdated\":null},\"byInstanceType\":{}}}},\"isStale\":true}}",
    "customfield_13002": null,
    "customfield_11023": null,
    "customfield_13005": null,
    "customfield_11300": null,
    "customfield_13004": null,
    "customfield_13007": null,
    "customfield_11301": null,
    "customfield_13006": null,
    "customfield_10800": null,
    "customfield_10801": null,
    "aggregatetimeestimate": null,
    "customfield_10802": null,
    "summary": "Goalkeeping PI.2024.4.1",
    "creator": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/user?username=rpxnr993",
      "name": "rpxnr993",
      "key": "eric.portelance",
      "emailAddress": "<EMAIL>",
      "avatarUrls": {
        "48x48": "https://jira.dc2.intactlab.ca/secure/useravatar?ownerId=eric.portelance&avatarId=12200",
        "24x24": "https://jira.dc2.intactlab.ca/secure/useravatar?size=small&ownerId=eric.portelance&avatarId=12200",
        "16x16": "https://jira.dc2.intactlab.ca/secure/useravatar?size=xsmall&ownerId=eric.portelance&avatarId=12200",
        "32x32": "https://jira.dc2.intactlab.ca/secure/useravatar?size=medium&ownerId=eric.portelance&avatarId=12200"
      },
      "displayName": "Eric Portelance",
      "active": true,
      "timeZone": "America/New_York"
    },
    "subtasks": [
      {
        "id": "239413",
        "key": "MARS-2940",
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/issue/239413",
        "fields": {
          "summary": "Twistlock - bloom-api:3.0.64, Fix By: 2024-11-26",
          "status": {
            "self": "https://jira.dc2.intactlab.ca/rest/api/2/status/10002",
            "description": "",
            "iconUrl": "https://jira.dc2.intactlab.ca/",
            "name": "Done",
            "id": "10002",
            "statusCategory": {
              "self": "https://jira.dc2.intactlab.ca/rest/api/2/statuscategory/3",
              "id": 3,
              "key": "done",
              "colorName": "success",
              "name": "Done"
            }
          },
          "priority": {
            "self": "https://jira.dc2.intactlab.ca/rest/api/2/priority/3",
            "iconUrl": "https://jira.dc2.intactlab.ca/images/icons/priorities/medium.svg",
            "name": "Medium",
            "id": "3"
          },
          "issuetype": {
            "self": "https://jira.dc2.intactlab.ca/rest/api/2/issuetype/10002",
            "id": "10002",
            "description": "The sub-task of the issue",
            "iconUrl": "https://jira.dc2.intactlab.ca/secure/viewavatar?size=xsmall&avatarId=10316&avatarType=issuetype",
            "name": "Sub-task",
            "subtask": true,
            "avatarId": 10316
          }
        }
      },
      {
        "id": "239415",
        "key": "MARS-2941",
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/issue/239415",
        "fields": {
          "summary": "Twistlock - bloom-contact-cl-processor:3.0.27, Fix By: 2024-11-26",
          "status": {
            "self": "https://jira.dc2.intactlab.ca/rest/api/2/status/10002",
            "description": "",
            "iconUrl": "https://jira.dc2.intactlab.ca/",
            "name": "Done",
            "id": "10002",
            "statusCategory": {
              "self": "https://jira.dc2.intactlab.ca/rest/api/2/statuscategory/3",
              "id": 3,
              "key": "done",
              "colorName": "success",
              "name": "Done"
            }
          },
          "priority": {
            "self": "https://jira.dc2.intactlab.ca/rest/api/2/priority/3",
            "iconUrl": "https://jira.dc2.intactlab.ca/images/icons/priorities/medium.svg",
            "name": "Medium",
            "id": "3"
          },
          "issuetype": {
            "self": "https://jira.dc2.intactlab.ca/rest/api/2/issuetype/10002",
            "id": "10002",
            "description": "The sub-task of the issue",
            "iconUrl": "https://jira.dc2.intactlab.ca/secure/viewavatar?size=xsmall&avatarId=10316&avatarType=issuetype",
            "name": "Sub-task",
            "subtask": true,
            "avatarId": 10316
          }
        }
      },
      {
        "id": "239418",
        "key": "MARS-2942",
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/issue/239418",
        "fields": {
          "summary": "Twistlock - bloom-mq-handler:1.3.26, Fix By: 2024-11-27",
          "status": {
            "self": "https://jira.dc2.intactlab.ca/rest/api/2/status/10002",
            "description": "",
            "iconUrl": "https://jira.dc2.intactlab.ca/",
            "name": "Done",
            "id": "10002",
            "statusCategory": {
              "self": "https://jira.dc2.intactlab.ca/rest/api/2/statuscategory/3",
              "id": 3,
              "key": "done",
              "colorName": "success",
              "name": "Done"
            }
          },
          "priority": {
            "self": "https://jira.dc2.intactlab.ca/rest/api/2/priority/3",
            "iconUrl": "https://jira.dc2.intactlab.ca/images/icons/priorities/medium.svg",
            "name": "Medium",
            "id": "3"
          },
          "issuetype": {
            "self": "https://jira.dc2.intactlab.ca/rest/api/2/issuetype/10002",
            "id": "10002",
            "description": "The sub-task of the issue",
            "iconUrl": "https://jira.dc2.intactlab.ca/secure/viewavatar?size=xsmall&avatarId=10316&avatarType=issuetype",
            "name": "Sub-task",
            "subtask": true,
            "avatarId": 10316
          }
        }
      },
      {
        "id": "239419",
        "key": "MARS-2943",
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/issue/239419",
        "fields": {
          "summary": "Twistlock - bloom-plp-processor:4.0.3, Fix By: 2024-11-27",
          "status": {
            "self": "https://jira.dc2.intactlab.ca/rest/api/2/status/10002",
            "description": "",
            "iconUrl": "https://jira.dc2.intactlab.ca/",
            "name": "Done",
            "id": "10002",
            "statusCategory": {
              "self": "https://jira.dc2.intactlab.ca/rest/api/2/statuscategory/3",
              "id": 3,
              "key": "done",
              "colorName": "success",
              "name": "Done"
            }
          },
          "priority": {
            "self": "https://jira.dc2.intactlab.ca/rest/api/2/priority/3",
            "iconUrl": "https://jira.dc2.intactlab.ca/images/icons/priorities/medium.svg",
            "name": "Medium",
            "id": "3"
          },
          "issuetype": {
            "self": "https://jira.dc2.intactlab.ca/rest/api/2/issuetype/10002",
            "id": "10002",
            "description": "The sub-task of the issue",
            "iconUrl": "https://jira.dc2.intactlab.ca/secure/viewavatar?size=xsmall&avatarId=10316&avatarType=issuetype",
            "name": "Sub-task",
            "subtask": true,
            "avatarId": 10316
          }
        }
      },
      {
        "id": "239420",
        "key": "MARS-2944",
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/issue/239420",
        "fields": {
          "summary": "Twistlock - bloom-repository-service:3.0.35, Fix By: 2024-11-27",
          "status": {
            "self": "https://jira.dc2.intactlab.ca/rest/api/2/status/10002",
            "description": "",
            "iconUrl": "https://jira.dc2.intactlab.ca/",
            "name": "Done",
            "id": "10002",
            "statusCategory": {
              "self": "https://jira.dc2.intactlab.ca/rest/api/2/statuscategory/3",
              "id": 3,
              "key": "done",
              "colorName": "success",
              "name": "Done"
            }
          },
          "priority": {
            "self": "https://jira.dc2.intactlab.ca/rest/api/2/priority/3",
            "iconUrl": "https://jira.dc2.intactlab.ca/images/icons/priorities/medium.svg",
            "name": "Medium",
            "id": "3"
          },
          "issuetype": {
            "self": "https://jira.dc2.intactlab.ca/rest/api/2/issuetype/10002",
            "id": "10002",
            "description": "The sub-task of the issue",
            "iconUrl": "https://jira.dc2.intactlab.ca/secure/viewavatar?size=xsmall&avatarId=10316&avatarType=issuetype",
            "name": "Sub-task",
            "subtask": true,
            "avatarId": 10316
          }
        }
      },
      {
        "id": "239422",
        "key": "MARS-2945",
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/issue/239422",
        "fields": {
          "summary": "Twistlock - bloom-xpas-processor:3.0.48, Fix By: 2024-11-27",
          "status": {
            "self": "https://jira.dc2.intactlab.ca/rest/api/2/status/10002",
            "description": "",
            "iconUrl": "https://jira.dc2.intactlab.ca/",
            "name": "Done",
            "id": "10002",
            "statusCategory": {
              "self": "https://jira.dc2.intactlab.ca/rest/api/2/statuscategory/3",
              "id": 3,
              "key": "done",
              "colorName": "success",
              "name": "Done"
            }
          },
          "priority": {
            "self": "https://jira.dc2.intactlab.ca/rest/api/2/priority/3",
            "iconUrl": "https://jira.dc2.intactlab.ca/images/icons/priorities/medium.svg",
            "name": "Medium",
            "id": "3"
          },
          "issuetype": {
            "self": "https://jira.dc2.intactlab.ca/rest/api/2/issuetype/10002",
            "id": "10002",
            "description": "The sub-task of the issue",
            "iconUrl": "https://jira.dc2.intactlab.ca/secure/viewavatar?size=xsmall&avatarId=10316&avatarType=issuetype",
            "name": "Sub-task",
            "subtask": true,
            "avatarId": 10316
          }
        }
      },
      {
        "id": "239424",
        "key": "MARS-2946",
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/issue/239424",
        "fields": {
          "summary": "Twistlock - broker-dialer-service:3.1.55, Fix By: 2024-11-26",
          "status": {
            "self": "https://jira.dc2.intactlab.ca/rest/api/2/status/10002",
            "description": "",
            "iconUrl": "https://jira.dc2.intactlab.ca/",
            "name": "Done",
            "id": "10002",
            "statusCategory": {
              "self": "https://jira.dc2.intactlab.ca/rest/api/2/statuscategory/3",
              "id": 3,
              "key": "done",
              "colorName": "success",
              "name": "Done"
            }
          },
          "priority": {
            "self": "https://jira.dc2.intactlab.ca/rest/api/2/priority/3",
            "iconUrl": "https://jira.dc2.intactlab.ca/images/icons/priorities/medium.svg",
            "name": "Medium",
            "id": "3"
          },
          "issuetype": {
            "self": "https://jira.dc2.intactlab.ca/rest/api/2/issuetype/10002",
            "id": "10002",
            "description": "The sub-task of the issue",
            "iconUrl": "https://jira.dc2.intactlab.ca/secure/viewavatar?size=xsmall&avatarId=10316&avatarType=issuetype",
            "name": "Sub-task",
            "subtask": true,
            "avatarId": 10316
          }
        }
      },
      {
        "id": "239426",
        "key": "MARS-2947",
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/issue/239426",
        "fields": {
          "summary": "Twistlock - broker-admin-app:1.0.7, Fix By: 2024-11-27",
          "status": {
            "self": "https://jira.dc2.intactlab.ca/rest/api/2/status/10002",
            "description": "",
            "iconUrl": "https://jira.dc2.intactlab.ca/",
            "name": "Done",
            "id": "10002",
            "statusCategory": {
              "self": "https://jira.dc2.intactlab.ca/rest/api/2/statuscategory/3",
              "id": 3,
              "key": "done",
              "colorName": "success",
              "name": "Done"
            }
          },
          "priority": {
            "self": "https://jira.dc2.intactlab.ca/rest/api/2/priority/3",
            "iconUrl": "https://jira.dc2.intactlab.ca/images/icons/priorities/medium.svg",
            "name": "Medium",
            "id": "3"
          },
          "issuetype": {
            "self": "https://jira.dc2.intactlab.ca/rest/api/2/issuetype/10002",
            "id": "10002",
            "description": "The sub-task of the issue",
            "iconUrl": "https://jira.dc2.intactlab.ca/secure/viewavatar?size=xsmall&avatarId=10316&avatarType=issuetype",
            "name": "Sub-task",
            "subtask": true,
            "avatarId": 10316
          }
        }
      },
      {
        "id": "239968",
        "key": "MARS-3049",
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/issue/239968",
        "fields": {
          "summary": "Nexus IQ - Bloom UI",
          "status": {
            "self": "https://jira.dc2.intactlab.ca/rest/api/2/status/10002",
            "description": "",
            "iconUrl": "https://jira.dc2.intactlab.ca/",
            "name": "Done",
            "id": "10002",
            "statusCategory": {
              "self": "https://jira.dc2.intactlab.ca/rest/api/2/statuscategory/3",
              "id": 3,
              "key": "done",
              "colorName": "success",
              "name": "Done"
            }
          },
          "priority": {
            "self": "https://jira.dc2.intactlab.ca/rest/api/2/priority/4",
            "iconUrl": "https://jira.dc2.intactlab.ca/images/icons/priorities/low.svg",
            "name": "Low",
            "id": "4"
          },
          "issuetype": {
            "self": "https://jira.dc2.intactlab.ca/rest/api/2/issuetype/10002",
            "id": "10002",
            "description": "The sub-task of the issue",
            "iconUrl": "https://jira.dc2.intactlab.ca/secure/viewavatar?size=xsmall&avatarId=10316&avatarType=issuetype",
            "name": "Sub-task",
            "subtask": true,
            "avatarId": 10316
          }
        }
      },
      {
        "id": "241051",
        "key": "MARS-3083",
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/issue/241051",
        "fields": {
          "summary": "Twistlocks - quickquote-intact-web:4.0.3",
          "status": {
            "self": "https://jira.dc2.intactlab.ca/rest/api/2/status/10002",
            "description": "",
            "iconUrl": "https://jira.dc2.intactlab.ca/",
            "name": "Done",
            "id": "10002",
            "statusCategory": {
              "self": "https://jira.dc2.intactlab.ca/rest/api/2/statuscategory/3",
              "id": 3,
              "key": "done",
              "colorName": "success",
              "name": "Done"
            }
          },
          "priority": {
            "self": "https://jira.dc2.intactlab.ca/rest/api/2/priority/3",
            "iconUrl": "https://jira.dc2.intactlab.ca/images/icons/priorities/medium.svg",
            "name": "Medium",
            "id": "3"
          },
          "issuetype": {
            "self": "https://jira.dc2.intactlab.ca/rest/api/2/issuetype/10002",
            "id": "10002",
            "description": "The sub-task of the issue",
            "iconUrl": "https://jira.dc2.intactlab.ca/secure/viewavatar?size=xsmall&avatarId=10316&avatarType=issuetype",
            "name": "Sub-task",
            "subtask": true,
            "avatarId": 10316
          }
        }
      },
      {
        "id": "241062",
        "key": "MARS-3085",
        "self": "https://jira.dc2.intactlab.ca/rest/api/2/issue/241062",
        "fields": {
          "summary": "Twistlocks - autoquote-intact-webservice-web:3.8.82",
          "status": {
            "self": "https://jira.dc2.intactlab.ca/rest/api/2/status/10002",
            "description": "",
            "iconUrl": "https://jira.dc2.intactlab.ca/",
            "name": "Done",
            "id": "10002",
            "statusCategory": {
              "self": "https://jira.dc2.intactlab.ca/rest/api/2/statuscategory/3",
              "id": 3,
              "key": "done",
              "colorName": "success",
              "name": "Done"
            }
          },
          "priority": {
            "self": "https://jira.dc2.intactlab.ca/rest/api/2/priority/3",
            "iconUrl": "https://jira.dc2.intactlab.ca/images/icons/priorities/medium.svg",
            "name": "Medium",
            "id": "3"
          },
          "issuetype": {
            "self": "https://jira.dc2.intactlab.ca/rest/api/2/issuetype/10002",
            "id": "10002",
            "description": "The sub-task of the issue",
            "iconUrl": "https://jira.dc2.intactlab.ca/secure/viewavatar?size=xsmall&avatarId=10316&avatarType=issuetype",
            "name": "Sub-task",
            "subtask": true,
            "avatarId": 10316
          }
        }
      }
    ],
    "reporter": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/user?username=rpdwr793",
      "name": "rpdwr793",
      "key": "JIRAUSER25396",
      "emailAddress": "<EMAIL>",
      "avatarUrls": {
        "48x48": "https://www.gravatar.com/avatar/adefc88bd6f9e99d2f07e847327b5b7e?d=mm&s=48",
        "24x24": "https://www.gravatar.com/avatar/adefc88bd6f9e99d2f07e847327b5b7e?d=mm&s=24",
        "16x16": "https://www.gravatar.com/avatar/adefc88bd6f9e99d2f07e847327b5b7e?d=mm&s=16",
        "32x32": "https://www.gravatar.com/avatar/adefc88bd6f9e99d2f07e847327b5b7e?d=mm&s=32"
      },
      "displayName": "Alexander-2 Graham",
      "active": true,
      "timeZone": "America/New_York"
    },
    "customfield_12100": null,
    "aggregateprogress": {
      "progress": 0,
      "total": 0
    },
    "customfield_12301": null,
    "customfield_10200": null,
    "customfield_12302": null,
    "customfield_10400": null,
    "customfield_12500": null,
    "customfield_13900": null,
    "customfield_11204": null,
    "customfield_11006": null,
    "customfield_11205": null,
    "environment": null,
    "customfield_11800": null,
    "customfield_13903": null,
    "duedate": null,
    "progress": {
      "progress": 0,
      "total": 0
    },
    "votes": {
      "self": "https://jira.dc2.intactlab.ca/rest/api/2/issue/MARS-2964/votes",
      "votes": 0,
      "hasVoted": false
    }
  }
}
