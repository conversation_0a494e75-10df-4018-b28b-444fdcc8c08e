<Context path="/BrokerAdm">
	<!-- Define datasources (should match JNDI name for lookup) -->
	<Resource
			name="jdbc/plpolicy"
			auth="Container"
			type="javax.sql.DataSource"
			maxTotal="100"
			maxIdle="30"
			maxWaitMillis="10000"
			username="PLP_WZONE_INT"
			password="${DB_PLP_PASSWORD}"
			driverClassName="oracle.jdbc.OracleDriver"
			url="${DB_PLP_URL}"/>

	<Resource
			name="jdbc/brm"
			auth="Container"
			type="javax.sql.DataSource"
			maxTotal="100"
			maxIdle="30"
			maxWaitMillis="10000"
			username="brmadmin"
			password="${DB_BRM_PASSWORD}"
			driverClassName="oracle.jdbc.OracleDriver"
			url="${DB_BRM_URL}"/>

	<Resource
			name="jdbc/CIF"
			auth="Container"
			type="javax.sql.DataSource"
			maxTotal="100"
			maxIdle="30"
			maxWaitMillis="10000"
			username="CIF_WZONE_INT"
			password="${DB_CIF_PASSWORD}"
			driverClassName="oracle.jdbc.OracleDriver"
			url="${DB_CIF_URL}"/>

	<Resource
			name="jdbc/SAD"
			auth="Container"
			type="javax.sql.DataSource"
			maxTotal="100"
			maxIdle="30"
			maxWaitMillis="10000"
			username="sadusrrw"
			password="${DB_SAD_PASSWORD}"
			driverClassName="oracle.jdbc.OracleDriver"
			url="${DB_SAD_URL}"/>

	<Resource
			name="jdbc/dw"
			auth="Container"
			type="javax.sql.DataSource"
			maxTotal="100"
			maxIdle="30"
			maxWaitMillis="10000"
			username="dw_report_intact1"
			password="${DB_DW_PASSWORD}"
			driverClassName="oracle.jdbc.OracleDriver"
			url="${DB_DW_URL}"/>
</Context>
