#!/bin/bash
echo "*** setenv.sh begin ***"
umask 0000

cp /mnt/extra-classpath/* $CATALINA_BASE/extra-classpath/

. "${CATALINA_BIN}/vault_replace.sh" "/vault/secrets/secrets.properties" "$CATALINA_BASE/extra-classpath"
. "${CATALINA_BIN}/vault_replace.sh" "/vault/secrets/secrets.properties" "$CATALINA_BASE/conf"

export ENV_LEVEL=${ENVIRONMENT}
export LOG_ROOT=${LOG_BASE_DIR}/${ENVIRONMENT}-${APPLICATIONNAME}
export WAS_SERVER_NAME=tomcat
export JAVA_OPTS="${ADDITIONAL_JAVA_OPTS} -DjndiPrefix=java:/comp/env/ -DLOG_ROOT=${LOG_ROOT} -DWAS_SERVER_NAME=${WAS_SERVER_NAME}"
export CATALINA_OPTS="-Duser.timezone=America/Montreal"
export POD_START=$(date)

# add the pod start time in container-info
echo "<div>POD_NAME=$HOSTNAME</div>" >> ${CATALINA_WEBAPPS}/container-info/index.html
echo "<div>POD_START=$POD_START</div>" >> ${CATALINA_WEBAPPS}/container-info/index.html
